/// 领料单-单据表
model MaterialRequisitionForm {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  settlementStatus MaterialSettlementStatus @default(UN_SETTLED) @map("settlement_status") /// 结算状态
  code             String                   @map("code") /// 单据编码
  supplierId       String?                  @map("supplier_id") /// 领料单位ID/供应商名录ID
  partName         String?                  @map("part_name") /// 使用部位名称
  year             Int                      @map("year") /// 年
  month            Int                      @map("month") /// 月
  day              Int                      @map("day") /// 日
  creator          String                   @map("creator") /// 创建人名称
  submitStatus     SubmitStatus             @default(PENDING) @map("submit_status") /// 提交状态
  auditStatus      AuditStatus              @default(PENDING) @map("audit_status") /// 审批状态

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_requisition_form")
}

/// 领料单-明细表
model MaterialRequisitionFormDetail {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  parentId                     String?  @map("parent_id") /// 父ID
  requisitionFormId            String   @map("requisition_form_id") /// 领料单ID
  materialId                   String   @map("material_id") /// 材料ID
  materialName                 String?  @map("material_name") /// 材料名称
  materialSpec                 String?  @map("material_spec") /// 材料规格
  materialReceivingInventoryId String?  @map("material_receiving_inventory_id") /// 收料单库存ID
  businessCostSubjectDetailId  String?  @map("business_cost_subject_detail_id") /// 成本科目明细ID
  unit                         String   @map("unit") /// 计量单位
  inventoryQuantity            Decimal? @map("inventory_quantity") @db.Decimal(20, 8) /// 库存数量
  actualQuantity               Decimal? @map("actual_quantity") @db.Decimal(20, 8) /// 实发数量
  price                        Decimal? @map("price") @db.Decimal(20, 6) /// 领料单价
  amount                       Decimal? @map("amount") @db.Decimal(20, 6) /// 领料金额
  orderNo                      BigInt   @default(autoincrement()) @map("order_no") /// 排序号

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_requisition_form_detail")
}

/// 领料单-附件表
model MaterialRequisitionFormAttachment {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  requisitionFormId String @map("requisition_form_id") /// 领料单ID
  fileName          String @map("file_name") /// 文件名称
  fileKey           String @map("file_key") /// 文件key
  fileSize          Int    @map("file_size") /// 文件大小
  fileExt           String @map("file_ext") /// 文件后缀
  fileContentType   String @map("file_content_type") /// 文件contentType

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_requisition_form_attachment")
}
