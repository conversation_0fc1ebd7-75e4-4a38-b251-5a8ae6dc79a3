// 供应商分类
enum SupplierDirectoryClassify {
  SERVICE_OUTSOURCING // 服务外包
  MECHANICAL_LEASING // 机械租赁
  LABOR_SUBCONTRACTING // 劳务分包
  MATERIAL_PURCHASING // 物资采购
  PROFESSIONAL_SUBCONTRACTING // 专业分包
}

// 纳税人资质类型
enum TaxpayerQualification {
  GENERAL // 一般纳税人
  SMALL_SCALE // 小规模纳税人
}

enum Grade {
  QUALIFIED // 合格
  UNQUALIFIED // 不合格
  BLACKLIST // 黑名单
}

enum ChangeType {
  ADD // 新增
  DELETE // 删除
}

// 供应商名录表
model SupplierDirectory {
  // 主键 & 外键
  id       String @id @default(uuid(7))
  orgId    String @map("org_id") // 组织id
  tenantId String @map("tenant_id") // 租户id

  // 业务字段
  // 基础信息
  fullName              String                      @map("full_name") // 供应商全称
  simpleName            String                      @map("simple_name") // 供应商简称
  creditCode            String                      @map("credit_code") // 统一社会信用代码
  regionCode            String?                     @map("region_code") // 省市区code
  registeredProvince    String?                     @map("registered_province") // 注册所在省
  registeredCity        String?                     @map("register_city") // 注册所在市
  registeredCounty      String?                     @map("registered_county") // 注册所在区县
  registeredAddress     String?                     @map("registered_address") // 注册地址
  mainBusiness          String?                     @map("main_business") // 主营业务
  classify              SupplierDirectoryClassify[] @map("classify") // 供应商分类
  jobContent            String?                     @map("job_content") // 供应工作内容
  region                String?                     @map("region") // 供应区域
  unitType              String?                     @map("unit_type") // 单位类型
  taxpayerQualification TaxpayerQualification       @map("taxpayer_qualification") // 纳税人资质
  registeredCapital     Decimal?                    @map("registered_capital") @db.Decimal(10, 2) // 注册资金（万元）
  establishAt           DateTime?                   @map("establish_at") // 成立时间
  contactBy             String?                     @map("contact_by") // 联系人
  contactPhone          String?                     @map("contact_phone") // 联系人电话
  contactEmail          String?                     @map("contact_email") // 联系人邮箱
  LegalBy               String?                     @map("legal_by") // 法定代表人/单位负责人
  relationEnterprise    String?                     @map("relation_enterprise") // 关联企业
  introductionAt        DateTime?                   @map("introduction_at") // 引进时间
  remark                String?                     @map("remark") // 备注
  isPublished           Boolean                     @default(false) @map("is_published") // 是否发布
  publishAt             DateTime?                   @map("publish_at") // 发布时间
  creator               String                      @map("creator") // 创建人
  isDefault             Boolean                     @default(false) @map("is_default") /// 是否默认

  // 供应商评级
  // grade      Grade     @map("grade") // 供应商评级
  // evaluateAt DateTime? @map("evaluate_at") // 评价时间
  // exitAt     DateTime? @map("exit_at") // 退场时间
  // weedOutAt  DateTime? @map("weed_out_at") // 淘汰时间

  supplierDirectoryChangeRecord SupplierDirectoryChangeRecord[] // 供应商变更记录表
  supplierDirectoryAccessory    SupplierDirectoryAccessory[] // 供应商附件表

  // 公共字段
  createAt  DateTime @default(now()) @map("create_at")
  createBy  String   @map("create_by")
  updateAt  DateTime @updatedAt() @map("update_at")
  updateBy  String   @map("update_by")
  isDeleted Boolean  @default(false) @map("is_deleted")

  @@unique([tenantId, orgId, id])
  @@map("supplier_directory")
}

// 供应商名录发布表
model PublishSupplierDirectory {
  // 主键 & 外键
  id       String @id @default(uuid(7))
  orgId    String @map("org_id") // 组织id
  tenantId String @map("tenant_id") // 租户id

  // 业务字段
  // 基础信息
  fullName              String                      @map("full_name") // 供应商全称
  simpleName            String                      @map("simple_name") // 供应商简称
  creditCode            String                      @map("credit_code") // 统一社会信用代码
  regionCode            String?                     @map("region_code") // 省市区code
  registeredProvince    String?                     @map("registered_province") // 注册所在省
  registeredCity        String?                     @map("register_city") // 注册所在市
  registeredCounty      String?                     @map("registered_county") // 注册所在区县
  registeredAddress     String?                     @map("registered_address") // 注册地址
  mainBusiness          String?                     @map("main_business") // 主营业务
  classify              SupplierDirectoryClassify[] @map("classify") // 供应商分类
  jobContent            String?                     @map("job_content") // 供应工作内容
  region                String?                     @map("region") // 供应区域
  unitType              String?                     @map("unit_type") // 单位类型
  taxpayerQualification TaxpayerQualification       @map("taxpayer_qualification") // 纳税人资质
  registeredCapital     Decimal?                    @map("registered_capital") @db.Decimal(10, 2) // 注册资金（万元）
  establishAt           DateTime?                   @map("establish_at") // 成立时间
  contactBy             String?                     @map("contact_by") // 联系人
  contactPhone          String?                     @map("contact_phone") // 联系人电话
  contactEmail          String?                     @map("contact_email") // 联系人邮箱
  LegalBy               String?                     @map("legal_by") // 法定代表人/单位负责人
  relationEnterprise    String?                     @map("relation_enterprise") // 关联企业
  introductionAt        DateTime?                   @map("introduction_at") // 引进时间
  remark                String?                     @map("remark") // 备注

  // 供应商评级
  // grade      Grade     @map("grade") // 供应商评级
  // evaluateAt DateTime? @map("evaluate_at") // 评价时间
  // exitAt     DateTime? @map("exit_at") // 退场时间
  // weedOutAt  DateTime? @map("weed_out_at") // 淘汰时间

  // supplierDirectoryChangeRecord SupplierDirectoryChangeRecord[] // 供应商变更记录表
  supplierPubDirectoryAccessory SupplierPublishDirectoryAccessory[] // 供应商附件表

  // 公共字段
  createAt  DateTime @default(now()) @map("create_at")
  createBy  String   @map("create_by")
  updateAt  DateTime @updatedAt() @map("update_at")
  updateBy  String   @map("update_by")
  isDeleted Boolean  @default(false) @map("is_deleted")

  @@unique([tenantId, orgId, id])
  @@map("publish_supplier_directory")
}

// 供应商变更记录表
model SupplierDirectoryChangeRecord {
  // 主键 & 外键
  id                  String @id @default(uuid(7)) @map("id")
  orgId               String @map("org_id") // 组织id
  tenantId            String @map("tenant_id") // 租户id
  supplierDirectoryId String @map("supplier_directory_id") // 标准供应商目录ID

  // 业务字段
  changeBy  String @map("change_by") // 变更人
  changeNum Int    @map("change_num") // 变更数量

  // 公共字段
  createAt  DateTime @default(now()) @map("create_at")
  createBy  String   @map("create_by")
  updateAt  DateTime @updatedAt() @map("update_at")
  updateBy  String   @map("update_by")
  isDeleted Boolean  @default(false) @map("is_deleted")

  supplierDirectory SupplierDirectory                      @relation(fields: [supplierDirectoryId], references: [id]) // // 关联企业标准供应商目录表
  details           SupplierDirectoryChangeRecordDetails[]

  @@unique([id, tenantId, orgId])
  @@map("supplier_directory_change_record")
}

// 供应商档案附件表
model SupplierDirectoryAccessory {
  // 主键 & 外键
  id                  String @id @default(uuid(7))
  orgId               String @map("org_id") // 组织id
  tenantId            String @map("tenant_id") // 租户id
  supplierDirectoryId String @map("supplier_directory_id") // // 关联供应商目录表

  // 业务字段
  fileName        String  @map("file_name") /// 文件名称
  fileExt         String  @map("file_ext") /// 文件扩展名
  fileKey         String  @map("file_key") /// 文件key
  fileSize        String  @map("file_size") /// 文件大小 字节
  fileContentType String  @map("file_content_type") /// 文件类型
  remark          String? @map("remark") // 备注

  // 公共字段
  createAt  DateTime @default(now()) @map("create_at")
  createBy  String   @map("create_by")
  updateAt  DateTime @updatedAt() @map("update_at")
  updateBy  String   @map("update_by")
  isDeleted Boolean  @default(false) @map("is_deleted")

  // 关联关系
  supplierDirectory SupplierDirectory @relation(fields: [supplierDirectoryId], references: [id]) // // 关联企业标准供应商目录表

  @@unique([id, tenantId, orgId])
  @@map("supplier_directory_accessory")
}

// 供应商发布档案附件表
model SupplierPublishDirectoryAccessory {
  // 主键 & 外键
  id                     String @id @default(uuid(7))
  orgId                  String @map("org_id") // 组织id
  tenantId               String @map("tenant_id") // 租户id
  supplierPubDirectoryId String @map("supplier_directory_id") // // 关联供应商目录表

  // 业务字段
  fileName        String  @map("file_name") /// 文件名称
  fileExt         String  @map("file_ext") /// 文件扩展名
  fileKey         String  @map("file_key") /// 文件key
  fileSize        String  @map("file_size") /// 文件大小
  fileContentType String  @map("file_content_type") /// 文件类型
  remark          String? @map("remark") // 备注

  // 公共字段
  createAt  DateTime @default(now()) @map("create_at")
  createBy  String   @map("create_by")
  updateAt  DateTime @updatedAt() @map("update_at")
  updateBy  String   @map("update_by")
  isDeleted Boolean  @default(false) @map("is_deleted")

  // 关联关系
  supplierPubDirectory PublishSupplierDirectory @relation(fields: [supplierPubDirectoryId], references: [id]) // // 关联企业标准供应商目录表

  @@unique([id, tenantId, orgId])
  @@map("supplier_publish_directory_accessory")
}

// 明细字段类型
enum DetailsFieldType {
  GENERAL // 普通类型
  FILE // 文件类型
}

// 供应商变更记录明细表
model SupplierDirectoryChangeRecordDetails {
  // 主键 & 外键
  id       String @id @default(uuid(7)) @map("id")
  orgId    String @map("org_id") // 组织id
  tenantId String @map("tenant_id") // 租户id

  // 业务字段
  fieldName  String           @map("field_name") // 变更单的字段名称
  oldValue   String?          @map("old_value") // 旧值
  newValue   String           @map("new_value") // 新值
  changeType ChangeType?      @map("change_type") // 变更类型
  fieldType  DetailsFieldType @map("field_type") // 字段类型

  // 关联供应商变更记录表
  supplierDirectoryId             String                        @map("supplier_directory_id") // 标准供应商目录ID
  supplierDirectoryChangeRecordId String                        @map("supplier_directory_change_record_id") // 供应商变更记录ID
  SupplierDirectoryChangeRecord   SupplierDirectoryChangeRecord @relation(fields: [supplierDirectoryChangeRecordId], references: [id])

  // 公共字段
  createAt  DateTime @default(now()) @map("create_at")
  createBy  String   @map("create_by")
  updateAt  DateTime @updatedAt() @map("update_at")
  updateBy  String   @map("update_by")
  isDeleted Boolean  @default(false) @map("is_deleted")

  @@unique([id, tenantId, orgId])
  @@map("supplier_directory_change_record_details")
}
