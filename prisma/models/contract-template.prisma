// 合同范本表版本状态
enum VersionStatus {
  PUBLISHED //  发布
  UNPUBLISHED // 未发布
  NOUSEING // 停用
}

// 合同范本类型
enum ContractTemplateClassifyType {
  GENERAL // 通用
  SUBPACKAGE_LABOUR_SERVICE // 分包-劳务
  SUBPACKAGE_LABOUR_SPECIALTY // 分包-专业
  MATERIALS_PURCHASING // 材料-物资采购
  MATERIALS_COMMERCIAL_CONCRETE // 材料-商品混凝土
  MATERIALS_LEASING_TURNOVER // 材料-租赁周转材料
  MACHINERY // 机械
  OTHERS // 其他
}

/// 企业标准合同范本表
model ContractTemplate {
  // 主键 & 外键
  id       String @id @default(uuid(7))
  orgId    String @map("org_id") // 组织id
  tenantId String @map("tenant_id") // 租户id

  // 业务字段
  name            String /// 合同类型/范本名称
  classify        ContractTemplateClassifyType @map("classify") /// 合同范本类型
  sort            Int                          @default(1) // 排序
  remark          String? /// 范本说明
  compileDate     DateTime                     @default(now()) @map("compile_date") /// 编制日期
  compileBy       String                       @map("compile_by") /// 编制人
  versionStatus   VersionStatus                @default(UNPUBLISHED) @map("version_status") /// 版本状态
  referNums       Int                          @default(0) @map("refer_nums") /// 引用次数
  fileContentType String                       @map("file_content_type") /// 文件类型
  fileKey         String                       @map("file_key") /// 文件key
  fileName        String                       @map("file_name") /// 文件名
  fileSize        String                       @map("file_size") /// 文件大小
  fileExt         String                       @default("") @map("file_ext") /// 文件后缀

  // 公共字段
  createAt  DateTime @default(now()) @map("create_at")
  createBy  String   @map("create_by")
  updateAt  DateTime @updatedAt() @map("update_at")
  updateBy  String   @map("update_by")
  isDeleted Boolean  @default(false) @map("is_deleted")

  materialContract MaterialContract[] // 合同编制表
  mandatoryTerm    MandatoryTerm[]

  @@unique([tenantId, orgId, id])
  @@map("contract_template")
}

// 字段类型
enum FieldType {
  TEXT // 文本
  NUMBER // 数值
  DATE // 日期
  TABLE // 表格
  PERCENT // 百分比
  ENUM // 枚举
}

// 来源模块
enum ModuleSourceType {
  SYSTEM // 系统内置
  ORG // 组织机构
  BASIC_PROJECT_INFORMATION // 项目基本信息
  BASIC_COMPANY_INFORMATION // 公司基本信息
  PROVIDER_DIRECTORY // 供应商名录
  CODE_RULE // 编码规则
  SYSTEM_BUILT // 系统内置
  TAX_RATE_DICTIONARY // 税率字典
}

// 字段分类
enum FieldClassifyType {
  PROJECT_INFORMATION // 项目信息
  Party_A_INFORMATION // 甲方信息
  Party_B_INFORMATION // 乙方信息
  CONTRACT_INFORMATION // 合同信息
}

// 企业标准字段规则表
model FieldRule {
  // 主键 & 外键
  id       String  @id @default(uuid(7))
  tenantId String? @map("tenant_id") // 租户id，没有为公用，有则为租户自身的

  // 业务字段
  name              String /// 字段名称
  code              String /// 字段编码
  isDefaultRequired Boolean                      @map("is_default_required") /// 是否默认必填
  isUpdate          Boolean                      @map("is_update") /// 是否可修改
  sort              Int                          @default(1) /// 排序
  classify          FieldClassifyType            @map("classify") /// 字段分类
  isRequired        Boolean                      @map("is_required") /// 是否必填
  fieldType         FieldType                    @map("field_type") /// 字段类型
  enumValue         String                       @map("enum_value") /// 枚举值
  type              ContractTemplateClassifyType @default(GENERAL) @map("type") /// 所属合同范本的分类
  moduleSource      ModuleSourceType             @default(SYSTEM) @map("module_source") /// 字段所属模块来源
  isMatching        Boolean                      @default(false) @map("is_matching") /// 是否匹配

  // 公共字段
  createAt  DateTime @default(now()) @map("create_at")
  createBy  String   @default("system") @map("create_by")
  updateAt  DateTime @updatedAt() @map("update_at")
  updateBy  String   @default("system") @map("update_by")
  isDeleted Boolean  @default(false) @map("is_deleted")

  @@map("field_rule")
}

// 合同范本所属的字段规则关联关系表
model ContractTemplateFieldRule {
  // 主键 & 外键
  id       String @id @default(uuid(7))
  orgId    String @map("org_id") // 组织id
  tenantId String @map("tenant_id") // 租户id

  coord              String @map("coord") // 坐标
  fieldRuleId        String @map("field_rule_id") // 字段规则id
  contractTemplateId String @map("contract_template_id") // 合同范本id

  isRequired Boolean @map("is_required") /// 是否必填

  // 公共字段
  createAt  DateTime @default(now()) @map("create_at")
  createBy  String   @map("create_by")
  updateAt  DateTime @updatedAt() @map("update_at")
  updateBy  String   @map("update_by")
  isDeleted Boolean  @default(false) @map("is_deleted")

  @@unique([tenantId, orgId, id])
  @@map("contract_template_field_rule")
}

// 企业标准强制条款表
model MandatoryTerm {
  // 主键 & 外键
  id                 String @id @default(uuid(7))
  orgId              String @map("org_id") // 组织id
  tenantId           String @map("tenant_id") // 租户id
  contractTemplateId String @map("contract_template_id") // 合同范本id

  // 业务字段
  name    String // 强制条款名称
  content String // 强制条款内容

  // 公共字段
  createAt  DateTime @default(now()) @map("create_at")
  createBy  String   @map("create_by")
  updateAt  DateTime @updatedAt() @map("update_at")
  updateBy  String   @map("update_by")
  isDeleted Boolean  @default(false) @map("is_deleted")

  contractTemplate ContractTemplate @relation(fields: [contractTemplateId], references: [id])

  @@unique([tenantId, orgId, id])
  @@map("mandatory_term")
}
