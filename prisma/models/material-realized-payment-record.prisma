// 实付记录
model MaterialRealizedPaymentRecord {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  contractId   String       @map("contract_id") /// 合同id
  amount       Decimal?     @map("amount") @db.Decimal(20, 6) /// 本期付款金额
  year         Int          @map("year") /// 年 付款日期年
  month        Int          @map("month") /// 月 付款日期月
  day          Int          @map("day") /// 日 付款日期日
  submitStatus SubmitStatus @default(PENDING) @map("submit_status") /// 提交状态
  remark       String?      @map("remark") /// 备注

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_realized_payment_record")
}
