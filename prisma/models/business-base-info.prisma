// 公司基本信息发布状态
enum BusinessBaseInfoVersionStatus {
  DRAFT // 草稿状态
  SUBMITTED //  已保存，等待发布
  PUBLISHED // 已发布（线上版本）
}

/// 公司基本信息-明细
model BusinessBaseInfo {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  companyVersion          String                        @map("company_version") /// 公司版本
  companyName             String?                       @map("company_name") /// 公司名称
  unifiedSocialCreditCode String?                       @map("unified_socialcredit_code") /// 统一社会信用代码
  registeredAddress       String?                       @map("register_address") /// 注册地址
  companyLocation         String?                       @map("company_location") /// 企业所在地
  taxpayerType            String?                       @map("taxpayer_type") /// 纳税人身份
  businessPhone           String?                       @map("business_phone") /// 业务电话
  bankName                String?                       @map("bank_name") /// 开户银行
  bankAddress             String?                       @map("bank_address") /// 开户地址
  bankAccount             String?                       @map("bank_account") /// 开户账号
  remark                  String?                       @map("remark") /// 备注
  status                  BusinessBaseInfoVersionStatus @default(DRAFT) @map("version") // 发布状态
  sort                    Int                           @default(1) // 排序
  // 公共字段
  isDeleted               Boolean                       @default(false) @map("is_deleted") /// 是否删除
  createBy                String                        @default("system") @map("create_by") /// 创建人
  updateBy                String                        @default("system") @map("update_by") /// 更新人
  createAt                DateTime                      @default(now()) @map("create_at") /// 创建时间
  updateAt                DateTime                      @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("business_base_info")
}

/// 公司基本信息-快照
model BusinessBaseInfoPublish {
  // 主键 & 外键
  tenantId     String @map("tenant_id") /// 租户id
  orgId        String @map("org_id") /// 组织id
  id           String @id @default(uuid(7)) @map("id") /// 数据id
  originInfoId String @map("origin_info_id") /// 原始数据id

  companyName             String   @map("company_name") /// 公司名称
  unifiedSocialCreditCode String   @map("unified_socialcredit_code") /// 统一社会信用代码
  registeredAddress       String?  @map("register_address") /// 注册地址
  companyLocation         String?  @map("company_location") /// 企业所在地
  taxpayerType            String?  @map("taxpayer_type") /// 纳税人身份
  businessPhone           String?  @map("business_phone") /// 业务电话
  bankName                String?  @map("bank_name") /// 开户银行
  bankAddress             String?  @map("bank_address") /// 开户地址
  bankAccount             String?  @map("bank_account") /// 开户账号
  versionNumber           Int      @map("version_number") /// 版本
  // 公共字段
  isDeleted               Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy                String   @default("system") @map("create_by") /// 创建人
  updateBy                String   @default("system") @map("update_by") /// 更新人
  createAt                DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt                DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("business_base_info_publish")
}

/// 公司基本信息-变更记录
model BusinessBaseInfoChangeLog {
  // 主键 & 外键
  tenantId     String @map("tenant_id") /// 租户id
  orgId        String @map("org_id") /// 组织id
  id           String @id @default(uuid(7)) @map("id") /// 数据id
  originInfoId String @map("origin_info_id") /// 原始数据id

  fieldKey      String   @map("field_key") /// 变更字段名，如 companyName
  oldValue      String?  @map("old_value") /// 旧值（nullable）
  newValue      String?  @map("new_value") /// 新值（nullable）
  createByName  String?  @map("create_by_name") /// 创建人名称
  versionNumber Int?     @map("version_number") /// 版本
  // 公共字段
  isDeleted     Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy      String   @default("system") @map("create_by") /// 创建人
  updateBy      String   @default("system") @map("update_by") /// 更新人
  createAt      DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt      DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId,id])
  @@map("business_base_info_change_log")
}
