/// 材料字典 - 版本
model MaterialDictionaryVersion {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  name                         String       @map("name") /// 名称
  businessCostSubjectVersionId String       @map("business_cost_subject_version_id") /// 业务成本成本科目版本id
  status                       EnableStatus @default(NOT_ENABLED) @map("status") /// 启用状态

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  materialDictionaryCategory MaterialDictionaryCategory[] /// 分类表
  materialDictionaryDetail   MaterialDictionaryDetail[] /// 明细表

  @@unique([tenantId, orgId, id])
  @@map("material_dictionary_version")
}

/// 材料字典 - 分类
model MaterialDictionaryCategory {
  // 主键 & 外键
  tenantId                    String @map("tenant_id") /// 租户id
  orgId                       String @map("org_id") /// 组织id
  id                          String @id @default(uuid(7)) @map("id") /// 数据id
  materialDictionaryVersionId String @map("material_dictionary_version_id") /// 材料字典版本id

  // 业务字段
  code     String       @map("code") /// 编码
  name     String       @map("name") /// 名称
  type     MaterialType @default(CONSUME_MATERIAL) @map("type") /// 材料类型 
  remark   String?      @map("remark") /// 备注
  isActive Boolean      @default(true) @map("is_active") /// 是否启用

  // 树形结构字段
  parentId String? @map("parent_id") /// 父级id
  fullId   String  @default("") @map("full_id") /// 全路径id(使用|分隔)
  fullName String  @default("") @map("full_name") /// 全名称(使用|分隔)
  level    Int     @default(1) @map("level") /// 级别
  sort     Int     @default(1) @map("sort") /// 排序
  isLeaf   Boolean @default(true) @map("is_leaf") /// 是否叶子节点

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  children MaterialDictionaryCategory[] @relation("materialDictionaryCategoryHierarchy")
  parent   MaterialDictionaryCategory?  @relation("materialDictionaryCategoryHierarchy", fields: [parentId], references: [id])

  // 模型关联
  materialDictionaryVersion MaterialDictionaryVersion  @relation(fields: [materialDictionaryVersionId], references: [id]) /// 材料字典 - 版本
  materialDictionaryDetail  MaterialDictionaryDetail[] /// 材料字典 - 明细

  @@unique([tenantId, orgId, id])
  @@map("material_dictionary_category")
}

/// 材料字典 - 明细
model MaterialDictionaryDetail {
  // 主键 & 外键
  tenantId                     String @map("tenant_id") /// 租户id
  orgId                        String @map("org_id") /// 组织id
  id                           String @id @default(uuid(7)) @map("id") /// 数据id
  materialDictionaryVersionId  String @map("material_dictionary_version_id") /// 材料字典版本id
  materialDictionaryCategoryId String @map("material_dictionary_category_id") /// 材料字典分类id

  // 业务字段
  code               String       @map("code") /// 编码
  name               String       @map("name") /// 名称
  specificationModel String?      @map("specification_model") /// 规格型号
  meteringUnit       String?      @map("metering_unit") /// 计量单位
  type               MaterialType @default(CONSUME_MATERIAL) @map("type") /// 材料类型
  remark             String?      @map("remark") /// 备注
  accountExplanation String?      @map("account_explanation") /// 核算说明
  sort               Int          @default(1) @map("sort") /// 排序
  isActive           Boolean      @default(true) @map("is_active") /// 是否启用

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  materialDictionaryVersion               MaterialDictionaryVersion                 @relation(fields: [materialDictionaryVersionId], references: [id])
  materialDictionaryCategory              MaterialDictionaryCategory                @relation(fields: [materialDictionaryCategoryId], references: [id])
  materialDictionaryUnitCalculation       MaterialDictionaryUnitCalculation[] /// 材料字典 - 单位换算
  materialDetailBusinessCostSubjectDetail MaterialDetailBusinessCostSubjectDetail[] /// 材料字典明细 & 业务成本科目明细 - 中间表

  @@unique([tenantId, orgId, id])
  @@map("material_dictionary_detail")
}

/// 材料字典 - 单位换算
model MaterialDictionaryUnitCalculation {
  // 主键 & 外键
  tenantId                   String @map("tenant_id") /// 租户id
  orgId                      String @map("org_id") /// 组织id
  id                         String @id @default(uuid(7)) @map("id") /// 数据id
  materialDictionaryDetailId String @map("material_dictionary_detail_id") /// 材料字典明细id

  // 业务字段
  unit   String  @map("unit") /// 单位
  factor Decimal @map("factor") @db.Decimal(10, 5) /// 换算系数 
  remark String? @map("remark") /// 备注
  sort   Int     @default(1) @map("sort") /// 排序

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  materialDictionaryDetail MaterialDictionaryDetail @relation(fields: [materialDictionaryDetailId], references: [id])

  @@unique([tenantId, orgId, id])
  @@map("material_dictionary_unit_calculation")
}

/// 材料字典明细 & 业务成本科目明细 - 中间表
model MaterialDetailBusinessCostSubjectDetail {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  businessCostSubjectDetailId String @map("business_cost_subject_detail_id") /// 业务成本科目明细 id
  materialDictionaryDetailId  String @map("material_dictionary_detail_id") /// 材料字典明细 id

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  materialDictionaryDetail  MaterialDictionaryDetail  @relation(fields: [materialDictionaryDetailId], references: [id])
  businessCostSubjectDetail BusinessCostSubjectDetail @relation(fields: [businessCostSubjectDetailId], references: [id])

  @@unique([tenantId, orgId, id])
  @@map("material_detail_business_cost_subject_detail")
}
