/// 财务成本科目
model FinancialCostSubject {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  code String @map("code") /// 编码
  name String @map("name") /// 名称

  // 树形结构字段
  parentId String? @map("parent_id") /// 父级id
  fullId   String  @default("") @map("full_id") /// 全路径id(使用|分隔)
  fullName String  @default("") @map("full_name") /// 全名称(使用|分隔)
  level    Int     @default(1) @map("level") /// 级别
  sort     Int     @default(1) @map("sort") /// 排序
  isLeaf   <PERSON>an @default(true) @map("is_leaf") /// 是否叶子节点

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  parent                    FinancialCostSubject?       @relation("FinancialCostSubjectTree", fields: [parentId], references: [id])
  children                  FinancialCostSubject[]      @relation("FinancialCostSubjectTree")
  businessCostSubjectDetail BusinessCostSubjectDetail[]

  @@unique([tenantId, orgId, id])
  @@map("financial_cost_subject")
}
