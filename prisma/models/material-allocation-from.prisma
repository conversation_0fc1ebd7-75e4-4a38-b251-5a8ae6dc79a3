// 调拨单-单据表
model MaterialAllocationFrom {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  materialSettlementStatus MaterialSettlementStatus @default(UN_SETTLED) @map("material_settlement_status") /// 结算状态
  transferInProjectId      String?                  @map("transfer_in_project_id") /// 调入项目id
  code                     String                   @map("code") /// 单据编码
  amount                   Decimal?                 @map("amount") @db.Decimal(20, 6) /// 调拨金额
  submitStatus             SubmitStatus             @default(PENDING) @map("submit_status") /// 提交状态
  auditStatus              AuditStatus              @default(PENDING) @map("audit_status") /// 审批状态
  year                     Int                      @map("year") /// 年
  month                    Int                      @map("month") /// 月
  day                      Int                      @map("day") /// 日

  // 公共字段
  isDeleted                        Boolean                            @default(false) @map("is_deleted") /// 是否删除
  createBy                         String                             @default("system") @map("create_by") /// 创建人
  updateBy                         String                             @default("system") @map("update_by") /// 更新人
  createAt                         DateTime                           @default(now()) @map("create_at") /// 创建时间
  updateAt                         DateTime                           @updatedAt @map("update_at") /// 更新时间
  MaterialAllocationFromDetail     MaterialAllocationFromDetail[]
  MaterialAllocationFromAttachment MaterialAllocationFromAttachment[]

  @@unique([tenantId, orgId, id])
  @@map("material_allocation_from")
}

// 调拨单-单据明细表
model MaterialAllocationFromDetail {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  materialAllocationFromId     String      @map("material_allocation_from_id") /// 调拨单id
  parentId                     String?     @map("parent_id") /// 父级ID
  materialReceivingInventoryId String?     @map("material_receiving_inventory_id") /// 收料单库存ID
  detailId                     String?     @map("detail_id") /// 明细ID (收料单库存id/退库单明细id)
  detailType                   DetailType? @map("detail_type") /// 明细类型
  detailCode                   String?     @map("detail_code") /// 明细的单据编码
  detailDate                   DateTime?   @map("detail_date") /// 明细的单据时间
  materialId                   String      @map("material_id") /// 材料ID
  materialName                 String      @map("material_name") /// 材料名称
  materialSpec                 String      @map("material_spec") /// 材料规格
  unit                         String?     @map("unit") /// 计量单位
  inStockQuantity              Decimal?    @map("in_stock_quantity") @db.Decimal(20, 8) /// 在库数量
  inStockPrice                 Decimal?    @map("in_stock_price") @db.Decimal(20, 6) /// 在库单价
  allocationQuantity           Decimal?    @map("allocation_quantity") @db.Decimal(20, 8) /// 调拨数量
  allocationPrice              Decimal?    @map("allocation_price") @db.Decimal(20, 6) /// 调拨单价
  allocationAmount             Decimal?    @map("allocation_amount") @db.Decimal(20, 6) /// 调拨金额
  orderNo                      Int         @default(autoincrement()) @map("order_no") /// 排序号
  remark                       String?     @map("remark") /// 备注

  materialAllocationFrom MaterialAllocationFrom @relation(fields: [materialAllocationFromId], references: [id])

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_allocation_from_detail")
}

// 调拨单-附件表
model MaterialAllocationFromAttachment {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  materialAllocationFromId String @map("material_allocation_from_id") /// 调拨单id
  fileName                 String @map("file_name") /// 文件名称
  fileKey                  String @map("file_key") /// 文件key
  fileSize                 Int    @map("file_size") /// 文件大小
  fileExt                  String @map("file_ext") /// 文件后缀
  fileContentType          String @map("file_content_type") /// 文件contentType

  materialAllocationFrom MaterialAllocationFrom @relation(fields: [materialAllocationFromId], references: [id])

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_allocation_form_attachment")
}
