/// 字段类型
enum BasicProjectFieldType {
  TEXT /// 文本
  NUMBER /// 数值
  DATETIME /// 日期时间
  ENUM_SINGLE_CHOICE /// 枚举单选
  ENUM_MULTIPLE_CHOICE /// 枚举多选
}

/// 项目基本信息-分类设置
model BasicProjectInfoCategory {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  name      String  @map("name") /// 分组名称
  sort      Int     @map("sort") /// 分组排序
  isPublish Boolean @default(false) @map("is_publish") /// 是否发布
  isDefault Boolean @default(false) @map("is_default") /// 是否默认

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  basicProjectInfoFieldDetail BasicProjectInfoFieldDetail[]

  @@unique([tenantId, orgId, id])
  @@map("basic_project_info_category")
}

/// 项目基本信息-字段设置
model BasicProjectInfoFieldDetail {
  // 主键 & 外键
  tenantId                   String @map("tenant_id") /// 租户id
  orgId                      String @map("org_id") /// 组织id
  id                         String @id @default(uuid(7)) @map("id") /// 数据id
  basicProjectInfoCategoryId String @map("basic_project_info_category_id") /// 分类id

  // 业务字段
  name        String                @map("name") /// 字段名称
  type        BasicProjectFieldType @default(TEXT) @map("type") /// 字段类型
  isRequired  Boolean               @default(false) @map("is_required") /// 是否必填
  unit        String?               @map("unit") /// 单位
  enumValue   String?               @map("enum_value") /// 枚举值
  placeholder String?               @map("placeholder") /// 字段提示(占位符)
  description String?               @map("description") /// 字段含义
  sort        Int                   @map("sort") /// 排序
  status      EnableStatus          @default(ENABLED) @map("status") /// 是否启用
  isDefault   Boolean               @default(false) @map("is_default") /// 是否默认

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  basicProjectInfoCategory BasicProjectInfoCategory @relation(fields: [basicProjectInfoCategoryId], references: [id])
  // BasicProjectInfoLedger   BasicProjectInfoLedger[]
  BasicProjectInfoLedger   BasicProjectInfoLedger[]

  @@unique([tenantId, orgId, id])
  @@map("basic_project_info_field_detail")
}

// 公司项目信息字段详情
model BasicProjectInfoLedger {
  // 主键 & 外键
  id                            String @id @default(uuid(7)) @map("id") /// 数据id
  tenantId                      String @map("tenant_id") /// 租户id
  orgId                         String @map("org_id") /// 组织id
  basicProjectInfoFieldDetailId String @map("basic_project_info_field_detail_id") /// 项目基本信息-字段id

  // 业务字段
  value String? @map("value") /// 数据值

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  basicProjectInfoFieldDetail BasicProjectInfoFieldDetail @relation(fields: [basicProjectInfoFieldDetailId], references: [id])

  // 模型关联
  @@unique([tenantId, orgId, id])
  @@map("basic_project_info_ledger")
}
