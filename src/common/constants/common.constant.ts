import { MaterialType, PurchaseType } from '@/prisma/generated';

export const MaterialTypeText: Record<MaterialType, string> = {
  [MaterialType.CONSUME_MATERIAL]: '消耗材料',
  [MaterialType.CONCRETE]: '商品混凝土',
  [MaterialType.TURNOVERME_MATERIAL]: '周转材料',
  [MaterialType.FIXEDASSETSL_CONSUMABLES]: '固定资产/低值易耗品'
};

export const PurchaseTypeText = {
  [PurchaseType.SELF_PURCHASE]: '自采',
  [PurchaseType.CENTRALIZED_PURCHASE]: '集采',
  [PurchaseType.PARTY_A_DIRECTED]: '甲指',
  [PurchaseType.PARTY_A_SUPPLIED]: '甲供',
  [PurchaseType.TRANSFER_IN]: '调入'
};

export const PurchaseTypeEn: Record<string, PurchaseType> = {
  自采: PurchaseType.SELF_PURCHASE,
  集采: PurchaseType.CENTRALIZED_PURCHASE,
  甲指: PurchaseType.PARTY_A_DIRECTED,
  甲供: PurchaseType.PARTY_A_SUPPLIED,
  调入: PurchaseType.TRANSFER_IN
};

export const HTTP_TIME_OUT_10M = 10 * 60 * 1000;

// 任务状态
export const TaskStatus = {
  DOING: 'doing',
  DONE: 'done',
  ERROR: 'error'
};
