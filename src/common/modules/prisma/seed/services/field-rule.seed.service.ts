import { Injectable, Logger } from '@nestjs/common';

import { PrismaService } from '@/common/modules/prisma/prisma.service';
import {
  ContractTemplateClassifyType,
  FieldClassifyType,
  FieldType,
  ModuleSourceType
} from '@/prisma/generated';

import * as contractTemplateData from '../seed-data/field-rule.json';

@Injectable()
export class FieldRuleService {
  private readonly logger = new Logger(FieldRuleService.name);
  constructor() {}

  // 初始化字段规则
  async init(txPrisma: PrismaService) {
    this.logger.log('开始初始化字段规则...');
    if (!contractTemplateData || !contractTemplateData.length) {
      return [];
    }

    // 深拷贝
    const contractTemplate = JSON.parse(JSON.stringify(contractTemplateData));

    for (const element of contractTemplate) {
      const fieldData = await txPrisma.fieldRule.findFirst({
        where: {
          name: element.name,
          type: element.type as ContractTemplateClassifyType,
          isDeleted: false
        }
      });
      if (fieldData) {
        continue;
      }
      const param = {
        name: element.name,
        classify: element.classify as FieldClassifyType,
        isRequired: element.isRequired,
        code: element.code,
        isDefaultRequired: element.isDefaultRequired,
        isUpdate: element.isUpdate,
        fieldType: element.fieldType as FieldType,
        enumValue: element.enumValue,
        type: element.type as ContractTemplateClassifyType,
        moduleSource: element.moduleSource as ModuleSourceType,
        sort: element.sort,
        isMatching: element.isMatching
      };
      await txPrisma.fieldRule.create({
        data: {
          ...param
        }
      });
      this.logger.log(`初始化合同范本字段规则：${element.name}成功`);
    }
  }
}
