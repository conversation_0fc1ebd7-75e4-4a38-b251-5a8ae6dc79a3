import { Injectable, Logger } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import * as concreteSurchargeData from '../seed-data/concrete-surcharge.json';

@Injectable()
export class ConcreteSurchargeSeedService {
  private readonly logger = new Logger(ConcreteSurchargeSeedService.name);
  constructor() {}

  // 初始化字段规则
  async init(
    txPrisma: PrismaService,
    reqUser: IReqUser,
    contractId: string,
    parentData: any[] = []
  ) {
    this.logger.log('开始初始化附加费数据...');
    if (!concreteSurchargeData) {
      return [];
    }

    // 深拷贝
    const concreteSurchargeTemplate = JSON.parse(
      JSON.stringify(concreteSurchargeData)
    );

    for (const element of concreteSurchargeTemplate) {
      const projectCategory =
        await txPrisma.contractConcreteSurcharge.findFirst({
          select: {
            id: true
          },
          where: {
            name: element.name,
            unit: element.unit,
            isDeleted: false,
            tenantId: reqUser.tenantId,
            orgId: reqUser.orgId,
            materialContractId: contractId
          }
        });
      if (!projectCategory) {
        await txPrisma.contractConcreteSurcharge.create({
          data: {
            ...element,
            price:
              parentData.find((item) => item.name === element.name)
                ?.changePrice || null,
            remark:
              parentData.find((item) => item.name === element.name)?.remark ||
              null,
            materialContractId: contractId,
            tenantId: reqUser.tenantId,
            orgId: reqUser.orgId,
            createBy: 'system',
            updateBy: 'system'
          }
        });
        this.logger.log(`初始化附加费：${element.name}成功`);
      }
    }
  }
}
