import { Injectable, Logger } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import * as supplierDirectoryData from '../seed-data/supplier-directory.json';

@Injectable()
export class SupplierDirectorySeedService {
  private readonly logger = new Logger(SupplierDirectorySeedService.name);
  constructor() {}

  // 初始化字段规则
  async init(txPrisma: PrismaService, reqUser: IReqUser) {
    this.logger.log('开始初始化供应商...');
    if (!supplierDirectoryData) {
      return [];
    }

    // 深拷贝
    const supplierDirectoryTemplate = JSON.parse(
      JSON.stringify(supplierDirectoryData)
    );

    const projectCategory = await txPrisma.supplierDirectory.findFirst({
      select: {
        id: true
      },
      where: {
        creditCode: supplierDirectoryTemplate.creditCode,
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId
      }
    });
    if (!projectCategory) {
      await txPrisma.supplierDirectory.create({
        data: {
          ...supplierDirectoryTemplate,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          creator: 'system',
          createBy: 'system',
          updateBy: 'system'
        }
      });
    }
    this.logger.log(`初始化供应商：${supplierDirectoryTemplate.fullName}成功`);
  }
}
