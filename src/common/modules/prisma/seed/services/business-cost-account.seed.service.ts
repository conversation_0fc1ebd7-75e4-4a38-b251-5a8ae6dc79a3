import { Injectable, Logger } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { Dict_Type } from '@/prisma/generated';

import * as costAccountTemplateData from '../seed-data/business-cost-account.json';

@Injectable()
export class BusinessCostAccountSeedService {
  private readonly logger = new Logger(BusinessCostAccountSeedService.name);
  constructor() {}

  // 初始化成品核算数据
  async init(txPrisma: PrismaService, reqUser: IReqUser) {
    this.logger.log('开始初始化成品核算数据...');
    if (!costAccountTemplateData || !costAccountTemplateData.length) {
      return [];
    }

    // 深拷贝
    const costAccountTemplate = JSON.parse(
      JSON.stringify(costAccountTemplateData)
    );

    for (const element of costAccountTemplate) {
      const data = await txPrisma.accountDictionary.findFirst({
        where: {
          name: element.name,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        }
      });
      if (data) {
        continue;
      }
      const param = {
        name: element.name,
        sort: element.sort,
        type: element.type as Dict_Type
      };
      await txPrisma.accountDictionary.create({
        data: {
          ...param,
          createBy: reqUser.id,
          updateBy: reqUser.id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        }
      });
      this.logger.log(`初始化成品核算：${element.name}成功`);
    }
  }
}
