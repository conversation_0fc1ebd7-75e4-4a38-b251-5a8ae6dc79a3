import { Injectable, Logger } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { BasicProjectFieldType } from '@/prisma/generated';

import * as projectData from '../seed-data/project.json';

@Injectable()
export class ProjectService {
  private readonly logger = new Logger(ProjectService.name);
  constructor() {}

  // 初始化字段规则
  async init(txPrisma: PrismaService, reqUser: IReqUser) {
    this.logger.log('开始初始化项目分类及字段信息...');
    if (!projectData) {
      return [];
    }

    // 深拷贝
    const projectTemplate = JSON.parse(JSON.stringify(projectData));

    const projectCategory = await txPrisma.basicProjectInfoCategory.findFirst({
      where: {
        name: projectTemplate.name,
        isDeleted: false,
        isDefault: projectTemplate.isDefault,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId
      }
    });
    if (!projectCategory) {
      const category = await txPrisma.basicProjectInfoCategory.create({
        data: {
          name: projectTemplate.name,
          isDeleted: false,
          sort: projectTemplate.sort,
          isPublish: projectTemplate.isPublish,
          isDefault: projectTemplate.isDefault,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          createBy: 'system',
          updateBy: 'system'
        }
      });
      await txPrisma.basicProjectInfoFieldDetail.createMany({
        data: projectTemplate.basicProjectInfoFieldDetail.map((item: any) => ({
          basicProjectInfoCategoryId: category.id,
          name: item.name,
          type: item.type as BasicProjectFieldType,
          enumValue: item.enumValue,
          description: item.description,
          isRequired: item.isRequired,
          placeholder: item.placeholder,
          sort: item.sort,
          unit: item.unit,
          value: item.value,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDefault: item.isDefault,
          createBy: 'system',
          updateBy: 'system'
        }))
      });
    }
    this.logger.log(`初始化项目信息：${projectTemplate.name}成功`);
  }
}
