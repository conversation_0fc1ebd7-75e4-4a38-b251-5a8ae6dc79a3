import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { Prisma, PrismaClient } from '@/prisma/generated';

import { PrismaExtensions } from './prisma.extension';

@Injectable()
export class PrismaService
  extends PrismaClient<Prisma.PrismaClientOptions, 'query'>
  implements OnModuleInit
{
  private readonly logger = new Logger(PrismaService.name);

  constructor(private configService: ConfigService) {
    super({
      datasources: {
        db: {
          url: configService.get('database.url')
        }
      },
      log: [{ level: 'query', emit: 'event' }]
    });

    const clientExt = this.$extends(PrismaExtensions.excludeDeletedDataExt)
      .$extends(PrismaExtensions.setDefaultDateTimeExt)
      .$extends(PrismaExtensions.formatResultDateTimeExt)
      .$extends(PrismaExtensions.formatResultFieldExt)
      .$extends(PrismaExtensions.transformDecimalToNumberExt);

    // 注册 sql 日志
    this.registerSqlLogger();

    return clientExt as this;
  }

  async onModuleInit() {
    await this.$connect();
  }

  private registerSqlLogger() {
    if (this.configService.get('database.logQueryEvent') === 'true') {
      this.$on('query', (e) => {
        if (e.query === 'SELECT 1') {
          return;
        }

        // 替换参数值
        let rawSql = e.query;
        const paramList = JSON.parse(e.params) as any[];
        paramList.forEach((param, index) => {
          if (typeof param === 'string') {
            param = `'${param}'`;
          }
          if (Array.isArray(param)) {
            param = param.map((item) => {
              if (typeof item === 'string') {
                item = `"${item}"`;
              }
              return item;
            });
            param = `'{${param.join(',')}}'`;
          }
          rawSql = rawSql.replace(`$${index + 1}`, param);
        });

        this.logger.verbose(`
Query:
${rawSql}
Duration: ${e.duration}ms
`);
      });
    }
  }
}
