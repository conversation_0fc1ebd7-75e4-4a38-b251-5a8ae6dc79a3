import { Injectable, Logger } from '@nestjs/common';

import { PrismaService } from '@/common/modules/prisma/prisma.service';

import { FieldRuleService } from './seed/services/field-rule.seed.service';
import { ProjectService } from './seed/services/project.seed.service';
import { SupplierDirectorySeedService } from './seed/services/supplier-directory.seed.service';

@Injectable()
export class PrismaSeedService {
  private readonly logger = new Logger(PrismaSeedService.name);

  constructor(
    private prisma: PrismaService,
    private fieldRuleService: FieldRuleService,
    private projectService: ProjectService,
    private supplierDirectorySeedService: SupplierDirectorySeedService
  ) {}

  /**
   * 初始化数据
   */
  async init() {
    // this.logger.log(' ========== 开始初始化数据 ========== ');
    // await this.initSeedData();
    // this.logger.log(' ========== 数据初始化完成 ========== ');
  }

  private async initSeedData() {
    try {
      await this.prisma.$transaction(
        async (tx) => {
          // 初始化字段规则
          // await this.fieldRuleService.init(tx as PrismaService);
          // 初始化项目信息
          // await this.projectService.init(tx as PrismaService);
        },
        { timeout: 300000 }
      );
    } catch (error) {
      console.log(error);
    }
  }
}
