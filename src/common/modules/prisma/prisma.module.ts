import { Module } from '@nestjs/common';

import { PrismaSeedService } from './prisma.seed.service';
import { PrismaService } from './prisma.service';
import {
  BusinessCostAccountSeedService,
  ConcreteSurchargeSeedService,
  FieldRuleService,
  ProjectService
} from './seed/services';
import { SupplierDirectorySeedService } from './seed/services/supplier-directory.seed.service';

@Module({
  providers: [
    PrismaService,
    PrismaSeedService,
    FieldRuleService,
    ProjectService,
    SupplierDirectorySeedService,
    BusinessCostAccountSeedService,
    ConcreteSurchargeSeedService
  ],
  exports: [
    PrismaService,
    PrismaSeedService,
    ProjectService,
    SupplierDirectorySeedService,
    BusinessCostAccountSeedService,
    ConcreteSurchargeSeedService
  ]
})
export class PrismaModule {}
