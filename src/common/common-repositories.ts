import { ChangeDataOrderDto } from './dtos/common.dto';
import { PrismaService } from './modules/prisma/prisma.service';

export class CommonRepositories {
  // 交换两条数据的顺序号
  static async changeDataOrderNo(
    prisma: PrismaService,
    data: ChangeDataOrderDto
  ) {
    const { fromId, toId, tableName, tenantId, orgId } = data;
    const sql = `
      WITH target_records AS (
        SELECT
          id,
          order_no,
          CASE
            WHEN id = $1 THEN $2
            WHEN id = $2 THEN $1
          END AS swap_with_id
        FROM ${tableName}
        WHERE id IN ($1, $2)
          AND is_deleted = false
          AND tenant_id = $3
          AND org_id = $4
      ),
      order_mapping AS (
        SELECT
          t1.id,
          t2.order_no AS new_order_no
        FROM target_records t1
        JOIN target_records t2 ON t1.swap_with_id = t2.id
      )
      UPDATE ${tableName}
      SET order_no = order_mapping.new_order_no
      FROM order_mapping
      WHERE ${tableName}.id = order_mapping.id
    `;

    const values = [fromId, toId, tenantId, orgId];
    await prisma.$executeRawUnsafe(sql, ...values);

    return true;
  }
}
