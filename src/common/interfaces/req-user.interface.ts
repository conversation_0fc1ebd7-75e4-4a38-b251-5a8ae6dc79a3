const enum UserStatus {
  ACTIVE = 'ACTIVE',
  DEACTIVE = 'DEACTIVE',
  FREEZE = 'FREEZE'
}

export interface IReqUser {
  /**
   * 用户id
   */
  id: string;

  /**
   * 租户id
   */
  tenantId: string;

  /**
   * 组织id
   */
  orgId: string;

  /**
   * 状态
   */
  status: UserStatus;

  /**
   * 昵称
   */
  nickname: string;

  /**
   * 用户名
   */
  username: string;

  /**
   * 电话
   */
  phone: string;

  /**
   * 是否系统管理员
   */
  isAdmin: boolean;
}
