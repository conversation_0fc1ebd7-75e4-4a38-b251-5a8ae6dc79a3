import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsString } from 'class-validator';

export class EditMoveDto {
  @ApiProperty({ description: 'id', default: 'xxxx' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '移动类型', default: 'up:上移，down：下移' })
  @IsNotEmpty({ message: '移动类型不能为空' })
  @IsString({ message: '移动类型必须是字符串' })
  moveType: 'up' | 'down';
}

// 时间选择器返回数据定义（进场验收、领料、退库）
export class TimeListResponseDto {
  @ApiProperty({ description: 'id' })
  id: string;

  @ApiProperty({ description: 'parentId' })
  parentId?: string | null;

  @ApiProperty({ description: '年' })
  year?: number;

  @ApiProperty({ description: '月' })
  month?: number;

  @ApiProperty({ description: '日' })
  day?: number;

  @ApiProperty({ description: '单据数量' })
  count: number;
}

// 附件常用字典dto定义
export class FileOperateDto {
  @ApiProperty({ description: '文件名称' })
  @IsString()
  fileName: string;

  @ApiProperty({ description: '文件key' })
  @IsString()
  fileKey: string;

  @ApiProperty({ description: '文件大小' })
  @IsInt()
  fileSize: number;

  @ApiProperty({ description: '文件后缀' })
  @IsString()
  fileExt: string;

  @ApiProperty({ description: '文件类型' })
  @IsString()
  fileContentType: string;
}

export class ChangeDataOrderDto {
  @ApiProperty({ description: '租户id' })
  tenantId: string;
  @ApiProperty({ description: '组织id' })
  orgId: string;
  @ApiProperty({ description: '数据id' })
  fromId: string;
  @ApiProperty({ description: '数据id' })
  toId: string;
  @ApiProperty({ description: '数据表名' })
  tableName: string;
}
