import {
  ArgumentMetadata,
  BadRequestException,
  PipeTransform
} from '@nestjs/common';

export class EnumValidationPipe<T extends Record<string, string | number>>
  implements PipeTransform
{
  constructor(private readonly enumType: T) {}

  transform(value: any, metadata: ArgumentMetadata) {
    if (value === undefined || value === null || value === '') {
      return value;
    }

    if (!Object.values(this.enumType).includes(value)) {
      throw new BadRequestException(
        `${metadata.data} 参数值 "${value}" 不是有效的枚举值`
      );
    }

    return value;
  }
}
