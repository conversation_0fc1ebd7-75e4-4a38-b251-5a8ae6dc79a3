import { ConsoleLogger } from '@nestjs/common';

export class CustomConsoleLogger extends ConsoleLogger {
  constructor() {
    super({
      prefix: 'ECOST',
      timestamp: true
    });
  }

  // 忽略的上下文(nestjs 内部日志)
  static contextsToIgnore = ['InstanceLoader', 'RoutesResolver', 'RouterExplorer'];

  log(message: any, context?: string) {
    if (context) {
      if (!CustomConsoleLogger.contextsToIgnore.includes(context)) {
        super.log(message, context);
      }
    } else {
      super.log(message);
    }
  }
}
