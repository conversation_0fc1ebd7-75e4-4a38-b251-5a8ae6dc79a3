import fastifyMultipart from '@fastify/multipart';
import { Logger, ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import {
  FastifyAdapter,
  NestFastifyApplication
} from '@nestjs/platform-fastify';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

import { PrismaSeedService } from '@/common/modules/prisma/prisma.seed.service';

import { AppModule } from './app.module';
import { CustomConsoleLogger } from './custom-console-logger';

const logger = new Logger('Main');

async function bootstrap() {
  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter(),
    {
      logger: new CustomConsoleLogger()
    }
  );

  // 注册 Fastify 的 Multipart 插件
  await app.register(fastifyMultipart as any, {
    limits: {
      fileSize: 1024 * 1024 * 100 // 100MB
    }
  });

  // 获取配置服务
  const configService = app.get(ConfigService);

  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: false,
      skipMissingProperties: true
    })
  );

  // Swagger 配置
  const config = new DocumentBuilder()
    .setTitle('ECOST Service API')
    .setDescription('ECOST Service API 文档')
    .setVersion('1.0')
    .addTag('项目成本控制管理系统')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        in: 'header'
      },
      'JWT-auth'
    )
    .build();
  const document = SwaggerModule.createDocument(app, config);
  // 只在开发环境开启 Swagger
  if (configService.get('app.envType') === 'dev') {
    SwaggerModule.setup('api-docs', app, document, {
      jsonDocumentUrl: '/api-docs-json'
    });
  }

  // 初始化种子数据
  const seedService = app.get(PrismaSeedService);
  await seedService.init();

  // 使用配置的端口
  const port = configService.get('app.port');
  await app.listen(port, '0.0.0.0');
  logger.log(`Application is running on: ${await app.getUrl()}`);
}
bootstrap();
