import { queryPeriodByMonthDate } from '@ewing/infra-cloud-sdk';
import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

import { ParamsName } from '@/prisma/generated';

import { orgsInfoResponseDto } from './platform.dto';

@Injectable()
export class PlatformService {
  constructor(
    private configService: ConfigService,
    private readonly httpService: HttpService
  ) {}

  // 根据用户id数组获取用户信息
  async getUsers(req: Request, userIds: string[]) {
    const result = await firstValueFrom(
      this.httpService.get(
        `${this.configService.get('app.apiUrl.platform')}/user/users-info`,
        {
          params: {
            userIds: userIds.join(',')
          },
          headers: { ...this.pickHeader(req) }
        }
      )
    );
    if (result.status === 200) {
      return result.data;
    } else {
      throw new HttpException('获取用户信息失败', HttpStatus.BAD_REQUEST);
    }
  }

  // 获取当前组织信息
  async getCurrentOrgInfo(req: Request, tenantId: string, orgId: string) {
    const org = await firstValueFrom(
      this.httpService.get(
        `${this.configService.get('app.apiUrl.platform')}/org/one`,
        {
          params: {
            orgId,
            tenantId
          },
          headers: { ...this.pickHeader(req) }
        }
      )
    );

    if (org.status === 200) {
      return org.data;
    } else {
      throw new HttpException('获取组织失败', HttpStatus.BAD_REQUEST);
    }
  }

  // 获取当前组织的id查询该组织所有上级层级
  async getOrgParentIds(req: Request, tenantId: string, orgId: string) {
    const parentList = await firstValueFrom(
      this.httpService.get(
        `${this.configService.get('app.apiUrl.platform')}/org/parent-org`,
        {
          params: {
            orgId,
            tenantId
          },
          headers: { ...this.pickHeader(req) }
        }
      )
    );

    if (parentList.status === 200) {
      return parentList.data;
    } else {
      throw new HttpException('获取组织失败', HttpStatus.BAD_REQUEST);
    }
  }

  // 获取当前组织的id查询该组织所有上级层级
  async getTopOrg(req: Request, tenantId: string, orgId: string) {
    const parentList = await firstValueFrom(
      this.httpService.get(
        `${this.configService.get('app.apiUrl.platform')}/org/top-org`,
        {
          params: {
            orgId,
            tenantId
          },
          headers: { ...this.pickHeader(req) }
        }
      )
    );

    if (parentList.status === 200) {
      return parentList.data;
    } else {
      throw new HttpException('获取组织失败', HttpStatus.BAD_REQUEST);
    }
  }

  // 根据组织id获取组织信息
  async getOrgs(
    req: Request,
    tenantId: string,
    orgIds?: string[] // 不传orgIds，查全部组织
  ): Promise<orgsInfoResponseDto[]> {
    const params: Record<string, string> = { tenantId };
    if (orgIds) {
      params.orgIds = orgIds.join(',');
    }
    const result = await firstValueFrom(
      this.httpService.get(
        `${this.configService.get('app.apiUrl.platform')}/org/orgs-info`,
        {
          params,
          headers: { ...this.pickHeader(req) }
        }
      )
    );

    if (result.status === 200) {
      return result.data;
    } else {
      throw new HttpException('根据组织id获取组织信息', HttpStatus.BAD_REQUEST);
    }
  }

  private pickHeader(req: Request): Record<string, string> {
    const headers: Record<string, string> = {};
    for (const [key, value] of Object.entries(req.headers)) {
      if (key === 'authorization' || key.startsWith('x-')) {
        headers[key] = value;
      }
    }
    return headers;
  }

  // 获取用户权限信息
  async gerUserPermissions(req: Request, userId: string): Promise<void> {
    const result = await firstValueFrom(
      this.httpService.get(
        `${this.configService.get('app.apiUrl.platform')}/user/all-permissions`,
        {
          params: { userId },
          headers: { ...this.pickHeader(req) }
        }
      )
    );

    if (result.status === 200) {
      return result.data;
    } else {
      throw new HttpException('根据组织id获取组织信息', HttpStatus.BAD_REQUEST);
    }
  }

  async getOrgParams(
    req: Request,
    tenantId: string,
    orgId: string,
    paramsNames: string
  ): Promise<Record<string, object>> {
    const result = await firstValueFrom(
      this.httpService.get(
        `${this.configService.get('app.apiUrl.platform')}/org-params`,
        {
          params: {
            orgId,
            tenantId,
            paramsNames,
            paramsType: 'ECOST'
          },
          headers: { ...this.pickHeader(req) }
        }
      )
    );

    if (result.status === 200) {
      return result.data;
    } else {
      throw new HttpException(
        '根据组织id获取组织参数设置信息',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async getOrgPeriod(
    req: Request,
    tenantId: string,
    orgId: string,
    currentDate: Date
  ): Promise<Record<string, any>> {
    const paramsRet = await this.getOrgParams(
      req,
      tenantId,
      orgId,
      ParamsName.ACCOUNT_CYCLE
    );
    const accountCycle = paramsRet.ACCOUNT_CYCLE;
    const curPeriod = queryPeriodByMonthDate(currentDate, accountCycle);
    return curPeriod;
  }
}
