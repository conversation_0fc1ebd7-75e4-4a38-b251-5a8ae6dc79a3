import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { ExcelService } from './excel.service';
import { PlatformService } from './platform.service';

@Module({
  exports: [PlatformService, ExcelService],
  imports: [
    HttpModule.registerAsync({
      useFactory: () => ({
        timeout: 5000,
        maxRedirects: 5
      })
    })
  ],
  providers: [PlatformService, ExcelService]
})
export class PlatformModule {}
