import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

import {
  HTTP_TIME_OUT_10M,
  TaskStatus
} from '@/common/constants/common.constant';

@Injectable()
export class ExcelService {
  constructor(
    private configService: ConfigService,
    private readonly httpService: HttpService
  ) {}

  // export-excel
  async exportExcel(req: Request, data: Record<string, any>) {
    const result = await firstValueFrom(
      this.httpService.post(
        `${this.configService.get('app.apiUrl.excel')}/excel/common/export-excel`,
        data,
        {
          headers: { ...this.pickHeader(req) },
          timeout: HTTP_TIME_OUT_10M
        }
      )
    );
    if (result.data) {
      return result.data;
    } else {
      throw new HttpException('导出excel异常', HttpStatus.BAD_REQUEST);
    }
  }

  // 获取excel的处理进度
  async getExcelProgress(req: Request, params: Record<string, any>) {
    const result = await firstValueFrom(
      this.httpService.get(
        `${this.configService.get('app.apiUrl.excel')}/excel/common/export-excel/progress`,
        {
          params,
          headers: { ...this.pickHeader(req) }
        }
      )
    );
    if (result.status === 200) {
      return result.data;
    } else {
      throw new HttpException('获取excel处理进度异常', HttpStatus.BAD_REQUEST);
    }
  }

  private pickHeader(req: Request): Record<string, string> {
    const headers: Record<string, string> = {};
    for (const [key, value] of Object.entries(req.headers)) {
      if (key === 'authorization' || key.startsWith('x-')) {
        headers[key] = value;
      }
    }
    return headers;
  }

  // 定时获取任务进度
  async scheduleGetProgress(req: Request, taskId: string) {
    const timeout = 5 * 60 * 1000; // 5分钟超时
    const pollInterval = 3000; // 3秒轮询间隔
    const startTime = Date.now();

    // 使用 Promise 轮询获取 Excel 导出进度
    const data: Record<string, any> = await new Promise((resolve, reject) => {
      const interval = setInterval(() => {
        (async () => {
          try {
            // 检查是否超时
            if (Date.now() - startTime > timeout) {
              clearInterval(interval);
              reject(new Error('excel导出超时'));
              return;
            }

            const progress = await this.getExcelProgress(req, {
              taskId
            });

            // 检查转换是否成功完成
            if (progress.status === TaskStatus.DONE) {
              clearInterval(interval);
              resolve(progress);
              return;
            }

            // 检查转换是否失败
            if (progress.status === TaskStatus.ERROR) {
              clearInterval(interval);
              reject(new Error(`excel导出失败`));
              return;
            }
          } catch (error: any) {
            clearInterval(interval);
            reject(new Error(`excel导出失败: ${error.message}`));
          }
        })();
      }, pollInterval);
    });

    return data;
  }

  // 获取导入excel的数据
  async getExcelImportData(req: Request, data: Record<string, string>) {
    const result = await firstValueFrom(
      this.httpService.post(
        `${this.configService.get('app.apiUrl.excel')}/excel/common/import-excel`,
        data,
        {
          headers: { ...this.pickHeader(req) },
          timeout: HTTP_TIME_OUT_10M
        }
      )
    );
    if (result.data) {
      return result.data;
    } else {
      throw new HttpException('导出excel异常', HttpStatus.BAD_REQUEST);
    }
  }
}
