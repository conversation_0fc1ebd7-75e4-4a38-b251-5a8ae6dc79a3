import {
  CallH<PERSON>ler,
  ExecutionContext,
  Injectable,
  NestInterceptor
} from '@nestjs/common';
import { FastifyRequest } from 'fastify';
import { Observable } from 'rxjs';

@Injectable()
export class FastifyFileInterceptor implements NestInterceptor {
  constructor(private fieldName: string) {}

  async intercept(
    context: ExecutionContext,
    next: CallHandler
  ): Promise<Observable<any>> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<FastifyRequest>();

    try {
      const file = await request.file();
      if (file) {
        request.body = {
          ...((request.body as Record<string, any>) || {}),
          file: {
            fieldname: file.fieldname,
            originalname: file.filename,
            mimetype: file.mimetype,
            buffer: await file.toBuffer()
          }
        };
      }
    } catch (error) {
      console.error('文件上传错误:', error);
    }

    return next.handle();
  }
}
