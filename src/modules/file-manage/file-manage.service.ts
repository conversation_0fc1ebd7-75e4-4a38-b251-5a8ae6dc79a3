import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as ObsClient from 'esdk-obs-nodejs';
import * as stream from 'stream';

import { IReqUser } from '@/common/interfaces/req-user.interface';

@Injectable()
export class FileManageService {
  private obsClient: any;
  constructor(
    private configService: ConfigService
    // private obsClient: any
  ) {
    this.obsClient = new ObsClient({
      // 推荐通过环境变量获取AKSK，这里也可以使用其他外部引入方式传入，如果使用硬编码可能会存在泄露风险。
      // 您可以登录访问管理控制台获取访问密钥AK/SK，获取方式请参见https://support.huaweicloud.com/usermanual-ca/ca_01_0003.html
      access_key_id: this.configService.get('app.obs.ak'),
      secret_access_key: this.configService.get('app.obs.sk'),
      // 【可选】如果使用临时AK/SK和SecurityToken访问OBS，同样建议您尽量避免使用硬编码，以降低信息泄露风险。您可以通过环境变量获取访问密钥AK/SK，也可以使用其他外部引入方式传入
      // security_token: process.env.SECURITY_TOKEN,
      // endpoint填写Bucket对应的Endpoint, 这里以华北-北京四为例，其他地区请按实际情况填写
      server: this.configService.get('app.obs.endpoint')
    });
  }

  // 文件上传
  async upload(
    fileName: string,
    fileBuffer: Buffer,
    mimetype: string,
    reqUser: IReqUser
  ) {
    // 使用访问OBS
    // 文件上传
    const result = await this.obsClient.putObject({
      Bucket: this.configService.get('app.obs.bucketName'),
      Key: reqUser.tenantId + '/' + fileName,
      Body: stream.Readable.from(fileBuffer),
      ContentType: mimetype
    });

    if (result.CommonMsg.Status !== 200) {
      throw new BadRequestException(result.CommonMsg.Message);
    }

    const url = await this.getImageUrl(fileName, reqUser);

    return url;

    // 关闭obsClient
    // this.obsClient.close();
  }

  // 获取图片访问路径函数
  async getImageUrl(fileName: string, reqUser: IReqUser) {
    try {
      // 对象存在，生成访问URL
      const urlResult = await this.obsClient.createSignedUrlSync({
        Method: 'GET',
        Bucket: this.configService.get('app.obs.bucketName'),
        Key: reqUser.tenantId + '/' + fileName,
        Expires: 630720000 // URL有效期（秒）
      });
      return urlResult.SignedUrl;
    } catch (error) {
      console.error('获取图片URL出错:', error);
      throw error;
    }
  }

  // 下载
  async download(
    fileName: string,
    reqUser: IReqUser,
    folder: string = reqUser.tenantId
  ) {
    const result = await this.obsClient.getObject({
      Bucket: this.configService.get('app.obs.bucketName'),
      Key: folder + '/' + fileName,
      SaveAsStream: true
    });

    if (result.CommonMsg.Status !== 200) {
      throw new BadRequestException(result.CommonMsg.Message);
    }

    // 关闭obsClient
    // this.obsClient.close();

    return result.InterfaceResult.Content as ReadableStream;
  }

  // 删除文件
  async delete(fileName: string) {
    const result = await this.obsClient.deleteObject({
      Bucket: this.configService.get('app.obs.bucketName'),
      Key: fileName
    });

    if (result.CommonMsg.Status !== 204) {
      throw new BadRequestException(result.CommonMsg.Message);
    }

    // 关闭obsClient
    // this.obsClient.close();

    return true;
  }

  // // 获取文件元数据
  // async getObjectMetadata(fileName: string) {
  //   const result = await obsClient.getObjectMetadata({
  //     Bucket: this.configService.get('app.obs.bucketName'),
  //     Key: fileName
  //   });

  //   if (result.CommonMsg.Status === 200) {
  //     throw new BadRequestException(result.CommonMsg.Message);
  //   }

  //   console.log(result);

  //   // 关闭obsClient
  //   obsClient.close();

  //   return result;
  // }
}
