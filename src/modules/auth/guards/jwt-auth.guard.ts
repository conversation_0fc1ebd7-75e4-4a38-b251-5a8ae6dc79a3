import {
  ExecutionContext,
  Injectable,
  UnauthorizedException
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { FastifyRequest } from 'fastify';

import { IReqUser } from '@/common/interfaces/req-user.interface';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  canActivate(context: ExecutionContext) {
    const req = context.switchToHttp().getRequest<FastifyRequest>();

    const whiteList: string[] = [];
    if (whiteList.includes(req.url)) {
      return true;
    }

    return super.canActivate(context);
  }

  handleRequest<TUser extends IReqUser>(
    err: any,
    user: TUser,
    info: any,
    context: ExecutionContext,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    status?: any
  ) {
    if (err || !user) {
      throw err || new UnauthorizedException('Token验证失败');
    }

    // 获取请求头的 x-org-id 赋值给 user 作为请求上下文中的组织id
    const req = context.switchToHttp().getRequest<FastifyRequest>();
    user.orgId = req.headers['x-org-id'] as string;

    return user;
  }
}
