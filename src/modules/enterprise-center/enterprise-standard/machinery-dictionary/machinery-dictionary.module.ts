import { Modu<PERSON> } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';

import { PlatformModule } from '../../../platform/platform.module';
import { MachineryDictionaryCategoryController } from './category/machinery-dictionary-category.controller';
import { MachineryDictionaryCategoryService } from './category/machinery-dictionary-category.service';
import { MachineryDictionaryDetailController } from './detail/machinery-dictionary-detail.controller';
import { MachineryDictionaryDetailService } from './detail/machinery-dictionary-detail.service';
import { MachineryDictionaryVersionController } from './version/machinery-dictionary-version.controller';
import { MachineryDictionaryVersionService } from './version/machinery-dictionary-version.service';

@Module({
  controllers: [
    MachineryDictionaryVersionController,
    MachineryDictionaryCategoryController,
    MachineryDictionaryDetailController
  ],
  providers: [
    MachineryDictionaryVersionService,
    MachineryDictionaryCategoryService,
    MachineryDictionaryDetailService
  ],
  imports: [PrismaModule, PlatformModule]
})
export class MachineryDictionaryModule {}
