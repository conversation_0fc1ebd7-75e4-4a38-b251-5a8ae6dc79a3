import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { MoveToEnum } from '@/common/enums/common.enum';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  MachineryDictionaryDetailCreateDto,
  MachineryDictionaryDetailQueryParamDto,
  MachineryDictionaryDetailResultDto,
  MachineryDictionaryDetailUpdateDto
} from './machinery-dictionary-detail.dto';
import { MachineryDictionaryDetailService } from './machinery-dictionary-detail.service';

@ApiTags('机械字典/明细')
@Controller('machinery-dictionary-detail')
export class MachineryDictionaryDetailController {
  constructor(private readonly service: MachineryDictionaryDetailService) {}

  @ApiOperation({
    summary: '获取明细列表',
    description: '获取明细列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取明细列表成功',
    type: MachineryDictionaryDetailResultDto,
    isArray: true
  })
  @Get()
  async getList(
    @Query() queryParam: MachineryDictionaryDetailQueryParamDto,
    @ReqUser() reqUser: IReqUser
  ) {
    const list = await this.service.getList(reqUser, queryParam);
    return list;
  }

  @ApiOperation({
    summary: '创建明细',
    description: '创建明细'
  })
  @ApiResponse({
    status: 200,
    description: '创建明细成功',
    type: MachineryDictionaryDetailResultDto
  })
  @Post()
  async createOne(
    @Body() data: MachineryDictionaryDetailCreateDto,
    @ReqUser() reqUser: IReqUser
  ) {
    const detail = await this.service.createOne(reqUser, data);
    return detail;
  }

  @ApiOperation({
    summary: '更新明细',
    description: '更新明细'
  })
  @ApiResponse({
    status: 200,
    description: '更新明细成功',
    type: Boolean
  })
  @Patch(':id')
  async updateOne(
    @Param('id') id: string,
    @Body() data: MachineryDictionaryDetailUpdateDto,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.updateOne(id, data, reqUser);
    return true;
  }

  @ApiOperation({
    summary: '删除明细',
    description: '删除明细'
  })
  @ApiResponse({
    status: 200,
    description: '更新明细成功',
    type: Boolean
  })
  @Delete(':id')
  async deleteOne(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    await this.service.deleteOne(id, reqUser);
    return true;
  }

  @ApiOperation({
    summary: '移动明细',
    description: '移动明细'
  })
  @ApiResponse({
    status: 200,
    description: '移动成功',
    type: Boolean
  })
  @Patch(':id/_move')
  async moveOne(
    @Param('id') id: string,
    @Query('moveTo') moveTo: MoveToEnum,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.moveOne(id, moveTo, reqUser);
    return true;
  }
}
