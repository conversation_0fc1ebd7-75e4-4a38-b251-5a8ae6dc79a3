import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { MoveToEnum } from '@/common/enums/common.enum';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  MachineryDictionaryCategoryCreateDto,
  MachineryDictionaryCategoryResultDto,
  MachineryDictionaryCategoryUpdateDto
} from './machinery-dictionary-category.dto';
import { MachineryDictionaryCategoryService } from './machinery-dictionary-category.service';

@ApiTags('机械字典/分类')
@Controller('machinery-dictionary-category')
export class MachineryDictionaryCategoryController {
  constructor(private readonly service: MachineryDictionaryCategoryService) {}

  @ApiOperation({
    summary: '获取分类列表',
    description: '获取分类列表'
  })
  @ApiQuery({
    name: 'versionId',
    description: '版本id',
    required: true,
    type: String
  })
  @ApiResponse({
    status: 200,
    description: '获取分类列表成功',
    type: MachineryDictionaryCategoryResultDto,
    isArray: true
  })
  @Get()
  async getList(
    @Query('versionId') versionId: string,
    @ReqUser() reqUser: IReqUser
  ) {
    const list = await this.service.getList(reqUser, versionId);
    return list;
  }

  @ApiOperation({
    summary: '创建分类',
    description: '创建分类'
  })
  @ApiResponse({
    status: 200,
    description: '创建分类成功',
    type: MachineryDictionaryCategoryResultDto
  })
  @Post()
  async createOne(
    @Body() data: MachineryDictionaryCategoryCreateDto,
    @ReqUser() reqUser: IReqUser
  ) {
    const category = await this.service.createOne(data, reqUser);
    return category;
  }

  @ApiOperation({
    summary: '更新分类',
    description: '更新分类'
  })
  @ApiResponse({
    status: 200,
    description: '更新分类成功',
    type: Boolean
  })
  @Patch(':id')
  async updateOne(
    @Param('id') id: string,
    @Body() data: MachineryDictionaryCategoryUpdateDto,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.updateOne(id, data, reqUser);
    return true;
  }

  @ApiOperation({
    summary: '删除分类',
    description: '删除分类'
  })
  @ApiResponse({
    status: 200,
    description: '删除分类成功',
    type: Boolean
  })
  @Delete(':id')
  async deleteOne(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    await this.service.deleteOne(id, reqUser);
    return true;
  }

  @ApiOperation({
    summary: '移动分类',
    description: '移动分类'
  })
  @ApiQuery({
    name: 'moveTo',
    description: '移动至',
    required: true,
    enum: MoveToEnum
  })
  @ApiResponse({
    status: 200,
    description: '移动分类成功',
    type: Boolean
  })
  @Patch(':id/_move')
  async moveOne(
    @Param('id') id: string,
    @Query('moveTo') moveTo: MoveToEnum,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.moveOne(reqUser, id, moveTo);
    return true;
  }
}
