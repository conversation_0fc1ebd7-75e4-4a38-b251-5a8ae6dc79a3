import { Controller, Delete, Param, Patch } from '@nestjs/common';
import { Body, Get, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  MachineryDictionaryVersionCreateDto,
  MachineryDictionaryVersionResultDto,
  MachineryDictionaryVersionUpdateDto
} from './machinery-dictionary-version.dto';
import { MachineryDictionaryVersionService } from './machinery-dictionary-version.service';

@ApiTags('机械字典/版本')
@Controller('machinery-dictionary-version')
export class MachineryDictionaryVersionController {
  constructor(private readonly service: MachineryDictionaryVersionService) {}

  @ApiOperation({
    summary: '获取版本列表',
    description: '获取版本列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取版本列表成功',
    type: MachineryDictionaryVersionResultDto,
    isArray: true
  })
  @Get()
  async getList(@ReqUser() reqUser: IReqUser) {
    const list = await this.service.getList(reqUser);
    return list;
  }

  @ApiOperation({
    summary: '创建版本',
    description: '创建版本'
  })
  @ApiResponse({
    status: 200,
    description: '创建版本成功',
    type: Boolean
  })
  @Post()
  async createOne(
    @ReqUser() reqUser: IReqUser,
    @Body() data: MachineryDictionaryVersionCreateDto
  ) {
    await this.service.createOne(reqUser, data);
    return true;
  }

  @ApiOperation({
    summary: '更新版本',
    description: '更新版本'
  })
  @ApiResponse({
    status: 200,
    description: '更新版本成功',
    type: Boolean
  })
  @Patch(':id')
  async updateOne(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() data: MachineryDictionaryVersionUpdateDto
  ) {
    await this.service.updateOne(id, data, reqUser);
    return true;
  }

  @ApiOperation({
    summary: '删除版本',
    description: '删除版本'
  })
  @ApiResponse({
    status: 200,
    description: '删除版本成功',
    type: Boolean
  })
  @Delete(':id')
  async deleteOne(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    await this.service.deleteOne(id, reqUser);
    return true;
  }
}
