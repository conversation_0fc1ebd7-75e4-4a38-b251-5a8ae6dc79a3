import { BadRequestException, Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { PlatformService } from '@/modules/platform/platform.service';
import { EnableStatus, Prisma } from '@/prisma/generated';

import { TaxrateDictionaryDetailService } from '../taxrate-dictionary-detail/taxrate-dictionary-detail.service';
import {
  TaxrateDictionaryVersionCreateDto,
  TaxrateDictionaryVersionUpdateDto
} from './taxratel-dictionary-version.dto';

@Injectable()
export class TaxratelDictionaryVersionService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly taxrateDictionaryDetailService: TaxrateDictionaryDetailService,
    private readonly platformService: PlatformService
  ) {}

  async getList(reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;

    // 拿到当前组织的id查询该组织所有上级层级，
    // const parentOrgList =
    //   await this.platformService.getOrgParentIds(
    //     tenantId,
    //     orgId
    //   );

    // const orgIds: string[] = parentOrgList;

    const recordList = await this.prisma.taxrateDictionaryVersion.findMany({
      select: {
        id: true,
        name: true,
        orgId: true,
        status: true
      },
      where: {
        tenantId,
        orgId,
        // orgId: {
        //   in: orgIds
        // },
        isDeleted: false
      },
      orderBy: {
        createAt: 'desc'
      }
    });

    // 获取所有版本的引用数量
    const refProjectCountsList = await this.getAllProjectRefInfo(tenantId);

    const list = recordList.map((item, index) => {
      return {
        ...item,
        projectRefCount:
          refProjectCountsList.find((item1) => item1.versionId === item.id)
            ?._count._all || 0
      };
    });
    return list;
  }

  async createOne(reqUser: IReqUser, data: TaxrateDictionaryVersionCreateDto) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 检查数据是否重复
    await this.checkUnique(tenantId, orgId, data);

    await this.prisma.taxrateDictionaryVersion.create({
      data: {
        ...data,
        tenantId,
        orgId,
        createBy: userId,
        updateBy: userId
      }
    });
  }

  async updateOne(
    id: string,
    data: TaxrateDictionaryVersionUpdateDto,
    reqUser: IReqUser
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 检查数据是否重复
    if (data.name != null) {
      await this.checkUnique(tenantId, orgId, data, id);
    }
    // 停用时，判断当前版本数据是否被引用，如果没有被引用，将状态改为“未启用”
    if (data.status === EnableStatus.DISABLED) {
      const projectRef = (await this.getProjectRefInfo(
        tenantId,
        orgId,
        id
      )) as {
        projectRefCount: number;
      };
      // 如果没有被引用，将状态改为“未启用”
      if (projectRef.projectRefCount === 0) {
        data.status = EnableStatus.NOT_ENABLED;
      }
    }
    if (data.status === EnableStatus.ENABLED) {
      // 启用状态
      // 数据进行变更校验
      await this.taxrateDictionaryDetailService.change(id, reqUser);
    }

    await this.prisma.taxrateDictionaryVersion.update({
      where: {
        id,
        tenantId,
        orgId,
        isDeleted: false
      },
      data: {
        ...data,
        status: data.status as EnableStatus,
        updateBy: userId
      }
    });
  }

  async deleteOne(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 检查删除约束
    await this.checkDeleteConstraint(tenantId, orgId, id);

    await this.prisma.taxrateDictionaryVersion.update({
      where: {
        tenantId,
        orgId,
        id,
        isDeleted: false
      },
      data: {
        isDeleted: true,
        updateBy: userId
      }
    });
  }

  /**
   * 检查唯一性
   */
  private async checkUnique(
    tenantId: string,
    orgId: string,
    data: TaxrateDictionaryVersionCreateDto | TaxrateDictionaryVersionUpdateDto,
    id?: string
  ) {
    const where: Prisma.TaxrateDictionaryVersionWhereInput = {
      tenantId,
      orgId,
      name: data.name,
      isDeleted: false
    };

    // 检查名称是否重复, 排除自身
    if (id) {
      Object.assign(where, {
        id: { not: id }
      });
    }
    const duplicateRecord =
      await this.prisma.taxrateDictionaryVersion.findFirst({
        select: {
          id: true
        },
        where
      });

    if (duplicateRecord) {
      throw new BadRequestException('名称重复，请重新输入！');
    }
  }

  /**
   * 检查删除约束
   */
  private async checkDeleteConstraint(
    tenantId: string,
    orgId: string,
    id: string
  ) {
    // 启用、停用状态的版本不能删除
    const currentRecord = await this.prisma.taxrateDictionaryVersion.findUnique(
      {
        select: {
          status: true
        },
        where: {
          tenantId,
          orgId,
          id,
          isDeleted: false
        }
      }
    );
    if (currentRecord && currentRecord.status === EnableStatus.ENABLED) {
      throw new BadRequestException('启用状态的版本不可删除！');
    }
    if (currentRecord && currentRecord.status === EnableStatus.DISABLED) {
      throw new BadRequestException('停用状态的版本不可删除！');
    }

    const projectRef = (await this.getProjectRefInfo(tenantId, orgId, id)) as {
      projectRefCount: number;
    };
    if (projectRef.projectRefCount > 0) {
      throw new BadRequestException('被引用的版本不可删除！');
    }

    // 存在分类数据的版本不能删除
    const category = await this.prisma.taxrateDictionaryCategory.findFirst({
      select: {
        id: true
      },
      where: {
        tenantId,
        orgId,
        taxrateDictionaryVersionId: id,
        isDeleted: false
      }
    });
    if (category) {
      throw new BadRequestException('该版本下存在数据，不可删除！');
    }
  }

  // 获取项目引用数
  async getProjectRefInfo(
    tenantId: string,
    orgId: string,
    id?: string
  ): Promise<{
    projectRefCount: number;
  }> {
    // 项目引用数统计
    const projectRefCount =
      await this.prisma.accountTaxRateDictionaryVersion.count({
        where: {
          tenantId,
          isDeleted: false,
          versionId: id
        }
      });
    return { projectRefCount: projectRefCount || 0 };
  }

  // 获取所有的版本的项目引用数，按版本分组统计
  async getAllProjectRefInfo(tenantId: string) {
    // 项目引用数统计
    return await this.prisma.accountTaxRateDictionaryVersion.groupBy({
      where: {
        tenantId,
        isDeleted: false
      },
      _count: {
        _all: true
      },
      by: ['versionId']
    });
  }
}
