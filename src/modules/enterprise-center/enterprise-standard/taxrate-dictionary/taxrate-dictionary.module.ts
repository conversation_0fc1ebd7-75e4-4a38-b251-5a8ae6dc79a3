import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';

import { PlatformModule } from '../../../platform/platform.module';
import { TaxrateDictionaryCategoryController } from './taxrate-dictionary-category/taxrate-dictionary-category.controller';
import { TaxrateDictionaryCategoryService } from './taxrate-dictionary-category/taxrate-dictionary-category.service';
import { TaxrateDictionaryDetailController } from './taxrate-dictionary-detail/taxrate-dictionary-detail.controller';
import { TaxrateDictionaryDetailService } from './taxrate-dictionary-detail/taxrate-dictionary-detail.service';
import { TaxratelDictionaryVersionController } from './taxratel-dictionary-version/taxratel-dictionary-version.controller';
import { TaxratelDictionaryVersionService } from './taxratel-dictionary-version/taxratel-dictionary-version.service';

@Module({
  imports: [PrismaModule, PlatformModule],
  controllers: [
    TaxrateDictionaryCategoryController,
    TaxrateDictionaryDetailController,
    TaxratelDictionaryVersionController
  ],
  providers: [
    TaxrateDictionaryCategoryService,
    TaxrateDictionaryDetailService,
    TaxratelDictionaryVersionService
  ]
})
export class TaxrateDictionaryModule {}
