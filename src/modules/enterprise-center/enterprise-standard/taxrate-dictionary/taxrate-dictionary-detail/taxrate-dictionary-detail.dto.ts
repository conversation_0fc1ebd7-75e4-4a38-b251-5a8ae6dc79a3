import {
  ApiProperty,
  IntersectionType,
  PartialType,
  PickType
} from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Min
} from 'class-validator';

import { TaxrateType } from '@/prisma/generated';

class TaxrateDictionaryDetailBaseDto {
  @ApiProperty({ description: '版本id' })
  @IsNotEmpty({ message: '版本id不能为空' })
  @IsString({ message: '版本id必须是字符串' })
  taxrateDictionaryVersionId: string;

  @ApiProperty({ description: '分类id' })
  @IsNotEmpty({ message: '分类id不能为空' })
  @IsString({ message: '分类id必须是字符串' })
  taxrateDictionaryCategoryId: string;

  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '编码' })
  @IsNotEmpty({ message: '编码不能为空' })
  @IsString({ message: '编码必须是字符串' })
  code: string;

  @ApiProperty({ description: '名称' })
  @IsNotEmpty({ message: '名称不能为空' })
  @IsString({ message: '名称必须是字符串' })
  name: string;

  @ApiProperty({ description: '发票类型' })
  @IsNotEmpty({ message: '发票类型不能为空' })
  @IsString({ message: '发票类型必须是字符串' })
  @IsEnum(TaxrateType, { message: '发票类型必须是枚举值' })
  type: TaxrateType;

  @ApiProperty({ description: '税率' })
  @IsNotEmpty({ message: '税率不能为空' })
  @IsString({ message: '税率必须是字符串' })
  taxRate: string;

  @ApiProperty({ description: '排序', default: 1 })
  @IsOptional({ message: '排序可以为空' })
  @IsInt({ message: '排序必须是数字' })
  @Min(1, { message: '排序不能小于1' })
  sort: number = 1;

  @ApiProperty({ description: '备注' })
  @IsOptional({ message: '备注可以为空' })
  remark: string | null = null;

  @ApiProperty({ description: '是否启用', default: true })
  @IsOptional({ message: '是否启用可以为空' })
  @IsBoolean({ message: '是否启用必须是布尔值' })
  isActive: boolean = true;

  @ApiProperty({ description: '执行时间' })
  @IsOptional({ message: '执行时间可以为空' })
  @IsString({ message: '执行时间必须是字符串' })
  executeDate: string | null = null;
}

/** 创建数据Dto */
export class TaxrateDictionaryDetailCreateDto extends PickType(
  TaxrateDictionaryDetailBaseDto,
  [
    'taxrateDictionaryVersionId',
    'taxrateDictionaryCategoryId',
    'taxRate',
    'code',
    'name',
    'type',
    'remark',
    'executeDate'
  ] as const
) {}

/** 更新数据Dto */
export class TaxrateDictionaryDetailUpdateDto extends IntersectionType(
  PickType(TaxrateDictionaryDetailBaseDto, [
    'taxrateDictionaryVersionId',
    'taxrateDictionaryCategoryId'
  ] as const),
  PartialType(
    PickType(TaxrateDictionaryDetailBaseDto, [
      'taxrateDictionaryVersionId',
      'taxrateDictionaryCategoryId',
      'taxRate',
      'code',
      'name',
      'type',
      'remark',
      'isActive',
      'executeDate'
    ] as const)
  )
) {}

/** 查询结果Dto */
export class TaxrateDictionaryDetailResultDto extends TaxrateDictionaryDetailBaseDto {}

/** 查询列表接口query参数Dto */
export class TaxrateDictionaryDetailQueryParamDto {
  @ApiProperty({ description: '版本id' })
  @IsNotEmpty({ message: '版本id不能为空' })
  @IsString({ message: '版本id必须是字符串' })
  taxrateDictionaryVersionId: string;

  @ApiProperty({ description: '分类id' })
  @IsNotEmpty({ message: '分类id不能为空' })
  @IsString({ message: '分类id必须是字符串' })
  taxrateDictionaryCategoryId: string;

  @ApiProperty({ description: '名称' })
  @IsOptional({ message: '名称可以为空' })
  @IsString({ message: '名称必须是字符串' })
  name?: string;
}
