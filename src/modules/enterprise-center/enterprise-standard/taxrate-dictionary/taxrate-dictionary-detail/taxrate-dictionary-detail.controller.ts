import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { MoveToEnum } from '@/common/enums/common.enum';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  TaxrateDictionaryDetailCreateDto,
  TaxrateDictionaryDetailQueryParamDto,
  TaxrateDictionaryDetailResultDto,
  TaxrateDictionaryDetailUpdateDto
} from './taxrate-dictionary-detail.dto';
import { TaxrateDictionaryDetailService } from './taxrate-dictionary-detail.service';

@ApiTags('税率字典/明细')
@Controller('taxrate-dictionary-detail')
export class TaxrateDictionaryDetailController {
  constructor(private readonly service: TaxrateDictionaryDetailService) {}

  @ApiOperation({
    summary: '获取明细列表',
    description: '获取明细列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取明细列表成功',
    type: TaxrateDictionaryDetailResultDto,
    isArray: true
  })
  @Get()
  async getList(
    @Query() queryParam: TaxrateDictionaryDetailQueryParamDto,
    @ReqUser() reqUser: IReqUser
  ) {
    const list = await this.service.getList(reqUser, queryParam);
    return list;
  }

  @ApiOperation({
    summary: '创建明细',
    description: '创建明细'
  })
  @ApiResponse({
    status: 200,
    description: '创建明细成功',
    type: TaxrateDictionaryDetailResultDto
  })
  @Post()
  async createOne(
    @Body() data: TaxrateDictionaryDetailCreateDto,
    @ReqUser() reqUser: IReqUser
  ) {
    const detail = await this.service.createOne(reqUser, data);
    return detail;
  }

  @ApiOperation({
    summary: '更新明细',
    description: '更新明细'
  })
  @ApiResponse({
    status: 200,
    description: '更新明细成功',
    type: Boolean
  })
  @Patch(':id')
  async updateOne(
    @Param('id') id: string,
    @Body() data: TaxrateDictionaryDetailUpdateDto,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.updateOne(id, data, reqUser);
    return true;
  }

  @ApiOperation({
    summary: '删除明细',
    description: '删除明细'
  })
  @ApiResponse({
    status: 200,
    description: '更新明细成功',
    type: Boolean
  })
  @Delete(':id')
  async deleteOne(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    await this.service.deleteOne(id, reqUser);
    return true;
  }

  @ApiOperation({
    summary: '移动明细',
    description: '移动明细'
  })
  @ApiResponse({
    status: 200,
    description: '移动成功',
    type: Boolean
  })
  @Patch(':id/_move')
  async moveOne(
    @Param('id') id: string,
    @Query('moveTo') moveTo: MoveToEnum,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.moveOne(id, moveTo, reqUser);
    return true;
  }

  @ApiOperation({
    summary: '搜索变更记录',
    description: '搜索变更记录'
  })
  @ApiResponse({
    status: 200,
    description: '搜索变更记录成功'
  })
  @Get('record/:taxrateDictionaryDetailId')
  async changeRecord(
    @Param('taxrateDictionaryDetailId') taxrateDictionaryDetailId: string
  ) {
    return await this.service.getChangeRecord(taxrateDictionaryDetailId);
  }
}
