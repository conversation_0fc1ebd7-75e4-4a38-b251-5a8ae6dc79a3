import * as infraCloudSdk from '@ewing/infra-cloud-sdk';
import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable
} from '@nestjs/common';
import { isEmpty as _isEmpty } from 'lodash';

import { MoveToEnum } from '@/common/enums/common.enum';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { EnableStatus, TaxrateDictionaryDetail } from '@/prisma/generated';
import { TaxrateDictionaryDetailWhereInput } from '@/prisma/generated/models';

import {
  TaxrateDictionaryDetailCreateDto,
  TaxrateDictionaryDetailQueryParamDto,
  TaxrateDictionaryDetailResultDto,
  TaxrateDictionaryDetailUpdateDto
} from './taxrate-dictionary-detail.dto';

@Injectable()
export class TaxrateDictionaryDetailService {
  constructor(private readonly prisma: PrismaService) {}

  async getList(
    reqUser: IReqUser,
    queryParam: TaxrateDictionaryDetailQueryParamDto
  ) {
    const { tenantId, orgId } = reqUser;
    const { taxrateDictionaryCategoryId, taxrateDictionaryVersionId, name } =
      queryParam;

    const where: TaxrateDictionaryDetailWhereInput = {
      tenantId,
      orgId,
      taxrateDictionaryVersionId,
      isDeleted: false
    };
    // 查询所有明细时，不按分类进行过滤
    if (taxrateDictionaryCategoryId !== '100') {
      where.taxrateDictionaryCategory = {
        fullId: { contains: taxrateDictionaryCategoryId }
      };
    }
    // 按名称搜索时
    if (name) {
      where.name = { contains: name };
    }

    const categoryList = await this.prisma.taxrateDictionaryCategory.findMany({
      select: {
        id: true,
        parentId: true,
        level: true,
        sort: true,
        isActive: true
      },
      where: {
        tenantId,
        orgId,
        taxrateDictionaryVersionId,
        fullId:
          taxrateDictionaryCategoryId !== '100'
            ? { contains: taxrateDictionaryCategoryId }
            : undefined
      },
      orderBy: [{ isActive: 'desc' }, { sort: 'asc' }]
    });
    let detailList = await this.prisma.taxrateDictionaryDetail.findMany({
      select: {
        id: true,
        taxrateDictionaryVersion: true,
        taxrateDictionaryCategoryId: true,
        code: true,
        name: true,
        type: true,
        remark: true,
        isActive: true,
        sort: true,
        taxRate: true,
        executeDate: true,
        taxrateDictionaryCategory: {
          select: {
            isActive: true
          }
        }
      },
      where,
      orderBy: [{ isActive: 'desc' }, { sort: 'asc' }]
    });
    detailList = detailList.map((item) => ({
      ...item,
      parentId: item.taxrateDictionaryCategoryId
    }));

    const { parentRecords: rootNodes } = infraCloudSdk.initTree([
      ...categoryList,
      ...detailList
    ] as any);
    let list = infraCloudSdk
      .flattenTree(rootNodes, { traversal: 'depth' })
      .filter(
        (item) => item.isLeaf && item.taxrateDictionaryCategoryId != null
      );
    // 将“废弃”数据排在最后
    list = [
      ...list.filter((item) => item.isActive),
      ...list.filter((item) => !item.isActive)
    ];

    return list as any as TaxrateDictionaryDetailResultDto[];
  }

  async createOne(reqUser: IReqUser, data: TaxrateDictionaryDetailCreateDto) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 校验启用状态
    await this.checkVersionStatus(data.taxrateDictionaryVersionId);

    await this.checkUnique({ tenantId, orgId, data });

    const maxSort = await this.prisma.taxrateDictionaryDetail.aggregate({
      _max: {
        sort: true
      },
      where: {
        tenantId,
        orgId,
        taxrateDictionaryVersionId: data.taxrateDictionaryVersionId,
        taxrateDictionaryCategoryId: data.taxrateDictionaryCategoryId,
        isDeleted: false
      }
    });
    // 创建明细数据
    const detail = await this.prisma.taxrateDictionaryDetail.create({
      data: {
        ...data,
        executeDate: data.executeDate ? new Date(data.executeDate) : null,
        sort: maxSort._max.sort ? maxSort._max.sort + 1 : 1,
        tenantId,
        orgId,
        createBy: userId,
        updateBy: userId
      }
    });

    return detail;
  }

  // 查询对应的版本
  async checkVersionStatus(id: string) {
    const version = await this.prisma.taxrateDictionaryVersion.findFirst({
      select: {
        status: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
    if (version?.status === EnableStatus.ENABLED) {
      throw new HttpException(
        `版本已启用，无法进行操作`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async getOne(id: string) {
    return await this.prisma.taxrateDictionaryDetail.findUnique({
      select: {
        taxrateDictionaryVersionId: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
  }

  async updateOne(
    id: string,
    data: TaxrateDictionaryDetailUpdateDto,
    reqUser: IReqUser
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询明细的版本
    const detailOne = await this.getOne(id);

    if (!detailOne) {
      throw new HttpException(`明细信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(detailOne.taxrateDictionaryVersionId);

    await this.checkUnique({ tenantId, orgId, data, id });

    // 更新数据
    await this.prisma.taxrateDictionaryDetail.update({
      data: {
        ...data,
        executeDate: data.executeDate ? new Date(data.executeDate) : null,
        updateBy: userId
      },
      where: {
        id,
        tenantId,
        orgId,
        isDeleted: false
      }
    });
  }

  async deleteOne(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询明细的版本
    const detailOne = await this.getOne(id);

    if (!detailOne) {
      throw new HttpException(`明细信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(detailOne.taxrateDictionaryVersionId);

    // 检查明细是否被引用
    const projectRefCount = await this.getProjectRefInfo(tenantId, id);
    if (projectRefCount.projectRefCount > 0) {
      throw new BadRequestException('明细被引用，无法删除！');
    }

    // 逻辑删除
    await this.prisma.taxrateDictionaryDetail.update({
      data: {
        isDeleted: true,
        updateBy: userId
      },
      where: {
        id,
        tenantId,
        orgId,
        isDeleted: false
      }
    });
  }

  async moveOne(id: string, moveTo: MoveToEnum, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询明细的版本
    const detailOne = await this.getOne(id);

    if (!detailOne) {
      throw new HttpException(`明细信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(detailOne.taxrateDictionaryVersionId);

    const currentRecord = await this.prisma.taxrateDictionaryDetail.findFirst({
      select: {
        id: true,
        taxrateDictionaryCategoryId: true,
        taxrateDictionaryVersionId: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        id,
        isDeleted: false
      }
    });
    if (!currentRecord) {
      throw new BadRequestException('当前操作数据不存在！');
    }

    if (moveTo === MoveToEnum.UP) {
      await this.moveUp(tenantId, orgId, currentRecord);
    }
    if (moveTo === MoveToEnum.DOWN) {
      await this.moveDown(tenantId, orgId, currentRecord);
    }
  }

  /**
   * 检查唯一性
   */
  private async checkUnique(args: {
    tenantId: string;
    orgId: string;
    data: TaxrateDictionaryDetailCreateDto | TaxrateDictionaryDetailUpdateDto;
    id?: string;
  }) {
    const { tenantId, orgId, data, id = '' } = args;

    const duplicateCode: any[] = await this.prisma.$queryRaw`
      select
        tdd.code
        ,tdc.name as category_name
        ,tdc.code as category_code
      from taxrate_dictionary_detail tdd
      join taxrate_dictionary_category tdc
        on tdc.is_deleted = false
        and tdc.tenant_id = tdd.tenant_id
        and tdc.org_id = tdd.org_id
        and tdc.id = tdd.taxrate_dictionary_category_id
      where tdd.is_deleted = false
        and tdd.tenant_id = ${tenantId}
        and tdd.org_id = ${orgId}
        and tdd.taxrate_dictionary_version_id = ${data.taxrateDictionaryVersionId}
        and tdd.id <> ${id}
        and tdd.code = ${data.code}
    `;

    if (!_isEmpty(duplicateCode)) {
      const { categoryName, categoryCode, code } = duplicateCode[0];
      throw new BadRequestException(
        `税率字典分类[${categoryCode}-${categoryName}], 已存在编码[${code}]`
      );
    }

    const duplicateName: any[] = await this.prisma.$queryRaw`
      select
        tdd.name
        ,tdc.name as category_name
        ,tdc.code as category_code
      from taxrate_dictionary_detail tdd
      join taxrate_dictionary_category tdc
        on tdc.is_deleted = false
        and tdc.tenant_id = tdd.tenant_id
        and tdc.org_id = tdd.org_id
        and tdc.id = tdd.taxrate_dictionary_category_id
      where tdd.is_deleted = false
        and tdd.tenant_id = ${tenantId}
        and tdd.org_id = ${orgId}
        and tdd.taxrate_dictionary_version_id = ${data.taxrateDictionaryVersionId}
        and tdd.id <> ${id}
        and tdd.name = ${data.name}
    `;
    if (!_isEmpty(duplicateName)) {
      const { categoryName, categoryCode, name } = duplicateName[0];
      throw new BadRequestException(
        `机械字典分类[${categoryCode}-${categoryName}], 已存在名称=[${name}]`
      );
    }
  }

  /** 上移 */
  private async moveUp(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      taxrateDictionaryVersionId: string;
      taxrateDictionaryCategoryId: string;
      sort: number;
    }
  ) {
    const { taxrateDictionaryVersionId, taxrateDictionaryCategoryId } =
      currentRecord;

    // 找到上一条数据
    const prevRecord = await this.prisma.taxrateDictionaryDetail.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        taxrateDictionaryCategoryId,
        taxrateDictionaryVersionId,
        isDeleted: false,
        id: {
          not: currentRecord.id
        },
        sort: {
          lt: currentRecord.sort
        }
      },
      orderBy: {
        sort: 'desc'
      }
    });

    if (!prevRecord || prevRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.taxrateDictionaryDetail.update({
        where: {
          tenantId,
          orgId,
          taxrateDictionaryVersionId,
          id: currentRecord.id,
          isDeleted: false
        },
        data: {
          sort: prevRecord.sort
        }
      }),
      this.prisma.taxrateDictionaryDetail.update({
        where: {
          tenantId,
          orgId,
          taxrateDictionaryVersionId,
          id: prevRecord.id,
          isDeleted: false
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  /** 下移 */
  private async moveDown(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      taxrateDictionaryVersionId: string;
      taxrateDictionaryCategoryId: string;
      sort: number;
    }
  ) {
    const { taxrateDictionaryVersionId, taxrateDictionaryCategoryId } =
      currentRecord;

    // 找到下一条数据
    const nextRecord = await this.prisma.taxrateDictionaryDetail.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        taxrateDictionaryVersionId,
        taxrateDictionaryCategoryId,
        isDeleted: false,
        id: {
          not: currentRecord.id
        },
        sort: {
          gt: currentRecord.sort
        }
      },
      orderBy: {
        sort: 'asc'
      }
    });

    if (!nextRecord || nextRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.taxrateDictionaryDetail.update({
        where: {
          tenantId,
          orgId,
          taxrateDictionaryVersionId,
          id: currentRecord.id,
          isDeleted: false
        },
        data: {
          sort: nextRecord.sort
        }
      }),
      this.prisma.taxrateDictionaryDetail.update({
        where: {
          tenantId,
          orgId,
          taxrateDictionaryVersionId,
          id: nextRecord.id,
          isDeleted: false
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  /**
   * 变更记录校验
   * @param taxrateDictionaryVersionId
   * @param reqUser
   */
  async change(taxrateDictionaryVersionId: string, reqUser: IReqUser) {
    // 2、查询当前版本下启用新增的数据
    const addRecord = await this.prisma.$queryRaw<any[]>`
      select id, taxrate_dictionary_version_id, taxrate_dictionary_category_id, tax_rate, tenant_id, org_id 
      from taxrate_dictionary_detail 
      where is_deleted = false 
        and taxrate_dictionary_version_id = ${taxrateDictionaryVersionId} 
        and id not in (
          select id from taxrate_dictionary_enable_detail 
            where is_deleted = false 
            and taxrate_dictionary_version_id = ${taxrateDictionaryVersionId} 
        )
    `;
    // 3、获取启用删除的数据
    const deleteRecord = await this.prisma.$queryRaw<any[]>`
      select id, taxrate_dictionary_version_id, taxrate_dictionary_category_id, tax_rate 
      from taxrate_dictionary_enable_detail 
      where is_deleted = false 
        and taxrate_dictionary_version_id = ${taxrateDictionaryVersionId} 
        and id not in (
          select id from taxrate_dictionary_detail 
          where is_deleted = false 
          and taxrate_dictionary_version_id = ${taxrateDictionaryVersionId} 
        )
    `;
    // 4.获取同时存在的数据
    const updateRecord = await this.prisma.$queryRaw<any[]>`
      select current.id, current.taxrate_dictionary_version_id, 
        current.taxrate_dictionary_category_id, 
        current.tax_rate current_tax_rate, 
        enable.tax_rate enable_tax_rate,
        current.tenant_id, current.org_id 
      from taxrate_dictionary_detail current 
      join taxrate_dictionary_enable_detail enable 
      on enable.taxrate_dictionary_version_id = current.taxrate_dictionary_version_id
        and enable.id = current.id
        and enable.is_deleted = false
      where current.is_deleted = false 
        and current.taxrate_dictionary_version_id = ${taxrateDictionaryVersionId} 
        AND current.tax_rate != enable.tax_rate
    `;
    // 变更处理
    await this.changeRecord(addRecord, deleteRecord, updateRecord, reqUser);
  }

  async changeRecord(
    addRecord: TaxrateDictionaryDetail[],
    deleteRecord: TaxrateDictionaryDetail[],
    updateRecord: any[],
    reqUser: IReqUser
  ) {
    await this.prisma.$transaction(async (tx) => {
      // 新增启用记录明细
      await this.createEnableDetail(addRecord, reqUser, tx as PrismaService);
      // 删除启用记录明细
      await this.delEnableDetail(deleteRecord, reqUser, tx as PrismaService);
      // 修改启用记录明细
      await this.updateEnableDetail(updateRecord, reqUser, tx as PrismaService);
      // 新增修改记录
      await this.createRecord(updateRecord, reqUser, tx as PrismaService);
    });
  }

  async createRecord(
    updateRecord: any[],
    reqUser: IReqUser,
    tx: PrismaService
  ) {
    if (updateRecord.length) {
      await tx.taxrateDictionaryChangeRecord.createMany({
        data: updateRecord.map((item) => ({
          taxrateDictionaryDetailId: item.id,
          tenantId: item.tenantId,
          orgId: item.orgId,
          fieldName: '税率',
          oldValue: item.enableTaxRate,
          newValue: item.currentTaxRate,
          opreateUserId: reqUser.id,
          opreateUserName: reqUser.nickname,
          createBy: reqUser.id,
          updateBy: reqUser.id
        }))
      });
    }
  }

  async createEnableDetail(
    addRecord: TaxrateDictionaryDetail[],
    reqUser: IReqUser,
    tx: PrismaService
  ) {
    if (addRecord.length) {
      await tx.taxrateDictionaryEnableDetail.createMany({
        data: addRecord.map((item) => ({
          ...item,
          createBy: reqUser.id,
          updateBy: reqUser.id
        }))
      });
      // await Promise.all(
      //   addRecord.map(async (item: any) => {
      //     await tx.taxrateDictionaryDetail.update({
      //       where: {
      //         id: item.id,
      //         isDeleted: false
      //       },
      //       data: {
      //         updateBy: reqUser.id
      //       }
      //     });
      //   })
      // );
    }
  }

  async delEnableDetail(
    deleteRecord: TaxrateDictionaryDetail[],
    reqUser: IReqUser,
    tx: PrismaService
  ) {
    if (deleteRecord.length) {
      await Promise.all(
        deleteRecord.map(async (item: any) => {
          await tx.taxrateDictionaryEnableDetail.update({
            where: {
              id: item.id,
              isDeleted: false
            },
            data: {
              isDeleted: true,
              updateBy: reqUser.id
            }
          });
        })
      );
    }
  }

  async updateEnableDetail(
    updateRecord: any[],
    reqUser: IReqUser,
    tx: PrismaService
  ) {
    if (updateRecord.length) {
      await Promise.all(
        updateRecord.map(async (item: any) => {
          await tx.taxrateDictionaryEnableDetail.update({
            where: {
              id: item.id,
              isDeleted: false
            },
            data: {
              taxRate: item.currentTaxRate,
              updateBy: reqUser.id
            }
          });
        })
      );
      // await Promise.all(
      //   updateRecord.map(async (item: any) => {
      //     await tx.taxrateDictionaryDetail.update({
      //       where: {
      //         id: item.id,
      //         isDeleted: false
      //       },
      //       data: {
      //         updateBy: reqUser.id
      //       }
      //     });
      //   })
      // );
    }
  }

  async getChangeRecord(taxrateDictionaryDetailId: string) {
    return await this.prisma.taxrateDictionaryChangeRecord.findMany({
      where: {
        taxrateDictionaryDetailId,
        isDeleted: false
      },
      orderBy: {
        createAt: 'desc'
      }
    });
  }

  // 获取明细项目引用数
  async getProjectRefInfo(
    tenantId: string,
    id?: string
  ): Promise<{
    projectRefCount: number;
  }> {
    // 项目引用数统计
    const projectRefCount =
      await this.prisma.accountTaxRateDictionaryDetail.count({
        where: {
          tenantId,
          isDeleted: false,
          detailId: id
        }
      });
    return { projectRefCount: projectRefCount || 0 };
  }
}
