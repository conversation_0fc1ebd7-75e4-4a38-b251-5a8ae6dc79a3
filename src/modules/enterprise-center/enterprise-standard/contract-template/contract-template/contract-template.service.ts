import { BadRequestException, Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { FileManageService } from '@/modules/file-manage/file-manage.service';
import {
  ContractTemplate,
  ContractTemplateClassifyType,
  VersionStatus
} from '@/prisma/generated';

import {
  CreateTemplateDto,
  EditStatusDto,
  UpdateTemplateDto
} from './contract-template.dto';

@Injectable()
export class ContractTemplateService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly fileManage: FileManageService
  ) {}

  async getName(
    name: string,
    orgId: string,
    reqUser: IReqUser,
    type: 'add' | 'edit',
    id: string = ''
  ) {
    const { tenantId } = reqUser;
    if (type === 'add') {
      return await this.prisma.contractTemplate.findFirst({
        where: {
          name,
          orgId,
          tenantId,
          isDeleted: false
        }
      });
    } else {
      return await this.prisma.contractTemplate.findFirst({
        where: {
          name: name,
          orgId,
          tenantId,
          isDeleted: false,
          id: {
            not: id
          }
        }
      });
    }
  }

  // 文件名存在则加（数字）
  async getNameNumber(name: string, reqUser: IReqUser) {
    const { baseName } = this.parseFileName(name);

    // 查询数据库中所有匹配基础名称的文件
    const existingFiles = await this.prisma.contractTemplate.findMany({
      where: {
        name: {
          startsWith: baseName
        },
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      },
      select: {
        name: true
      }
    });
    // 生成唯一名称
    const uniqueName = this.generateUniqueName(existingFiles, name);

    return uniqueName;
  }

  // 解析文件名，提取基础名称和后缀编号
  parseFileName(name: string) {
    const regex = /^(.*?)\s*\((\d+)\)$/;
    const match = name.match(regex);

    if (match) {
      return {
        baseName: match[1],
        number: parseInt(match[2], 10)
      };
    }

    return {
      baseName: name,
      number: null
    };
  }

  // 生成唯一名称
  generateUniqueName(existingNames: any[], originalName: string) {
    const { baseName, number } = this.parseFileName(originalName);

    // 提取所有已存在的编号
    const existingNumbers = existingNames
      .map((name) => {
        const match = name.name.match(/\((\d+)\)$/);
        return match ? parseInt(match[1], 10) : null;
      })
      .filter((n) => n !== null);

    // 如果原始名称没有编号，从 1 开始
    if (number === null) {
      const maxNumber = Math.max(0, ...existingNumbers);
      return existingNames.some((f) => f.name === originalName)
        ? `${baseName} (${maxNumber + 1})`
        : originalName;
    }

    // 如果原始名称有编号，找到不冲突的最小大于编号
    const nextNumber = this.findNextAvailableNumber(existingNumbers, number);
    return `${baseName}(${nextNumber})`;
  }

  // 找到大于等于 startNumber 的最小可用编号
  findNextAvailableNumber(existingNumbers: any[], startNumber: number) {
    let num = startNumber;
    while (existingNumbers.includes(num)) {
      num++;
    }
    return num;
  }

  async add(data: CreateTemplateDto, reqUser: IReqUser) {
    const { tenantId, id, orgId } = reqUser;
    const { classify } = data;
    // 查询当前租户组织下分类下的最大sort
    const maxSort = await this.prisma.contractTemplate.aggregate({
      where: {
        classify: classify as ContractTemplateClassifyType,
        orgId,
        tenantId,
        isDeleted: false
      },
      _max: {
        sort: true
      }
    });
    return await this.prisma.contractTemplate.create({
      data: {
        ...data,
        orgId,
        classify: classify as ContractTemplateClassifyType,
        tenantId: tenantId,
        compileBy: reqUser.nickname,
        sort: maxSort._max.sort ? maxSort._max.sort + 1 : 1,
        createBy: id,
        updateBy: id
      }
    });
  }

  async getList(reqUser: IReqUser) {
    return await this.prisma.$queryRaw<any[]>`
      with tmp_classify as (
            select 
              '1' as id, '分包-劳务' as name, 1 as sort, null as parent_id, 1 as level,
              'SUBPACKAGE_LABOUR_SERVICE' as classify, null as remark, 
              LOCALTIMESTAMP as compile_date, null as version_status, 
              0 as refer_nums, null as file_content_type, null as compile_by,
              null as file_key, null as file_name, null as file_size,
              null as is_matching, null as is_mandatory,
              LOCALTIMESTAMP as create_at, LOCALTIMESTAMP as update_at
              
            union all
            
            select 
              '2' as id, '分包-专业' as name, 2 as sort, null as parent_id, 1 as level,
              'SUBPACKAGE_LABOUR_SPECIALTY' as classify, null as remark, 
              LOCALTIMESTAMP as compile_date, null as version_status, 
              0 as refer_nums, null as file_content_type, null as compile_by,
              null as file_key, null as file_name, null as file_size,
              null as is_matching, null as is_mandatory,
              LOCALTIMESTAMP as create_at, LOCALTIMESTAMP as update_at

            union all
            
            select 
              '3' as id, '材料-物资采购' as name, 3 as sort, null as parent_id, 1 as level, 
              'MATERIALS_PURCHASING' as classify, null as remark, 
              LOCALTIMESTAMP as compile_date, null as version_status, 
              0 as refer_nums, null as file_content_type, null as compile_by,
              null as file_key, null as file_name, null as file_size,
              null as is_matching, null as is_mandatory,
              LOCALTIMESTAMP as create_at, LOCALTIMESTAMP as update_at
              
            union all
            
            select 
              '4' as id, '材料-商品混凝土' as name, 4 as sort, null as parent_id, 1 as level, 
              'MATERIALS_COMMERCIAL_CONCRETE' as classify, null as remark, 
              LOCALTIMESTAMP as compile_date, null as version_status, 
              0 as refer_nums, null as file_content_type, null as compile_by,
              null as file_key, null as file_name, null as file_size,
              null as is_matching, null as is_mandatory,
              LOCALTIMESTAMP as create_at, LOCALTIMESTAMP as update_at
              
            union all
            
            select 
              '5' as id, '材料-租赁周转材料' as name, 5 as sort, null as parent_id, 1 as level, 
              'MATERIALS_LEASING_TURNOVER' as classify, null as remark, 
              LOCALTIMESTAMP as compile_date, null as version_status, 
              0 as refer_nums, null as file_content_type, null as compile_by,
              null as file_key, null as file_name, null as file_size,
              null as is_matching, null as is_mandatory,
              LOCALTIMESTAMP as create_at, LOCALTIMESTAMP as update_at
              
            union all
            
            select 
              '6' as id, '机械' as name, 6 as sort, null as parent_id, 1 as level, 
              'MACHINERY' as classify, null as remark, LOCALTIMESTAMP as compile_date, 
              null as version_status, 0 as refer_nums, null as file_content_type, null as compile_by,
              null as file_key, null as file_name, null as file_size,
              null as is_matching, null as is_mandatory,
              LOCALTIMESTAMP as create_at, LOCALTIMESTAMP as update_at
              
            union all
            
            select 
              '7' as id, '其他' as name, 7 as sort, null as parent_id, 1 as level, 
              'OTHERS' as classify, null as remark, LOCALTIMESTAMP as compile_date, 
              null as version_status, 0 as refer_nums, null as file_content_type, null as compile_by,
              null as file_key, null as file_name, null as file_size,
              null as is_matching, null as is_mandatory,
              LOCALTIMESTAMP as create_at, LOCALTIMESTAMP as update_at
          ), tmp_template as (
          SELECT
              esct.id,
              esct.name,
              esct.sort,
              tc.id AS parent_id, 
              2 as level,
              esct.classify::TEXT AS classify,
              esct.remark,
							esct.compile_date,
              esct.version_status::TEXT AS version_status,
              COALESCE(ref_counts.ref_count, 0) AS refer_nums,
              esct.file_content_type,
              esct.compile_by,
              esct.file_key,
              esct.file_name,
              esct.file_size,
              CASE 
                  -- 需要匹配的规则总数为0时，直接返回“未匹配”
                  WHEN required_matching_rules.total = 0 THEN '未匹配'
                  -- 已匹配数量 = 需要匹配的总数时，返回“完全匹配”
                  WHEN matched_rules.matched_count = required_matching_rules.total THEN '完全匹配'
                  -- 已匹配数量 > 0 但 < 总数时，返回“部分匹配”
                  WHEN matched_rules.matched_count > 0 THEN '部分匹配'
                  -- 否则返回“未匹配”
                  ELSE '未匹配'
              END AS is_matching,
              CASE 
                  WHEN EXISTS (
                      SELECT 1 
                      FROM mandatory_term esmt 
                      WHERE esmt.contract_template_id = esct.id and esmt.is_deleted = false
                  ) THEN '有' 
                  ELSE '无' 
              END AS is_mandatory,
              esct.create_at,
              esct.update_at
          FROM contract_template esct
          JOIN tmp_classify tc ON esct.classify::TEXT = tc.classify
          -- 统计每个模板的引用次数
          LEFT JOIN (
            SELECT 
              contract_template_id,
              COUNT(*)::Int AS ref_count  -- 统计每个模板的引用次数
            FROM material_contract  -- 替换为实际引用合同模板的表名
            WHERE 
              is_deleted = false
              AND tenant_id = ${reqUser.tenantId}
            GROUP BY contract_template_id
          ) ref_counts ON ref_counts.contract_template_id = esct.id
          LEFT JOIN (
              SELECT 
                  COUNT(id) AS total
              FROM field_rule
              WHERE is_matching = true
              and is_deleted = false
          ) required_matching_rules on 1=1
          LEFT JOIN (
              SELECT 
                  esct.id AS template_id,
                  COUNT(esfr.id) AS matched_count
              FROM contract_template esct
              JOIN field_rule esfr 
                  ON esfr.is_matching = true
                  and esfr.is_deleted = false 
              JOIN contract_template_field_rule ctfr 
                  ON ctfr.contract_template_id = esct.id
                  AND ctfr.field_rule_id = esfr.id
                  and ctfr.is_deleted = false 
              GROUP BY esct.id
          ) matched_rules ON matched_rules.template_id = esct.id
          WHERE
              esct.tenant_id = ${reqUser.tenantId}
              AND esct.is_deleted = false 
              AND esct.org_id = ${reqUser.orgId}
          ORDER BY esct.sort, esct.create_at DESC
      )
      SELECT *
      FROM (
          SELECT * FROM tmp_classify
          UNION ALL
          SELECT * FROM tmp_template
      ) AS t
      ORDER BY level, sort, name;
    `;
  }

  async getObj(id: string) {
    return await this.prisma.contractTemplate.findFirst({
      where: {
        id,
        isDeleted: false
      }
    });
  }

  async edit(id: string, data: UpdateTemplateDto, reqUser: IReqUser) {
    return await this.prisma.contractTemplate.update({
      where: {
        id,
        isDeleted: false
      },
      data: {
        ...data,
        updateBy: reqUser.id
      }
    });
  }

  // 查询范本的引用数量
  async getReferNums(id: string) {
    return await this.prisma.materialContract.count({
      where: {
        contractTemplateId: id,
        isDeleted: false
      }
    });
  }

  async editStatus(
    data: EditStatusDto,
    userId: string,
    template: ContractTemplate
  ) {
    const { id, versionStatus } = data;
    const status = versionStatus as VersionStatus;
    let newStatus;
    // 查询范本的引用数量
    const referNums = await this.getReferNums(id);
    if (status === VersionStatus.UNPUBLISHED && referNums) {
      // 未发布且引用数大于0，改为已停用
      newStatus = VersionStatus.NOUSEING;
    } else {
      newStatus = status;
    }
    if (status === VersionStatus.PUBLISHED) {
      // 校验模版下的必填字段是否都选了
      await this.checkTemplateField(id);
    }
    return await this.prisma.contractTemplate.update({
      where: {
        id,
        isDeleted: false
      },
      data: {
        versionStatus: newStatus,
        updateBy: userId
      }
    });
  }

  async checkTemplateField(id: string) {
    // 查询当前模版
    const template = await this.prisma.contractTemplate.findUnique({
      select: {
        classify: true
      },
      where: {
        id
      }
    });
    if (!template) {
      throw new BadRequestException('模版不存在');
    }
    const classify = [
      'GENERAL' as ContractTemplateClassifyType,
      template.classify
    ];
    const ruleList = await this.prisma.fieldRule.findMany({
      select: {
        id: true
      },
      where: {
        isRequired: true,
        isDeleted: false,
        type: {
          in: classify
        }
      }
    });
    const templateFieldList =
      await this.prisma.contractTemplateFieldRule.findMany({
        select: {
          fieldRuleId: true
        },
        where: {
          contractTemplateId: id,
          isDeleted: false
        }
      });
    ruleList.map((item) => {
      const obj = templateFieldList.find(
        (templateItem) => item.id === templateItem.fieldRuleId
      );
      if (!obj) {
        throw new BadRequestException('缺少必填字段');
      }
    });
  }

  // 上移
  async up(id: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    console.log('上移', id);
    await this.prisma.$transaction(async (tx) => {
      // 查询当前模版
      const template = await tx.contractTemplate.findUnique({
        where: {
          id,
          isDeleted: false
        }
      });
      console.log('查询当前模版', template);
      if (template) {
        // 查询要下移的模版的id
        const templateDown = await tx.contractTemplate.findFirst({
          where: {
            sort: {
              lt: template.sort
            },
            tenantId,
            orgId,
            isDeleted: false,
            classify: template.classify
          },
          orderBy: {
            sort: 'desc'
          }
        });
        console.log('查询要下移的模版的id', templateDown);
        if (!templateDown?.id) {
          throw new BadRequestException('无法上移');
        }
        console.log('互换', templateDown);
        // 当前排序修改
        await tx.contractTemplate.update({
          where: {
            id,
            isDeleted: false
          },
          data: {
            sort: templateDown.sort,
            updateBy: reqUser.id
          }
        });
        // 上一模版修改
        await tx.contractTemplate.update({
          where: {
            id: templateDown.id,
            isDeleted: false
          },
          data: {
            sort: template.sort,
            updateBy: reqUser.id
          }
        });
      }
    });
  }

  // 下移
  async down(id: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    await this.prisma.$transaction(async (tx) => {
      // 查询当前模版
      const template = await tx.contractTemplate.findUnique({
        where: {
          id,
          isDeleted: false
        }
      });
      if (template) {
        // 查询要上移的模版的id
        const templateDown = await tx.contractTemplate.findFirst({
          where: {
            sort: {
              gt: template.sort
            },
            tenantId,
            orgId,
            isDeleted: false,
            classify: template.classify
          },
          orderBy: {
            sort: 'asc'
          }
        });
        if (!templateDown?.id) {
          throw new BadRequestException('无法下移');
        }
        // 当前模版
        await tx.contractTemplate.update({
          where: {
            id,
            isDeleted: false
          },
          data: {
            sort: templateDown.sort,
            updateBy: reqUser.id
          }
        });
        // 互换的模版
        await tx.contractTemplate.update({
          where: {
            id: templateDown.id,
            isDeleted: false
          },
          data: {
            sort: template.sort,
            updateBy: reqUser.id
          }
        });
      }
    });
  }

  async del(id: string, userId: string) {
    const template = await this.getObj(id);
    if (template && template.versionStatus !== VersionStatus.UNPUBLISHED) {
      throw new BadRequestException('只有未发布的范本可以删除');
    }
    return await this.prisma.contractTemplate.update({
      where: {
        id,
        isDeleted: false
      },
      data: {
        isDeleted: true,
        updateBy: userId
      }
    });
  }
}
