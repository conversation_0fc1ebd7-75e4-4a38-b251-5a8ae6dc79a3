import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateMandatoryTermDto {
  @ApiProperty({ description: '强制条款名称', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '强制条款名称' })
  @IsString({ message: '强制条款名称必须为字符串' })
  name: string;

  @ApiProperty({ description: '强制条款内容', default: 'xxxxxxx' })
  @IsNotEmpty({ message: '强制条款内容不能为空' })
  @IsString({ message: '强制条款内容必须为字符串' })
  content: string;

  @ApiProperty({ description: '合同范本id', default: '1' })
  @IsNotEmpty({ message: '合同范本id不能为空' })
  @IsString({ message: '合同范本id必须为字符串' })
  contractTemplateId: string;
}

export class UpdateMandatoryTermDto {
  @ApiProperty({ description: '强制条款名称', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '强制条款名称' })
  @IsString({ message: '强制条款名称必须为字符串' })
  name: string;

  @ApiProperty({ description: '强制条款内容', default: 'xxxxxxx' })
  @IsNotEmpty({ message: '强制条款内容不能为空' })
  @IsString({ message: '强制条款内容必须为字符串' })
  content: string;
}

export class QueryMandatoryTermDto {
  @ApiProperty({ description: '合同范本id', default: '1' })
  @IsNotEmpty({ message: '合同范本id不能为空' })
  @IsString({ message: '合同范本id必须为字符串' })
  contractTemplateId: string;
}
