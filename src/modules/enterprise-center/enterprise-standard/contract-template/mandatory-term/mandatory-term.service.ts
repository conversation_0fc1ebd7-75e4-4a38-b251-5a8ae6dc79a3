import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import {
  CreateMandatoryTermDto,
  UpdateMandatoryTermDto
} from './mandatory-term.dto';

@Injectable()
export class MandatoryTermService {
  constructor(private readonly prisma: PrismaService) {}

  async add(data: CreateMandatoryTermDto, reqUser: IReqUser) {
    return await this.prisma.mandatoryTerm.create({
      data: {
        ...data,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }
    });
  }

  async edit(id: string, data: UpdateMandatoryTermDto, reqUser: IReqUser) {
    return await this.prisma.mandatoryTerm.update({
      where: {
        id,
        isDeleted: false
      },
      data: {
        ...data,
        updateBy: reqUser.id
      }
    });
  }

  async del(id: string, reqUser: IReqUser) {
    return await this.prisma.mandatoryTerm.update({
      where: {
        id,
        isDeleted: false
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }

  async getList(contractTemplateId: string) {
    return await this.prisma.mandatoryTerm.findMany({
      where: {
        contractTemplateId,
        isDeleted: false
      },
      orderBy: [
        {
          createAt: 'desc'
        },
        {
          name: 'asc'
        }
      ]
    });
  }
}
