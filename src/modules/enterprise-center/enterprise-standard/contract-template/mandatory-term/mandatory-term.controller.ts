import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  CreateMandatoryTermDto,
  QueryMandatoryTermDto,
  UpdateMandatoryTermDto
} from './mandatory-term.dto';
import { MandatoryTermService } from './mandatory-term.service';

@ApiTags('企业标准-强制条款')
@Controller('enterprise-standard-mandatory-term')
export class MandatoryTermController {
  constructor(private readonly service: MandatoryTermService) {}

  @ApiOperation({
    summary: '查询强制条款',
    description: '查询强制条款'
  })
  @ApiResponse({ status: 200, description: '查询强制条款成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/list')
  async list(@Query() { contractTemplateId }: QueryMandatoryTermDto) {
    return await this.service.getList(contractTemplateId);
  }

  @ApiOperation({
    summary: '新增强制条款',
    description: '新增强制条款'
  })
  @ApiResponse({ status: 200, description: '新增强制条款成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/add')
  async create(
    @ReqUser() reqUser: IReqUser,
    @Body() data: CreateMandatoryTermDto
  ) {
    return await this.service.add(data, reqUser);
  }

  @ApiOperation({
    summary: '删除强制条款',
    description: '删除强制条款'
  })
  @ApiResponse({ status: 200, description: '删除强制条款成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Delete('/del/:id')
  async del(@ReqUser() reqUser: IReqUser, @Param('id') id: string) {
    return await this.service.del(id, reqUser);
  }

  @ApiOperation({
    summary: '修改强制条款',
    description: '修改强制条款'
  })
  @ApiResponse({ status: 200, description: '修改强制条款成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Patch('/edit/:id')
  async edit(
    @Param('id') id: string,
    @Body() body: UpdateMandatoryTermDto,
    @ReqUser() reqUser: IReqUser
  ) {
    return await this.service.edit(id, body, reqUser);
  }
}
