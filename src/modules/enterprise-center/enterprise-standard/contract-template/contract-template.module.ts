import { <PERSON>du<PERSON> } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';
import { FileManageService } from '@/modules/file-manage/file-manage.service';

import { ContractTemplateController } from './contract-template/contract-template.controller';
import { ContractTemplateService } from './contract-template/contract-template.service';
import { FieldRuleController } from './field-rule/field-rule.controller';
import { FieldRuleService } from './field-rule/field-rule.service';
import { MandatoryTermController } from './mandatory-term/mandatory-term.controller';
import { MandatoryTermService } from './mandatory-term/mandatory-term.service';

@Module({
  imports: [PrismaModule],
  controllers: [
    ContractTemplateController,
    FieldRuleController,
    MandatoryTermController
  ],
  providers: [
    ContractTemplateService,
    FileManageService,
    FieldRuleService,
    MandatoryTermService
  ]
})
export class ContractTemplateModule {}
