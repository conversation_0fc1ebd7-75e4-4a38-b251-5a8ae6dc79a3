import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  CreateFieldRuleDto,
  DelFieldRuleDto,
  QueryFieldRuleDto
} from './field-rule.dto';
import { FieldRuleService } from './field-rule.service';

@ApiTags('字段规则模块')
@Controller('enterprise-standard-field-rule')
export class FieldRuleController {
  constructor(private readonly service: FieldRuleService) {}

  @ApiOperation({
    summary: '查询所有字段规则',
    description: '查询所有字段规则'
  })
  @ApiResponse({ status: 200, description: '获取某个授权单下的授权单详情成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/list')
  async getList(
    @Query() query: QueryFieldRuleDto,
    @ReqUser() reqUser: IReqUser
  ) {
    const { contractTemplateId } = query;
    // 先查询当前模版是否有数据
    // const hasData = await this.service.hasData(contractTemplateId);
    // if (hasData) {
    //   // 查询本身已存在的字段规则
    return await this.service.getcurrentList(query, reqUser);
    // } else {
    //   // 获取所有字段规则
    //   return await this.service.getAllList(query, reqUser);
    // }
  }

  @ApiOperation({
    summary: '合同范本新增字段规则',
    description: '合同范本新增字段规则'
  })
  @ApiResponse({ status: 200, description: '合同范本新增字段规则成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/add')
  async add(@Body() body: CreateFieldRuleDto, @ReqUser() reqUser: IReqUser) {
    return await this.service.add(body, reqUser);
  }

  @ApiOperation({
    summary: '合同范本取消字段规则',
    description: '合同范本取消字段规则'
  })
  @ApiResponse({ status: 200, description: '合同范本取消字段规则成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/del')
  async del(@Body() body: DelFieldRuleDto, @ReqUser() reqUser: IReqUser) {
    return await this.service.del(body, reqUser);
  }
}
