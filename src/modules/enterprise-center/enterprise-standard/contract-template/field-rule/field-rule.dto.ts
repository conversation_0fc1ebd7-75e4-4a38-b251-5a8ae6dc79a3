import { ApiProperty, PickType } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class QueryFieldRuleDto {
  @ApiProperty({ description: '字段名称', default: 'xxxxxx' })
  @IsOptional()
  @IsString({ message: '字段名称必须为字符串' })
  name?: string;

  @ApiProperty({ description: '合同范本id', default: 'xxxxxx' })
  @IsNotEmpty({ message: '合同范本id不能为空' })
  @IsString({ message: '合同范本id必须为字符串' })
  contractTemplateId: string;
}

export class CreateFieldRuleDto {
  @ApiProperty({ description: '坐标', default: '1' })
  @IsNotEmpty({ message: '坐标不能为空' })
  @IsString({ message: '坐标必须为字符串' })
  coord: string;

  @ApiProperty({ description: '字段规则id', default: '1' })
  @IsNotEmpty({ message: '字段规则id不能为空' })
  @IsString({ message: '字段规则id必须为字符串' })
  fieldRuleId: string;

  @ApiProperty({ description: '合同范本id', default: '1' })
  @IsNotEmpty({ message: '合同范本id不能为空' })
  @IsString({ message: '合同范本id必须为字符串' })
  contractTemplateId: string;

  @ApiProperty({ description: '是否必填', default: true })
  @IsNotEmpty({ message: '是否必填不能为空' })
  @IsBoolean({ message: '是否必填必须为字符串' })
  isRequired: boolean;
}

export class DelFieldRuleDto extends PickType(CreateFieldRuleDto, [
  'fieldRuleId',
  'contractTemplateId',
  'coord'
] as const) {}

// export class DelFieldRuleDto extends CreateFieldRuleDto {}
