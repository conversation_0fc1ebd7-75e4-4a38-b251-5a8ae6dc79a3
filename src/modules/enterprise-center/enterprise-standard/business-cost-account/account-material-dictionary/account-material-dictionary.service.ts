import { BadRequestException, Injectable } from '@nestjs/common';
import { create, isEmpty } from 'lodash';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { PlatformService } from '@/modules/platform/platform.service';
import { EnableStatus, Prisma } from '@/prisma/generated';

import {
  CreateBusinessCostAccountDto,
  DeleteDictionaryDetailType,
  DictionaryCategoryType,
  DictionaryDetailType,
  DictionaryType,
  EditBusinessCostSubjectDto
} from '../business-cost-account.dto';
import { BusinessCostAccountService } from '../business-cost-account.service';

@Injectable()
export class AccountMaterialDictionaryService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly service: BusinessCostAccountService,
    private readonly platformService: PlatformService
  ) {}

  async getVersionList(req: Request, reqUser: IReqUser, data: DictionaryType) {
    const { accountDictionaryId } = data;
    // 查询当前公司的最上级组织
    const topOrg = await this.platformService.getOrgParentIds(
      req,
      reqUser.tenantId,
      reqUser.orgId
    );
    let result: any[] = await this.prisma.$queryRaw<any[]>`
        SELECT 
          bcsv.id,
          bcsv.tenant_id,
          bcsv.org_id,
          bcsv.business_cost_subject_version_id,
          bcsv.name,
          o.name as org_name,
          bcsv.status,
          EXISTS (
            SELECT 1 
            FROM account_material_dictionary_version adv
            WHERE adv.tenant_id = bcsv.tenant_id
              AND adv.org_id = ${reqUser.orgId}
              AND adv.version_id = bcsv.id
              AND adv.account_dictionary_id = ${accountDictionaryId}
              AND adv.is_deleted = false
          ) AS is_use
        FROM material_dictionary_version bcsv
        JOIN platform_meta.org AS o
          ON o.tenant_id = bcsv.tenant_id
          AND o.id = bcsv.org_id
          AND o.is_deleted = false
        WHERE bcsv.is_deleted = false 
          AND bcsv.status != 'NOT_ENABLED' 
          AND bcsv.tenant_id = ${reqUser.tenantId}
          AND bcsv.org_id = ANY(${topOrg}::text[])
        ORDER BY bcsv.create_by DESC
      `;

    // 不是已启用并且没有被项目层选中的版本不展示
    result = result.filter(
      (item) => item.isUse || item.status === EnableStatus.ENABLED
    );

    return result;
  }

  async getCategoryList(
    req: Request,
    reqUser: IReqUser,
    data: DictionaryCategoryType
  ) {
    const { versionId, accountDictionaryId } = data;
    // 查询当前公司的最上级组织
    const topOrg = await this.platformService.getOrgParentIds(
      req,
      reqUser.tenantId,
      reqUser.orgId
    );
    return await this.prisma.$queryRaw<any[]>`
       SELECT
        bcsv.id,
        bcsv.tenant_id,
        bcsv.org_id,
        bcsv.name,
        bcsv.code,
        bcsv.type,
        bcsv.parent_id,
        bcsv.remark,
        bcsv.material_dictionary_version_id as version_id,
        EXISTS (
          SELECT 1
          FROM account_material_dictionary_category adv
          WHERE adv.tenant_id = bcsv.tenant_id
            AND adv.org_id = ${reqUser.orgId}
            AND adv.account_dictionary_id = ${accountDictionaryId}
            AND adv.version_id = bcsv.material_dictionary_version_id
            AND adv.category_id = bcsv.id
            AND adv.is_deleted = false
        ) AS is_use
      FROM material_dictionary_category bcsv
      WHERE bcsv.is_deleted = false
        AND bcsv.tenant_id = ${reqUser.tenantId}
        AND bcsv.org_id = ANY(${topOrg}::text[])
        AND bcsv.material_dictionary_version_id = ${versionId}
      ORDER BY bcsv."level", bcsv.sort
      `;
  }

  async getDetailList(reqUser: IReqUser, data: DictionaryDetailType) {
    const { versionId, categoryId } = data;

    const categories = await this.prisma.$queryRaw<any[]>`
      select
        id
      from material_dictionary_category
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and position(${categoryId} in full_id) > 0
        and is_leaf = true
    `;
    const categoryIds = categories.map((item) => item.id);
    if (isEmpty(categoryIds) && versionId !== categoryId) return [];

    const res = await this.prisma.$queryRaw<any[]>`
      SELECT
        bcsv.*,
        COALESCE(STRING_AGG(mdbcsd.business_cost_subject_detail_id, ','), '') AS business_cost_subject_detail_ids
      FROM (
      SELECT
        bcsv.id,
        bcsv.tenant_id,
        bcsv.org_id,
        bcsv.name,
        bcsv.material_dictionary_version_id as version_id,
        bcsv.material_dictionary_category_id as category_id,
        bcsv.code,
        bcsv.specification_model,
        bcsv.metering_unit,
        bcsv.type,
        bcsv.remark,
        bcsv.account_explanation
      FROM material_dictionary_detail bcsv
      WHERE bcsv.is_deleted = false
        AND bcsv.tenant_id = ${reqUser.tenantId}
        AND bcsv.material_dictionary_version_id = ${versionId}
        ${
          categoryId === versionId
            ? Prisma.empty
            : Prisma.sql`and bcsv.material_dictionary_category_id in (${Prisma.join(categoryIds)})`
        }
        AND bcsv.is_active = true
      ORDER BY bcsv.material_dictionary_category_id, bcsv.sort
      ) bcsv
      LEFT JOIN material_detail_business_cost_subject_detail mdbcsd
        ON mdbcsd.material_dictionary_detail_id = bcsv.id
        AND mdbcsd.tenant_id = bcsv.tenant_id
        AND mdbcsd.org_id = bcsv.org_id
        AND mdbcsd.is_deleted = false
      GROUP BY
        bcsv.id,
        bcsv.tenant_id,
        bcsv.org_id,
        bcsv.name,
        bcsv.version_id,
        bcsv.category_id,
        bcsv.code,
        bcsv.specification_model,
        bcsv.metering_unit,
        bcsv.type,
        bcsv.remark,
        bcsv.account_explanation
    `;
    return res.map((item) => {
      return {
        ...item,
        businessCostSubjectDetailIds:
          item.businessCostSubjectDetailIds.split(',')
      };
    });
  }

  async add(reqUser: IReqUser, data: CreateBusinessCostAccountDto) {
    const { accountDictionaryId, versionId } = data;
    // 获取当前项目启用的业务成本科目版本
    const businessSubjectVersion =
      await this.prisma.accountBusinessCostSubjectVersion.findFirst({
        where: {
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        },
        select: {
          versionId: true
        }
      });

    await this.prisma.$transaction(async (tx) => {
      // 新增版本
      await this.createVersion(
        tx as PrismaService,
        reqUser,
        accountDictionaryId,
        versionId
      );
      // 新增分类
      await this.createCategory(
        tx as PrismaService,
        reqUser,
        accountDictionaryId,
        versionId
      );
      // 新增明细
      await this.createDetail(
        tx as PrismaService,
        reqUser,
        accountDictionaryId,
        versionId
      );
      // 新增业务成本科目关联关系
      await this.createSubjectDetailRelation(
        tx as PrismaService,
        reqUser,
        accountDictionaryId,
        versionId,
        businessSubjectVersion?.versionId
      );

      // 修改配置状态为已配置
      await this.service.updateStatus(
        tx as PrismaService,
        reqUser,
        data.accountDictionaryId,
        true
      );
    });
    return true;
  }

  /**
   * 新增版本
   * @param tx
   * @param reqUser
   * @param accountDictionaryId
   * @param versionId
   */
  async createVersion(
    tx: PrismaService,
    reqUser: IReqUser,
    accountDictionaryId: string,
    versionId: string
  ) {
    // 删除旧版本
    await tx.accountMaterialDictionaryVersion.updateMany({
      where: {
        accountDictionaryId,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
    // 增加新版本
    await tx.accountMaterialDictionaryVersion.create({
      data: {
        accountDictionaryId,
        versionId,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }
    });
  }

  /**
   * 新增分类
   * @param tx
   * @param reqUser
   * @param versionId
   * @param categoryIds
   */
  async createCategory(
    tx: PrismaService,
    reqUser: IReqUser,
    accountDictionaryId: string,
    versionId: string
  ) {
    // 删除旧的分类
    await tx.accountMaterialDictionaryCategory.updateMany({
      where: {
        accountDictionaryId,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        versionId: versionId,
        isDeleted: false
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });

    // 增加新的分类
    await tx.$executeRaw`
      insert into account_material_dictionary_category (tenant_id, org_id, id, account_dictionary_id, category_id, version_id, create_by, update_by, create_at, update_at)  
      select
        tenant_id
        ,${reqUser.orgId} as org_id
        ,gen_random_uuid() as id
        ,${accountDictionaryId} as account_dictionary_id
        ,id as category_id
        ,material_dictionary_version_id as version_id
        ,${reqUser.id} as create_by
        ,${reqUser.id} as update_by
        ,${new Date()} as create_at
        ,${new Date()} as update_at
      from material_dictionary_category
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and material_dictionary_version_id = ${versionId}
    `;
  }

  /**
   * 新增明细
   * @param tx
   * @param reqUser
   * @param accountDictionaryId
   * @param versionId
   * @param data
   */
  async createDetail(
    tx: PrismaService,
    reqUser: IReqUser,
    accountDictionaryId: string,
    versionId: string
  ) {
    // 删除旧的明细
    await tx.accountMaterialDictionaryDetail.updateMany({
      where: {
        accountDictionaryId,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        versionId: versionId,
        isDeleted: false
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });

    // 新增新的明细
    await tx.$executeRaw`
      insert into account_material_dictionary_detail (tenant_id, org_id, id, account_dictionary_id, category_id, version_id, detail_id, create_by, update_by, create_at, update_at)  
      select
        tenant_id
        ,${reqUser.orgId} as org_id
        ,gen_random_uuid() as id
        ,${accountDictionaryId} as account_dictionary_id
        ,material_dictionary_category_id  as category_id
        ,material_dictionary_version_id as version_id
        ,id as detail_id
        ,${reqUser.id} as create_by
        ,${reqUser.id} as update_by
        ,${new Date()} as create_at
        ,${new Date()} as update_at
      from material_dictionary_detail
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and material_dictionary_version_id = ${versionId}
    `;
  }

  async createSubjectDetailRelation(
    tx: PrismaService,
    reqUser: IReqUser,
    accountDictionaryId: string,
    versionId: string,
    businessSubjectVersionId: string | undefined
  ) {
    // 删除旧的明细
    await tx.accountMaterialDetailBusinessCostSubjectDetail.updateMany({
      where: {
        accountDictionaryId,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        versionId: versionId,
        isDeleted: false
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });

    // 如果项目层启用了业务成本科目才同步数据
    if (businessSubjectVersionId) {
      // 新增新的明细
      await tx.$executeRaw`
        insert into account_material_detail_business_cost_subject_detail (tenant_id, org_id, id, account_dictionary_id, version_id, business_cost_subject_detail_id, material_dictionary_detail_id, create_by, update_by, create_at, update_at)  
        select
          tenant_id
          ,${reqUser.orgId} as org_id
          ,gen_random_uuid() as id
          ,${accountDictionaryId} as account_dictionary_id
          ,${versionId}  as version_id
          ,business_cost_subject_detail_id
          ,material_dictionary_detail_id
          ,${reqUser.id} as create_by
          ,${reqUser.id} as update_by
          ,${new Date()} as create_at
          ,${new Date()} as update_at
        from material_detail_business_cost_subject_detail
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and material_dictionary_detail_id in (
            select id from material_dictionary_detail 
            where is_deleted = false 
            and tenant_id = ${reqUser.tenantId} 
            and material_dictionary_version_id = ${versionId}  
          )
          and business_cost_subject_detail_id in (
            select id from business_cost_subject_detail
            where is_deleted = false
              and tenant_id = ${reqUser.tenantId}
              and business_cost_subject_version_id = ${businessSubjectVersionId}
          )
      `;
    }
  }

  async getChooseVersionList(reqUser: IReqUser, data: DictionaryType) {
    const result = await this.prisma.$queryRaw`
      with temp_version as (
        select
          mdv.id
          , mdv.name
          , null as parent_id
          , null as code
          , null as remark
          , mdv.id as version_id
          , mdv.business_cost_subject_version_id
          , abcsv.version_id as account_business_cost_subject_version_id
          , null::"MaterialType" as type
        from account_material_dictionary_version amdv
        join material_dictionary_version mdv
          on mdv.is_deleted = false
          and mdv.tenant_id = amdv.tenant_id
          and mdv.id = amdv.version_id
          and mdv.status != 'NOT_ENABLED'
        left join account_business_cost_subject_version abcsv
          on abcsv.is_deleted = false
          and abcsv.tenant_id = ${reqUser.tenantId}
          and abcsv.org_id = ${reqUser.orgId}
        where amdv.is_deleted = false
          and amdv.tenant_id = ${reqUser.tenantId}
          and amdv.org_id = ${reqUser.orgId}
          and amdv.account_dictionary_id = ${data.accountDictionaryId}
      )
      ,temp_category as (
        select
          mdc.id
          ,mdc.name
          ,case when mdc.parent_id is null then mdc.material_dictionary_version_id else mdc.parent_id end as parent_id
          ,mdc.code
          ,mdc.remark
          ,mdc.material_dictionary_version_id as version_id
          ,temp_version.business_cost_subject_version_id
          ,temp_version.account_business_cost_subject_version_id
          ,mdc.type
        from material_dictionary_category mdc
        join temp_version
          on temp_version.id = mdc.material_dictionary_version_id
        join account_material_dictionary_category amdc
          on amdc.category_id = mdc.id
          and amdc.version_id = mdc.material_dictionary_version_id
          and amdc.tenant_id = ${reqUser.tenantId}
          and amdc.org_id = ${reqUser.orgId}
          and amdc.is_deleted = false
        where mdc.is_deleted = false
          and mdc.tenant_id = ${reqUser.tenantId}
        order by mdc.level, mdc.sort
      )
      select * from temp_version
      union all
      select * from temp_category
    `;
    return result;
  }

  async getChooseDetailList(
    req: Request,
    reqUser: IReqUser,
    data: DictionaryDetailType
  ) {
    const { accountDictionaryId, versionId, categoryId } = data;
    // 查询当前公司的最上级组织
    const topOrg = await this.platformService.getOrgParentIds(
      req,
      reqUser.tenantId,
      reqUser.orgId
    );
    let categoryIds: string[] = [];
    if (categoryId) {
      // 查询该分类id下的所有叶子级id
      const category = await this.prisma.$queryRaw<any[]>`
        with RECURSIVE temp_category as (
          select id, name, is_leaf, is_active from material_dictionary_category 
          where is_deleted = false 
          and tenant_id = ${reqUser.tenantId}
          and org_id = ANY(${topOrg}::text[])
          and id = ${categoryId}
          and is_active = true
          
          UNION ALL
          
          select child.id, child.name, child.is_leaf, child.is_active from material_dictionary_category as child
          join temp_category tc on tc.id = child.parent_id
          where child.is_deleted = false 
          and child.is_active = true
        )
        select tm.id from temp_category tm
        join account_material_dictionary_category amdc
        on amdc.category_id = tm.id 
        and amdc.is_deleted = false 
        and amdc.account_dictionary_id = ${accountDictionaryId}
        and amdc.tenant_id = ${reqUser.tenantId}
        and amdc.org_id = ${reqUser.orgId}
        where tm.is_leaf = true
      `;
      categoryIds = category.map((item) => item.id);
    }
    const res = await this.prisma.$queryRaw<any[]>`
      SELECT
        bcsv.*,
        COALESCE(STRING_AGG(amd.business_cost_subject_detail_id, ','), '') AS business_cost_subject_detail_ids
      FROM (
        select mde.id, amde.category_id, amde.version_id, 
          mde.name, mde.code, mde.specification_model, 
          mde.metering_unit, mde.type, mde.remark, 
          mde.account_explanation, mde.tenant_id, mde.org_id,
          amde.account_dictionary_id
        from account_material_dictionary_detail amde 
        join material_dictionary_detail mde
        on mde.id = amde.detail_id
          and mde.is_deleted = false
          and mde.is_active = true
          and mde.org_id = ANY(${topOrg}::text[])
          and mde.tenant_id = ${reqUser.tenantId}
        where amde.is_deleted = false
          ${versionId ? Prisma.sql`and amde.version_id = ${versionId}` : Prisma.empty}
          ${categoryIds.length ? Prisma.sql`and amde.category_id in (${categoryIds.join(',')})` : Prisma.empty}
          and amde.account_dictionary_id = ${accountDictionaryId}
          and amde.tenant_id = ${reqUser.tenantId}
          and amde.org_id = ${reqUser.orgId}
        order by amde.category_id, mde.sort
      ) bcsv
      LEFT JOIN account_material_detail_business_cost_subject_detail amd 
        ON amd.material_dictionary_detail_id = bcsv.id
        AND amd.tenant_id = bcsv.tenant_id
        AND amd.org_id = ${reqUser.orgId}
        AND amd.is_deleted = false
      GROUP BY 
        bcsv.id, 
        bcsv.name,
        bcsv.version_id,
        bcsv.category_id,
        bcsv.code,
        bcsv.specification_model,
        bcsv.metering_unit,
        bcsv.type,
        bcsv.remark,
        bcsv.tenant_id,
        bcsv.org_id,
        bcsv.account_explanation,
        bcsv.account_dictionary_id
    `;
    return res.map((item) => {
      return {
        ...item,
        businessCostSubjectDetailIds:
          item.businessCostSubjectDetailIds.split(',')
      };
    });
  }

  async deleteVersion(
    versionId: string,
    reqUser: IReqUser,
    data: DictionaryType
  ) {
    // 缺少删除的引用判断，后续补上（查询明细是否被引用，已引用的不可删除）
    await this.prisma.$transaction(async (tx) => {
      // 删除版本
      await this.removeVerisonByVersionId(
        tx as PrismaService,
        versionId,
        reqUser,
        data.accountDictionaryId
      );
      // 删除分类
      await this.removeCategoryByVersionId(
        tx as PrismaService,
        versionId,
        reqUser,
        data
      );
      // 删除明细
      await this.removeDetailByVersionId(
        tx as PrismaService,
        versionId,
        reqUser,
        data
      );
      // 查询是否还有其他版本的引用
      const version = await tx.accountMaterialDictionaryVersion.findFirst({
        where: {
          accountDictionaryId: data.accountDictionaryId,
          isDeleted: false,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId
        }
      });
      if (!version) {
        // 修改配置状态为未配置
        await this.service.updateStatus(
          tx as PrismaService,
          reqUser,
          data.accountDictionaryId,
          false
        );
      }
    });
    return true;
  }

  async deleteCategory(
    categoryId: string,
    reqUser: IReqUser,
    data: DictionaryCategoryType
  ) {
    // 缺少删除的引用判断，后续补上（查询明细是否被引用，已引用的不可删除）
    await this.prisma.$transaction(async (tx) => {
      // 删除分类
      await this.removeCategoryByCategoryId(
        tx as PrismaService,
        categoryId,
        reqUser,
        data.accountDictionaryId
      );
      // 删除明细
      await this.removeDetailByCategoryId(
        tx as PrismaService,
        categoryId,
        reqUser,
        data
      );
      // 查询该版本下是否还存在其他分类
      await this.getCategoryByVersionId(
        tx as PrismaService,
        data.versionId,
        reqUser,
        data.accountDictionaryId
      );
      // 查询是否还有其他版本的引用
      await this.getVersionByAccountDictionaryId(
        tx as PrismaService,
        reqUser,
        data.accountDictionaryId
      );
    });
    return true;
  }

  async deleteDetail(
    detailId: string,
    reqUser: IReqUser,
    data: DeleteDictionaryDetailType
  ) {
    // 缺少删除的引用判断，后续补上（查询明细是否被引用，已引用的不可删除）
    await this.prisma.$transaction(async (tx) => {
      // 删除明细
      await this.removeDetailByDetailId(
        tx as PrismaService,
        detailId,
        reqUser,
        data.accountDictionaryId,
        data.versionId,
        data.categoryId
      );
      // 查询该版本分类下是否还有明细
      await this.getDetailByCategoryIdAndVersionId(
        tx as PrismaService,
        data.versionId,
        data.categoryId,
        detailId,
        reqUser,
        data.accountDictionaryId
      );
      // 查询该版本下是否还存在其他分类
      await this.getCategoryByVersionId(
        tx as PrismaService,
        data.versionId,
        reqUser,
        data.accountDictionaryId
      );
      // 查询是否还有其他版本的引用
      await this.getVersionByAccountDictionaryId(
        tx as PrismaService,
        reqUser,
        data.accountDictionaryId
      );
    });
    return true;
  }

  // 查询该版本分类下是否还有明细
  async getDetailByCategoryIdAndVersionId(
    tx: PrismaService,
    versionId: string,
    categoryId: string,
    detailId: string,
    reqUser: IReqUser,
    accountDictionaryId: string
  ) {
    const detailList = await tx.accountMaterialDictionaryDetail.findFirst({
      select: {
        id: true
      },
      where: {
        accountDictionaryId: accountDictionaryId,
        versionId,
        categoryId,
        detailId,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      }
    });
    if (!detailList) {
      // 若不存在，删除分类
      return await this.removeCategoryByCategoryId(
        tx,
        categoryId,
        reqUser,
        accountDictionaryId
      );
    }
  }

  // 查询该版本下是否还存在其他分类
  async getCategoryByVersionId(
    tx: PrismaService,
    versionId: string,
    reqUser: IReqUser,
    accountDictionaryId: string
  ) {
    const categoryList =
      await this.prisma.accountMaterialDictionaryCategory.findFirst({
        select: {
          id: true
        },
        where: {
          isDeleted: false,
          versionId: versionId,
          accountDictionaryId,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId
        }
      });
    if (!categoryList) {
      // 如果该版本下已没有其他分类，则删除该版本
      await this.removeVerisonByVersionId(
        tx,
        versionId,
        reqUser,
        accountDictionaryId
      );
    }
  }

  // 查询是否还有其他版本的引用
  async getVersionByAccountDictionaryId(
    tx: PrismaService,
    reqUser: IReqUser,
    accountDictionaryId: string
  ) {
    const version = await tx.accountMaterialDictionaryVersion.findFirst({
      where: {
        accountDictionaryId,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId
      }
    });
    if (!version) {
      // 修改配置状态为未配置
      await this.service.updateStatus(tx, reqUser, accountDictionaryId, false);
    }
  }

  // 根据明细id删除明细
  async removeDetailByDetailId(
    tx: PrismaService,
    detailId: string,
    reqUser: IReqUser,
    accountDictionaryId: string,
    versionId: string,
    categoryId: string
  ) {
    // 缺少删除的引用判断，后续补上（查询明细是否被引用，已引用的不可删除）
    return await tx.accountMaterialDictionaryDetail.updateMany({
      where: {
        detailId,
        isDeleted: false,
        accountDictionaryId,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        versionId,
        categoryId
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }

  // 根据分类id删除分类
  async removeCategoryByCategoryId(
    tx: PrismaService,
    categoryId: string,
    reqUser: IReqUser,
    accountDictionaryId: string
  ) {
    return await tx.accountMaterialDictionaryCategory.updateMany({
      where: {
        categoryId,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        accountDictionaryId
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }

  // 根据分类id删除明细
  async removeDetailByCategoryId(
    tx: PrismaService,
    categoryId: string,
    reqUser: IReqUser,
    data: DictionaryType
  ) {
    return await tx.accountMaterialDictionaryDetail.updateMany({
      where: {
        categoryId,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        accountDictionaryId: data.accountDictionaryId
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }

  // 删除版本
  async removeVerisonByVersionId(
    tx: PrismaService,
    versionId: string,
    reqUser: IReqUser,
    accountDictionaryId: string
  ) {
    return await tx.accountMaterialDictionaryVersion.updateMany({
      where: {
        versionId,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        accountDictionaryId: accountDictionaryId
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }

  // 根据版本删除分类
  async removeCategoryByVersionId(
    tx: PrismaService,
    versionId: string,
    reqUser: IReqUser,
    data: DictionaryType
  ) {
    return await tx.accountMaterialDictionaryCategory.updateMany({
      where: {
        versionId,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        accountDictionaryId: data.accountDictionaryId
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }

  // 根据版本删除明细
  async removeDetailByVersionId(
    tx: PrismaService,
    versionId: string,
    reqUser: IReqUser,
    data: DictionaryType
  ) {
    return await tx.accountMaterialDictionaryDetail.updateMany({
      where: {
        versionId,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        accountDictionaryId: data.accountDictionaryId
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }

  // 获取材料字典明细的单位换算列表
  async getMaterialUnitCalculationList(reqUser: IReqUser, id: string) {
    const result = await this.prisma.materialDictionaryUnitCalculation.findMany(
      {
        where: {
          materialDictionaryDetailId: id,
          isDeleted: false,
          tenantId: reqUser.tenantId
        },
        select: {
          id: true,
          unit: true,
          factor: true,
          remark: true
        },
        orderBy: { sort: 'asc' }
      }
    );

    return result;
  }

  // 修改业务成本科目关联关系
  async editBusinessCostSubject(
    reqUser: IReqUser,
    data: EditBusinessCostSubjectDto
  ) {
    await this.prisma.$transaction(async (tx) => {
      // 先删除关系
      await tx.accountMaterialDetailBusinessCostSubjectDetail.updateMany({
        where: {
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          materialDictionaryDetailId: data.detailId,
          accountDictionaryId: data.accountDictionaryId,
          versionId: data.versionId
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });

      // 新增关系
      const insertData: any[] = data.businessCostSubjectDetailsIds.map(
        (id) => ({
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          accountDictionaryId: data.accountDictionaryId,
          versionId: data.versionId,
          materialDictionaryDetailId: data.detailId,
          businessCostSubjectDetailId: id,
          createBy: reqUser.id,
          updateBy: reqUser.id
        })
      );
      await tx.accountMaterialDetailBusinessCostSubjectDetail.createMany({
        data: insertData
      });
    });

    return true;
  }
}
