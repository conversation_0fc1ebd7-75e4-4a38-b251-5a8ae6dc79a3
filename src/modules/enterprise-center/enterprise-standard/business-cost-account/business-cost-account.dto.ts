import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsOptional, IsString } from 'class-validator';

import { DictionaryTypeEnum } from '@/common/enums/common.enum';

export class DictionaryType {
  @ApiProperty({ description: '字典id', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '字典id不能为空' })
  @IsString({ message: '字典id必须为字符串' })
  accountDictionaryId: string;
}

export class DictionaryCategoryType extends DictionaryType {
  @ApiProperty({ description: '版本id', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '版本id不能为空' })
  @IsString({ message: '版本id必须为字符串' })
  versionId: string;
}

export class DictionaryDetailType extends DictionaryType {
  @ApiProperty({ description: '版本id', default: 'xxxxxxxxxxxx' })
  @IsOptional({ message: '版本id可以为空，当为空时查询全部明细' })
  @IsString({ message: '版本id必须为字符串' })
  versionId?: string;

  @ApiProperty({ description: '分类id', default: 'xxxxxxxxxxxx' })
  @IsOptional({ message: '分类id可以为空，当为空时查询全部明细' })
  @IsString({ message: '分类id必须为字符串' })
  categoryId?: string;
}

export class DeleteDictionaryDetailType extends DictionaryType {
  @ApiProperty({ description: '版本id', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '版本id不可以为空' })
  @IsString({ message: '版本id必须为字符串' })
  versionId: string;

  @ApiProperty({ description: '分类id', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '分类id不可以为空' })
  @IsString({ message: '分类id必须为字符串' })
  categoryId: string;
}

export class CreateAccountDto {
  @ApiProperty({ description: '版本id', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '版本id不能为空' })
  @IsString({ message: '版本id必须为字符串' })
  versionId: string;

  @ApiProperty({ description: '字典id', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '字典id不能为空' })
  @IsString({ message: '字典id必须为字符串' })
  accountDictionaryId: string;
}

export class CreateBusinessCostAccountDetailDto {
  @ApiProperty({ description: '分类id', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '分类id不能为空' })
  @IsString({ message: '分类id必须为字符串' })
  categoryId: string;

  @ApiProperty({ description: '明细id', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '明细id不能为空' })
  @IsString({ message: '明细id必须为字符串' })
  detailId: string;
}

export class CreateBusinessCostAccountDto extends DictionaryType {
  @ApiProperty({ description: '版本id', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '版本id不能为空' })
  @IsString({ message: '版本id必须为字符串' })
  versionId: string;

  @ApiProperty({ description: '分类数组', default: [] })
  @IsNotEmpty({ message: '分类数组不能为空' })
  @IsArray({ message: '分类数组必须为数组' })
  categoryIds: string[];

  @ApiProperty({
    description: '明细数组',
    default: [],
    type: CreateBusinessCostAccountDetailDto,
    isArray: true
  })
  @IsNotEmpty({ message: '明细数组不能为空' })
  @IsArray({ message: '明细数组必须为数组' })
  details: CreateBusinessCostAccountDetailDto[];
}

export class MaterialUnitCalculationResponseDto {
  @ApiProperty({ description: 'id' })
  id: string;
  @ApiProperty({ description: '名称' })
  unit: string;
  @ApiProperty({ description: '系数' })
  factor: number;
  @ApiProperty({ description: '备注' })
  remark: string;
}

export enum DictType {}

export class EditBusinessCostSubjectDto {
  @ApiProperty({ description: '字典类型' })
  @IsNotEmpty({ message: '字典类型不能为空' })
  @IsString({ message: '字典类型必须为字符串' })
  dictType: DictionaryTypeEnum;

  @ApiProperty({ description: '成本核算设置字典ID' })
  @IsNotEmpty()
  @IsString()
  accountDictionaryId: string;

  @ApiProperty({ description: '字典版本ID' })
  @IsNotEmpty()
  @IsString()
  versionId: string;

  @ApiProperty({ description: '细目ID，材料字典/机械字典/费用字典' })
  @IsNotEmpty({ message: 'detailId不能为空' })
  @IsString({ message: 'detailId必须为字符串' })
  detailId: string;

  @ApiProperty({ description: '业务成本明细ID' })
  @IsNotEmpty({ message: 'businessCostSubjectDetailId不能为空' })
  @IsArray({ message: 'businessCostSubjectDetailsIds必须为数组' })
  businessCostSubjectDetailsIds: string[];
}
