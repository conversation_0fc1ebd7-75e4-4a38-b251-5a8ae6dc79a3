import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';

import { PlatformModule } from '../../../platform/platform.module';
import { AccountBusinessCostSubjectService } from './account-business-cost-subject/account-business-cost-subject.service';
import { AccountCostDictionaryService } from './account-cost-dictionary/account-account-cost-dictionary.service';
import { AccountMaterialDictionaryService } from './account-material-dictionary/account-material-dictionary.service';
import { AccountMachineryDictionaryService } from './account-mechinery-dictionary/account-account-mechinery-dictionary.service';
import { AccountTaxRateDictionaryService } from './account-taxratel-dictionary/account-taxratel-dictionary.service';
import { BusinessCostAccountController } from './business-cost-account.controller';
import { BusinessCostAccountService } from './business-cost-account.service';

@Module({
  imports: [PrismaModule, PlatformModule],
  controllers: [BusinessCostAccountController],
  providers: [
    BusinessCostAccountService,
    AccountBusinessCostSubjectService,
    AccountMaterialDictionaryService,
    AccountMachineryDictionaryService,
    AccountCostDictionaryService,
    AccountTaxRateDictionaryService
  ]
})
export class BusinessCostAccountModule {}
