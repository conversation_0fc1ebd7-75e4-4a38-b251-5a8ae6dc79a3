import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateAccessoryListDto {
  @ApiProperty({
    description: 'id,若不存在id的则为新增，若存在id则判断状态',
    default: 'xxxxxxxxxxxx'
  })
  @IsOptional()
  @IsString({ message: 'id必须为字符串' })
  id?: string;

  // @ApiProperty({
  //   description: '状态：true则为删除，false的则不进行处理',
  //   default: 'true'
  // })
  // @IsOptional()
  // @IsBoolean({ message: 'i状态必须为boolean' })
  // type?: boolean;

  @ApiProperty({ description: '文件名称', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '文件名称不能为空' })
  @IsString({ message: '文件名称必须为字符串' })
  fileName: string;

  @ApiProperty({ description: '文件key', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '文件key不能为空' })
  @IsString({ message: '文件key必须为字符串' })
  fileKey: string;

  @ApiProperty({ description: '文件扩展名', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '文件扩展名不能为空' })
  @IsString({ message: '文件扩展名必须为字符串' })
  fileExt: string;

  @ApiProperty({ description: '附文件大小件大小', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '文件大小不能为空' })
  @IsString({ message: '文件大小必须为字符串' })
  fileSize: string;

  @ApiProperty({ description: '文件类型', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '文件类型不能为空' })
  @IsString({ message: '文件类型必须为字符串' })
  fileContentType: string;

  @ApiProperty({ description: '备注', default: 'xxxxxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '备注必须为字符串' })
  remark?: string;
}

export class QueryDirectoryAccessoryDto {
  @ApiProperty({ description: '供应商id', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '供应商id不能为空' })
  @IsString({ message: '供应商id必须为字符串' })
  supplierDirectoryId: string;
}

export class CreateDirectoryAccessoryDto {
  @ApiProperty({ description: '供应商id' })
  @IsNotEmpty({ message: '供应商id不能为空' })
  @IsString({ message: '供应商id必须为字符串' })
  supplierDirectoryId: string;

  @ApiProperty({ description: '文件名称' })
  @IsNotEmpty({ message: '文件名称不能为空' })
  @IsString({ message: '文件名称必须是字符串' })
  fileName: string;

  @ApiProperty({ description: '文件扩展名' })
  @IsNotEmpty({ message: '文件扩展名不能为空' })
  @IsString({ message: '文件扩展名必须是字符串' })
  fileExt: string;

  @ApiProperty({ description: '文件key' })
  @IsNotEmpty({ message: '文件key不能为空' })
  @IsString({ message: '文件key必须是字符串' })
  fileKey: string;

  @ApiProperty({ description: '文件大小' })
  @IsNotEmpty({ message: '文件大小不能为空' })
  @IsString({ message: '文件大小必须是字符串' })
  fileSize: string;

  @ApiProperty({ description: '文件类型' })
  @IsNotEmpty({ message: '文件类型不能为空' })
  @IsString({ message: '文件类型必须是字符串' })
  fileContentType: string;

  @ApiProperty({ description: '备注' })
  @IsOptional({ message: '备注可以为空' })
  @IsString({ message: '备注必须是字符串' })
  remark?: string;
}
