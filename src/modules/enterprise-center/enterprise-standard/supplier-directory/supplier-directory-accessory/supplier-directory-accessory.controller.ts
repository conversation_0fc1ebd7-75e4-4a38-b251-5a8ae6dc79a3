import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  CreateDirectoryAccessoryDto,
  QueryDirectoryAccessoryDto
} from './supplier-directory-accessory.dto';
import { SupplierDirectoryAccessoryService } from './supplier-directory-accessory.service';

@ApiTags('供应商名录附件')
@Controller('supplier-directory-accessory')
export class SupplierDirectoryAccessoryController {
  constructor(private readonly service: SupplierDirectoryAccessoryService) {}

  @ApiOperation({
    summary: '查询供应商附件',
    description: '查询供应商附件'
  })
  @ApiResponse({ status: 200, description: '查询供应商附件成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/list')
  async list(
    @Query() query: QueryDirectoryAccessoryDto,
    @ReqUser() reqUser: IReqUser
  ) {
    return await this.service.getList(query, reqUser);
  }

  @ApiOperation({
    summary: '新增供应商附件',
    description: '新增供应商附件'
  })
  @ApiResponse({ status: 200, description: '新增供应商附件成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('')
  async add(
    @Body() body: CreateDirectoryAccessoryDto,
    @ReqUser() reqUser: IReqUser
  ) {
    return await this.service.save(body, reqUser);
  }

  @ApiOperation({
    summary: '删除供应商附件',
    description: '删除供应商附件'
  })
  @ApiResponse({ status: 200, description: '删除供应商附件成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Delete('/:id')
  async delete(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    return await this.service.delete(id, reqUser);
  }
}
