import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';
import { FileManageService } from '@/modules/file-manage/file-manage.service';
import { PlatformModule } from '@/modules/platform/platform.module';

import { SupplierDirectoryController } from './supplier-directory/supplier-directory.controller';
import { SupplierDirectoryService } from './supplier-directory/supplier-directory.service';
import { SupplierDirectoryAccessoryController } from './supplier-directory-accessory/supplier-directory-accessory.controller';
import { SupplierDirectoryAccessoryService } from './supplier-directory-accessory/supplier-directory-accessory.service';
import { SupplierDirectoryChangeRecordController } from './supplier-directory-change-record/supplier-directory-change-record.controller';
import { SupplierDirectoryChangeRecordService } from './supplier-directory-change-record/supplier-directory-change-record.service';

@Module({
  imports: [PrismaModule, PlatformModule],
  controllers: [
    SupplierDirectoryChangeRecordController,
    SupplierDirectoryAccessoryController,
    SupplierDirectoryController
  ],
  providers: [
    SupplierDirectoryChangeRecordService,
    SupplierDirectoryAccessoryService,
    SupplierDirectoryService,
    FileManageService
  ]
})
export class SupplierDirectoryModule {}
