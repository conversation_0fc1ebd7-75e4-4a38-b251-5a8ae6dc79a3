import { ApiProperty } from '@nestjs/swagger';

export class ChangeRecordDetailDto {
  @ApiProperty({ description: '变更字段名称' })
  fieldName: string;

  @ApiProperty({ description: '旧值' })
  oldValue?: string;

  @ApiProperty({ description: '新值' })
  newValue: string;

  @ApiProperty({ description: '变更类型' })
  changeType?: ChangeType;

  @ApiProperty({ description: '字段类型' })
  fieldType: DetailsFieldType;
}

// 字段类型
export enum ChangeType {
  ADD = 'ADD', // 新增
  DELETE = 'DELETE' // 删除
}

// 明细字段类型
export enum DetailsFieldType {
  GENERAL = 'GENERAL', //普通类型
  FILE = 'FILE' //文件类型
}

// export class QueryChangeRecordDto {
//   @ApiProperty({ description: '供应商id', default: 'xxxxxxxxxxxx' })
//   @IsNotEmpty({ message: '供应商id不能为空' })
//   @IsString({ message: '供应商id必须为字符串' })
//   supplierDirectoryId: string;
// }
