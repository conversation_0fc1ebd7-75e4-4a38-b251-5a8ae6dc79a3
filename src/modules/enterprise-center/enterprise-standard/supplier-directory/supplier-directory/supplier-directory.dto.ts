import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsEmail,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPhoneNumber,
  IsString,
  Length,
  Max,
  <PERSON><PERSON><PERSON><PERSON>,
  ValidateIf
} from 'class-validator';

import { CreateAccessoryListDto } from '../supplier-directory-accessory/supplier-directory-accessory.dto';

// 供应商分类
export enum SupplierDirectoryClassify {
  SERVICE_OUTSOURCING = 'SERVICE_OUTSOURCING', // 服务外包
  MECHANICAL_LEASING = 'MECHANICAL_LEASING', // 机械租赁
  LABOR_SUBCONTRACTING = 'LABOR_SUBCONTRACTING', // 劳务分包
  MATERIAL_PURCHASING = 'MATERIAL_PURCHASING', // 物资采购
  PROFESSIONAL_SUBCONTRACTING = 'PROFESSIONAL_SUBCONTRACTING' // 专业分包
}

// 纳税人资质类型
enum TaxpayerQualification {
  GENERAL = 'GENERAL', // 一般纳税人
  SMALL_SCALE = 'SMALL_SCALE' // 小规模纳税人
}

// 供应商评级
enum Grade {
  QUALIFIED = 'QUALIFIED', // 合格
  UNQUALIFIED = 'UNQUALIFIED', // 不合格
  BLACKLIST = 'BLACKLIST' // 黑名单
}

export class PublishSupplierDto {
  @ApiProperty({ description: '是否发布', example: 'true:发布,false:取消发布' })
  @IsNotEmpty({ message: '是否发布不能为空' })
  @IsBoolean({ message: '是否发布必须为boolean值' })
  isPublished: boolean;
}

export class CreateSupplierDto {
  @ApiProperty({ description: '供应商全称', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '供应商全称不能为空' })
  @IsString({ message: '供应商全称必须为字符串' })
  fullName: string;

  @ApiProperty({ description: '供应商简称', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '供应商简称不能为空' })
  @IsString({ message: '供应商简称必须为字符串' })
  simpleName: string;

  @ApiProperty({ description: '统一社会信用代码', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '统一社会信用代码不能为空' })
  @IsString({ message: '统一社会信用代码必须为字符串' })
  @Length(18, 18, { message: '统一社会信用代码长度必须为18位' })
  creditCode: string;

  @ApiProperty({ description: '省市区code', default: 'xxxxxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '省市区code必须为字符串' })
  regionCode?: string;

  @ApiProperty({ description: '注册所在省', default: 'xxxxxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '注册所在省必须为字符串' })
  registeredProvince?: string;

  @ApiProperty({ description: '注册所在市', default: 'xxxxxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '注册所在市必须为字符串' })
  registeredCity?: string;

  @ApiProperty({ description: '注册所在区县', default: 'xxxxxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '注册所在区县必须为字符串' })
  registeredCounty?: string;

  @ApiProperty({ description: '注册地址', default: 'xxxxxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '注册地址必须为字符串' })
  registeredAddress?: string;

  @ApiProperty({ description: '主营业务', default: 'xxxxxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '主营业务必须为字符串' })
  mainBusiness?: string;

  @ApiProperty({
    description: '供应商分类',
    example: SupplierDirectoryClassify.LABOR_SUBCONTRACTING,
    enum: SupplierDirectoryClassify
  })
  @IsNotEmpty({ message: '供应商分类不能为空' })
  @IsArray({ message: '供应商分类必须为数组' })
  // @IsIn(Object.values(SupplierDirectoryClassify), {
  //   message: '供应商分类必须是有效枚举值'
  // })
  classify: SupplierDirectoryClassify[];

  @ApiProperty({ description: '供应工作内容', default: 'xxxxxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '供应工作内容必须为字符串' })
  jobContent?: string;

  @ApiProperty({ description: '供应区域', default: 'xxxxxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '供应区域必须为字符串' })
  region?: string;

  @ApiProperty({ description: '单位类型', default: 'xxxxxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '单位类型必须为字符串' })
  unitType?: string;

  @ApiProperty({
    description: '纳税人资质',
    example: TaxpayerQualification.GENERAL,
    enum: TaxpayerQualification
  })
  @IsNotEmpty({ message: '纳税人资质不能为空' })
  @IsString({ message: '纳税人资质必须为字符串' })
  @IsIn(Object.values(TaxpayerQualification), {
    message: '纳税人资质必须是有效枚举值'
  })
  taxpayerQualification: TaxpayerQualification;

  @ApiProperty({ description: '注册资金（万元）', default: 1000 })
  @IsOptional()
  @IsNumber({}, { message: '注册资金（万元）必须为数字' })
  registeredCapital?: number;

  @ApiProperty({
    description: '成立时间',
    example: '2022-12-25 12:00:00',
    required: false
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : undefined))
  @IsDate({ message: '成立时间必须是有效的日期' })
  establishAt?: Date;

  @ApiProperty({ description: '联系人', default: 'xxxxxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '联系人必须为字符串' })
  contactBy?: string;

  @ApiProperty({ description: '联系人电话', default: 'xxxxxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '联系人电话必须为字符串' })
  @IsPhoneNumber('CN', { message: '联系人电话格式不正确' })
  contactPhone?: string;

  @ApiProperty({ description: '联系人邮箱', default: 'xxxxxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '联系人邮箱必须为字符串' })
  @IsEmail({}, { message: '联系人邮箱格式不正确' })
  contactEmail?: string;

  @ApiProperty({
    description: '法定代表人/单位负责人',
    default: 'xxxxxxxxxxxx'
  })
  @IsOptional()
  @IsString({ message: '法定代表人/单位负责人必须为字符串' })
  LegalBy?: string;

  @ApiProperty({ description: '关联企业', default: 'xxxxxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '关联企业必须为字符串' })
  relationEnterprise?: string;

  @ApiProperty({
    description: '引进时间',
    default: 'xxxxxxxxxxxx'
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : undefined))
  @IsDate({ message: '引进时间必须是有效的日期时间' })
  introductionAt?: Date;

  @ApiProperty({ description: '备注', default: 'xxxxxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '备注必须为字符串' })
  remark?: string;

  // @ApiProperty({
  //   description: '供应商评级',
  //   example: Grade.BLACKLIST,
  //   enum: Grade
  // })
  // @IsNotEmpty({ message: '供应商评级不能为空' })
  // @IsString({ message: '供应商评级必须为字符串' })
  // @IsIn(Object.values(Grade), {
  //   message: '供应商评级必须是有效枚举值'
  // })
  // grade: Grade;

  // @ApiProperty({
  //   description: '评价时间',
  //   default: 'xxxxxxxxxxxx'
  // })
  // @IsOptional()
  // @ValidateIf((o) => o.establishAt !== undefined) // 字段存在时才校验
  // @Transform(({ value }) => {
  //   const date = new Date(value);
  //   return isNaN(date.getTime()) ? undefined : date;
  // })
  // @IsDate({ message: '评价时间必须是有效的日期时间' })
  // evaluateAt?: Date;

  // @ApiProperty({
  //   description: '退场时间',
  //   default: 'xxxxxxxxxxxx'
  // })
  // @IsOptional()
  // @ValidateIf((o) => o.establishAt !== undefined) // 字段存在时才校验
  // @Transform(({ value }) => {
  //   const date = new Date(value);
  //   return isNaN(date.getTime()) ? undefined : date;
  // })
  // @IsDate({ message: '退场时间必须是有效的日期时间' })
  // exitAt?: Date;

  // @ApiProperty({
  //   description: '淘汰时间',
  //   default: 'xxxxxxxxxxxx'
  // })
  // @IsOptional()
  // @ValidateIf((o) => o.establishAt !== undefined) // 字段存在时才校验
  // @Transform(({ value }) => {
  //   const date = new Date(value);
  //   return isNaN(date.getTime()) ? undefined : date;
  // })
  // @IsDate({ message: '淘汰时间必须是有效的日期时间' })
  // weedOutAt?: Date;

  @ApiProperty({
    description: '所属附件',
    default: []
  })
  @IsArray({ message: '附件必须是一个数组' })
  accessoryList: CreateAccessoryListDto[];
}

export class UpdateSupplierDto extends CreateSupplierDto {}

export interface QueryOptions {
  includeAccessory?: boolean; // 是否包含附件
}
