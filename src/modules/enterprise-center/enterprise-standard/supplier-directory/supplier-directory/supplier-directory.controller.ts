import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post,
  Req
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import { CreateSupplierDto, UpdateSupplierDto } from './supplier-directory.dto';
import { SupplierDirectoryService } from './supplier-directory.service';

@ApiTags('企业标准供应商目录')
@Controller('enterprise-standard-supplier-directory')
export class SupplierDirectoryController {
  constructor(private readonly service: SupplierDirectoryService) {}

  @ApiOperation({
    summary: '新增供应商',
    description: '新增供应商'
  })
  @ApiResponse({ status: 200, description: '新增供应商成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/add')
  async create(@ReqUser() reqUser: IReqUser, @Body() data: CreateSupplierDto) {
    // 校验统一信用代码
    const isExist = await this.service.getObjByCreditCode(
      data.creditCode,
      reqUser
    );
    if (isExist) {
      throw new HttpException('统一社会信用代码已存在', HttpStatus.BAD_REQUEST);
    }
    return await this.service.add(data, reqUser);
  }

  @ApiOperation({
    summary: '编辑供应商',
    description: '编辑供应商'
  })
  @ApiResponse({ status: 200, description: '编辑供应商成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Patch('/edit/:id')
  async edit(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() data: UpdateSupplierDto
  ) {
    const supplierDirectory = await this.service.getObj(id, reqUser);
    if (supplierDirectory.isPublished) {
      throw new HttpException('已发布供应商不能编辑', HttpStatus.BAD_REQUEST);
    }
    if (supplierDirectory.isDefault) {
      throw new HttpException('默认供应商不能编辑', HttpStatus.BAD_REQUEST);
    }
    const isExist = await this.service.getObjByCreditCodeNotInOwer(
      id,
      data.creditCode,
      reqUser
    );
    if (isExist) {
      throw new HttpException('统一社会信用代码已存在', HttpStatus.BAD_REQUEST);
    }
    return await this.service.edit(id, data, reqUser);
  }

  @ApiOperation({
    summary: '供应商取消发布',
    description: '供应商取消发布'
  })
  @ApiResponse({ status: 200, description: '供应商取消发布成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Patch('/unPublish/:id')
  async unPublish(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    return await this.service.unPublish(id, reqUser);
  }

  @ApiOperation({
    summary: '供应商发布',
    description: '供应商发布'
  })
  @ApiResponse({ status: 200, description: '供应商发布成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Patch('/publish/:id')
  async publish(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    return await this.service.publish(id, reqUser);
  }

  @ApiOperation({
    summary: '查询供应商',
    description: '查询供应商'
  })
  @ApiResponse({ status: 200, description: '查询供应商成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/list')
  async list(@Req() req: Request, @ReqUser() reqUser: IReqUser) {
    return await this.service.getList(req, reqUser);
  }

  @ApiOperation({
    summary: '删除供应商',
    description: '删除供应商'
  })
  @ApiResponse({ status: 200, description: '删除供应商成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Delete('/del/:id')
  async del(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    const data = await this.service.getObj(id, reqUser);
    if (data.isPublished) {
      throw new HttpException('已发布供应商不能删除', HttpStatus.BAD_REQUEST);
    }
    if (data.isDefault) {
      throw new HttpException('默认供应商不能删除', HttpStatus.BAD_REQUEST);
    }
    return await this.service.del(id, reqUser);
  }

  @ApiOperation({
    summary: '查询供应商对象',
    description: '查询供应商对象'
  })
  @ApiResponse({ status: 200, description: '查询供应商对象成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/obj/:id')
  async obj(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    return await this.service.getObj(id, reqUser);
  }
}
