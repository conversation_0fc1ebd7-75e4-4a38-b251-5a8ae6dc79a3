import { <PERSON><PERSON><PERSON> } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';

import { PlatformModule } from '../../../platform/platform.module';
import { ProjectBasicInfoCategoryController } from './project-basic-info-category/project-basic-info-category.controller';
import { ProjectBasicInfoCategoryService } from './project-basic-info-category/project-basic-info-category.service';
import { ProjectBasicInfoFieldDetailController } from './project-basic-info-field-detail/project-basic-info-field-detail.controller';
import { ProjectBasicInfoFieldDetailService } from './project-basic-info-field-detail/project-basic-info-field-detail.service';

@Module({
  imports: [PrismaModule, PlatformModule],
  controllers: [
    ProjectBasicInfoCategoryController,
    ProjectBasicInfoFieldDetailController
  ],
  providers: [
    ProjectBasicInfoCategoryService,
    ProjectBasicInfoFieldDetailService
  ]
})
export class ProjectBaseInfoModule {}
