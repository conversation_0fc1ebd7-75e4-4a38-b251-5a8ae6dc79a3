import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsIn,
  IsNotEmpty,
  IsOptional,
  IsString
} from 'class-validator';

import { BasicProjectFieldType, EnableStatus } from '@/prisma/generated';

export class CreateProjectBasicInfoFieldDetailDto {
  @ApiProperty({ description: '分组id', example: 'xxxxxxxxx' })
  @IsNotEmpty({ message: '分组id不能为空' })
  @IsString({ message: '分组id必须为字符串' })
  basicProjectInfoCategoryId: string;

  @ApiProperty({ description: '字段名称', example: 'xxxxxxxxx' })
  @IsNotEmpty({ message: '字段名称不能为空' })
  @IsString({ message: '字段名称必须为字符串' })
  name: string;

  @ApiProperty({ description: '字段类型', example: 'xxxxxxxxx' })
  @IsNotEmpty({ message: '字段类型不能为空' })
  @IsIn(Object.values(BasicProjectFieldType), {
    message: '字段类型必须是有效枚举值'
  })
  @IsString({ message: '字段类型必须为字符串' })
  type: string;

  @ApiProperty({ description: '是否必填', example: true })
  @IsOptional()
  @IsBoolean({ message: '是否必填必须为boolean值' })
  isRequired?: boolean;

  @ApiProperty({ description: '单位', example: 'xxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '单位必须为字符串' })
  unit?: string;

  @ApiProperty({ description: '枚举值', example: 'xxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '枚举值必须为字符串' })
  enumValue?: string;

  @ApiProperty({ description: '字段提示', example: 'xxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '字段提示必须为字符串' })
  placeholder?: string;

  @ApiProperty({ description: '字段含义', example: 'xxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '字段含义必须为字符串' })
  description?: string;
}

export class UpdateProjectBasicInfoFieldDetailDto extends CreateProjectBasicInfoFieldDetailDto {}

export class SaveProjectBasicInfoFieldValueList {
  @ApiProperty({ description: '字段值数组' })
  @IsArray({ message: '字段值必须为数组' })
  data: SaveProjectBasicInfoFieldValue[];
}

export class SaveProjectBasicInfoFieldValue {
  @ApiProperty({ description: '字段id', example: 'xxxxxxxxx' })
  @IsNotEmpty({ message: '字段id不能为空' })
  @IsString({ message: '字段id必须为字符串' })
  id: string;

  @ApiProperty({ description: '字段值', example: 'xxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '字段值必须为字符串' })
  value?: string;
}

export class MoveProjectBasicDetailDto {
  @ApiProperty({ description: 'id', default: 'xxxx' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '移动类型', default: 'up:上移，down：下移' })
  @IsNotEmpty({ message: '移动类型不能为空' })
  @IsString({ message: '移动类型必须是字符串' })
  moveType: 'up' | 'down';
}

export class UpdateProjectBasicDetailsEnableDto {
  @ApiProperty({
    description: '启用状态',
    example: true
  })
  @IsNotEmpty({ message: '启用状态不能为空' })
  @IsIn(Object.values(EnableStatus), {
    message: '启用状态必须是有效枚举值'
  })
  @IsString({ message: '启用状态必须为字符串' })
  status: string;
}
