import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsString } from 'class-validator';

export class CreateProjectBasicInfoCategoryDto {
  @ApiProperty({ description: '分组名称', example: 'xxxxxxxxx' })
  @IsNotEmpty({ message: '分组名称不能为空' })
  @IsString({ message: '分组名称必须为字符串' })
  name: string;
}

export class UpdateProjectBasicInfoCategoryDto extends CreateProjectBasicInfoCategoryDto {}

export class UpdateProjectBasicPublishTypeDto {
  @ApiProperty({
    description: '发布状态(发布:true,取消发布:false)',
    example: true
  })
  @IsNotEmpty({ message: '发布状态不能为空' })
  @IsBoolean({ message: '发布状态必须为布尔值' })
  isPublish: boolean;
}

export class MoveProjectBasicDto {
  @ApiProperty({ description: 'id', default: 'xxxx' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '移动类型', default: 'up:上移，down：下移' })
  @IsNotEmpty({ message: '移动类型不能为空' })
  @IsString({ message: '移动类型必须是字符串' })
  moveType: 'up' | 'down';
}
