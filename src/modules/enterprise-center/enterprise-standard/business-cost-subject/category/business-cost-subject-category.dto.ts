import { ApiProperty, PickType } from '@nestjs/swagger';
import {
  IsBoolean,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min
} from 'class-validator';

/** 基础Dto */
class BusinessCostSubjectCategoryBaseDto {
  @ApiProperty({ description: '版本id' })
  @IsNotEmpty({ message: '版本id不能为空' })
  @IsString({ message: '版本id必须是字符串' })
  businessCostSubjectVersionId: string;

  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '父级id', default: null })
  @IsOptional({ message: '父级id可以为空' })
  @IsString({ message: '父级id必须是字符串' })
  parentId: string | null = null;

  @ApiProperty({ description: '编码' })
  @IsNotEmpty({ message: '编码不能为空' })
  @IsString({ message: '编码必须是字符串' })
  code: string;

  @ApiProperty({ description: '名称' })
  @IsNotEmpty({ message: '名称不能为空' })
  @IsString({ message: '名称必须是字符串' })
  name: string;

  @ApiProperty({ description: '备注', default: null })
  @IsOptional({ message: '备注可以为空' })
  @IsString({ message: '备注必须是字符串' })
  remark: string | null = null;

  @ApiProperty({ description: '是否启用', default: true })
  @IsOptional({ message: '是否启用可以为空' })
  @IsBoolean({ message: '是否启用必须是布尔值' })
  isActive: boolean = true;

  @ApiProperty({ description: '排序', default: 1 })
  @IsOptional({ message: '排序可以为空' })
  @IsInt({ message: '排序必须是数字' })
  @Min(1, { message: '排序不能小于1' })
  sort: number = 1;
}

/** 创建数据Dto */
export class BusinessCostSubjectCategoryCreateDto extends PickType(
  BusinessCostSubjectCategoryBaseDto,
  [
    'businessCostSubjectVersionId',
    'parentId',
    'code',
    'name',
    'remark'
  ] as const
) {}

/** 更新数据Dto */
export class BusinessCostSubjectCategoryUpdateDto extends PickType(
  BusinessCostSubjectCategoryBaseDto,
  [
    'businessCostSubjectVersionId',
    'code',
    'name',
    'remark',
    'isActive'
  ] as const
) {}

/** 查询结果Dto */
export class BusinessCostSubjectCategoryResultDto extends BusinessCostSubjectCategoryBaseDto {}
