import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  BusinessCostSubjectVersionCreateDto,
  BusinessCostSubjectVersionResultDto,
  BusinessCostSubjectVersionSearchListDto,
  BusinessCostSubjectVersionUpdateDto
} from './business-cost-subject-version.dto';
import { BusinessCostSubjectVersionService } from './business-cost-subject-version.service';

@ApiTags('业务成本科目/版本')
@Controller('business-cost-subject-version')
export class BusinessCostSubjectVersionController {
  constructor(private readonly service: BusinessCostSubjectVersionService) {}

  @ApiOperation({
    summary: '获取版本列表',
    description: '获取版本列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取版本列表成功',
    type: BusinessCostSubjectVersionResultDto,
    isArray: true
  })
  @Get()
  async getList(
    @ReqUser() reqUser: IReqUser,
    @Query() query: BusinessCostSubjectVersionSearchListDto
  ) {
    const list = await this.service.getList(reqUser, query);
    return list;
  }

  @ApiOperation({
    summary: '创建版本',
    description: '创建版本'
  })
  @ApiResponse({
    status: 200,
    description: '创建版本成功',
    type: Boolean
  })
  @Post()
  async createOne(
    @Body() data: BusinessCostSubjectVersionCreateDto,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.createOne(reqUser, data);
    return true;
  }

  @ApiOperation({
    summary: '更新版本',
    description: '更新版本'
  })
  @ApiResponse({
    status: 200,
    description: '更新版本成功',
    type: Boolean
  })
  @Patch(':id')
  async updateOne(
    @Param('id') id: string,
    @Body() data: BusinessCostSubjectVersionUpdateDto,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.updateOne(reqUser, id, data);
    return true;
  }

  @ApiOperation({
    summary: '删除版本',
    description: '删除版本'
  })
  @ApiResponse({
    status: 200,
    description: '删除版本成功',
    type: Boolean
  })
  @Delete(':id')
  async deleteOne(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    await this.service.deleteOne(reqUser, id);
    return true;
  }
}
