import { ApiProperty, OmitType, PartialType, PickType } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';

import { EnableStatus } from '@/prisma/generated';

/** 基础Dto */
class BusinessCostSubjectVersionBaseDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '名称' })
  @IsNotEmpty({ message: '名称不能为空' })
  @IsString({ message: '名称必须是字符串' })
  name: string;

  @ApiProperty({ description: '启用状态' })
  @IsNotEmpty({ message: '启用状态不能为空' })
  @IsString({ message: '启用状态必须是字符串' })
  @IsEnum(EnableStatus, { message: '启用状态必须是枚举值' })
  status: EnableStatus;
}

/** 创建数据Dto */
export class BusinessCostSubjectVersionCreateDto extends PickType(
  BusinessCostSubjectVersionBaseDto,
  ['name'] as const
) {}

/** 更新数据Dto */
export class BusinessCostSubjectVersionUpdateDto extends PartialType(
  OmitType(BusinessCostSubjectVersionBaseDto, ['id'] as const)
) {}

/** 查询结果Dto */
export class BusinessCostSubjectVersionResultDto extends BusinessCostSubjectVersionBaseDto {
  @ApiProperty({ description: '项目引用数量' })
  projectRefCount: number;

  @ApiProperty({ description: '字典引用数量' })
  dictRefCount: number;
}

/**
 *查询已启用的版本
 */
export class BusinessCostSubjectVersionSearchListDto {
  @ApiProperty({ description: '状态' })
  @IsOptional({ message: '状态可以为空，当为空时查询所有版本' })
  @IsEnum(EnableStatus, { message: '启用状态必须是枚举值' })
  status?: EnableStatus;
}
