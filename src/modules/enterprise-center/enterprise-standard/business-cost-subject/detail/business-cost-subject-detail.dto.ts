import {
  ApiProperty,
  IntersectionType,
  PartialType,
  PickType
} from '@nestjs/swagger';
import {
  IsBoolean,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min
} from 'class-validator';

/** 基础Dto */
class BusinessCostSubjectDetailBaseDto {
  @ApiProperty({ description: '版本id' })
  @IsNotEmpty({ message: '版本id不能为空' })
  @IsString({ message: '版本id必须是字符串' })
  businessCostSubjectVersionId: string;

  @ApiProperty({ description: '分类id' })
  @IsNotEmpty({ message: '分类id不能为空' })
  @IsString({ message: '分类id必须是字符串' })
  businessCostSubjectCategoryId: string;

  @ApiProperty({ description: '财务成本科目id', default: null })
  @IsString({ message: '财务成本科目id必须是字符串' })
  financialCostSubjectId: string;

  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '编码' })
  @IsNotEmpty({ message: '编码不能为空' })
  @IsString({ message: '编码必须是字符串' })
  code: string;

  @ApiProperty({ description: '名称' })
  @IsNotEmpty({ message: '名称不能为空' })
  @IsString({ message: '名称必须是字符串' })
  name: string;

  @ApiProperty({ description: '单位', default: null })
  @IsOptional({ message: '单位可以为空' })
  @IsString({ message: '单位必须是字符串' })
  unit: string;

  @ApiProperty({ description: '费用类别', default: null })
  @IsOptional({ message: '费用类别可以为空' })
  @IsString({ message: '费用类别必须是字符串' })
  expenseCategory: string;

  @ApiProperty({ description: '核算说明', default: null })
  @IsOptional({ message: '核算说明可以为空' })
  @IsString({ message: '核算说明必须是字符串' })
  accountingDescription: string;

  @ApiProperty({ description: '安全施工费', default: false })
  @IsOptional({ message: '安全施工费可以为空' })
  @IsBoolean({ message: '安全施工费必须是布尔值' })
  isSafetyConstructionFee: boolean = false;

  @ApiProperty({ description: '科目对照说明', default: null })
  @IsOptional({ message: '科目对照说明可以为空' })
  @IsString({ message: '科目对照说明必须是字符串' })
  subjectMappingDescription: string;

  @ApiProperty({ description: '财务成本科目名称', default: null })
  @IsOptional({ message: '财务成本科目名称可以为空' })
  @IsString({ message: '财务成本科目名称必须是字符串' })
  financialCostSubjectName: string;

  @ApiProperty({ description: '排序', default: 1 })
  @IsOptional({ message: '排序可以为空' })
  @IsInt({ message: '排序必须是数字' })
  @Min(1, { message: '排序不能小于1' })
  sort: number = 1;

  @ApiProperty({ description: '是否启用', default: true })
  @IsOptional({ message: '是否启用可以为空' })
  @IsBoolean({ message: '是否启用必须是布尔值' })
  isActive: boolean = true;
}

/** 创建数据Dto */
export class BusinessCostSubjectDetailCreateDto extends PickType(
  BusinessCostSubjectDetailBaseDto,
  [
    'businessCostSubjectVersionId',
    'businessCostSubjectCategoryId',
    'financialCostSubjectId',
    'code',
    'name',
    'unit',
    'expenseCategory',
    'accountingDescription',
    'isSafetyConstructionFee',
    'subjectMappingDescription',
    'financialCostSubjectName'
  ] as const
) {}

/** 更新数据Dto */
export class BusinessCostSubjectDetailUpdateDto extends IntersectionType(
  PickType(BusinessCostSubjectDetailBaseDto, [
    'businessCostSubjectVersionId',
    'businessCostSubjectCategoryId'
  ] as const),
  PartialType(
    PickType(BusinessCostSubjectDetailBaseDto, [
      'financialCostSubjectId',
      'code',
      'name',
      'unit',
      'expenseCategory',
      'accountingDescription',
      'isSafetyConstructionFee',
      'subjectMappingDescription',
      'financialCostSubjectName',
      'isActive'
    ] as const)
  )
) {}

/** 查询结果Dto */
export class BusinessCostSubjectDetailResultDto extends BusinessCostSubjectDetailBaseDto {}

/** 获取明细树(分类+明细)结果Dto */
export class BusinessCostSubjectDetailTreeResultDto {
  @ApiProperty({ description: 'id' })
  id: string;

  @ApiProperty({
    description: '父级id',
    required: false,
    nullable: true
  })
  @IsOptional()
  parentId?: string;

  @ApiProperty({ description: '编码' })
  code: string;

  @ApiProperty({ description: '名称' })
  name: string;

  @ApiProperty({
    description: '子级',
    type: BusinessCostSubjectDetailTreeResultDto,
    isArray: true,
    required: false,
    nullable: true
  })
  @IsOptional()
  children?: BusinessCostSubjectDetailTreeResultDto[];
}

/** 查询列表接口query参数Dto */
export class BusinessCostSubjectDetailQueryListDto {
  @ApiProperty({ description: '版本id' })
  @IsNotEmpty({ message: '版本id不能为空' })
  @IsString({ message: '版本id必须是字符串' })
  businessCostSubjectVersionId: string;

  @ApiProperty({ description: '分类id，查询全部明细时，传固定字符串："100"' })
  @IsNotEmpty({ message: '分类id不能为空' })
  @IsString({ message: '分类id必须是字符串' })
  businessCostSubjectCategoryId: string;

  @ApiProperty({ description: '名称' })
  @IsOptional({ message: '名称可以为空，当为空时查询全部明细' })
  @IsString({ message: '名称必须是字符串' })
  name?: string;
}
