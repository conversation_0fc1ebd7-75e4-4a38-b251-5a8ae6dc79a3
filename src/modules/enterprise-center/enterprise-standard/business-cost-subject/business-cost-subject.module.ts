import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';

import { PlatformModule } from '../../../platform/platform.module';
import { BusinessCostSubjectCategoryController } from './category/business-cost-subject-category.controller';
import { BusinessCostSubjectCategoryService } from './category/business-cost-subject-category.service';
import { BusinessCostSubjectDetailController } from './detail/business-cost-subject-detail.controller';
import { BusinessCostSubjectDetailService } from './detail/business-cost-subject-detail.service';
import { BusinessCostSubjectVersionController } from './version/business-cost-subject-version.controller';
import { BusinessCostSubjectVersionService } from './version/business-cost-subject-version.service';

@Module({
  imports: [PrismaModule, PlatformModule],
  controllers: [
    BusinessCostSubjectVersionController,
    BusinessCostSubjectCategoryController,
    BusinessCostSubjectDetailController
  ],
  providers: [
    BusinessCostSubjectVersionService,
    BusinessCostSubjectCategoryService,
    BusinessCostSubjectDetailService
  ]
})
export class BusinessCostSubjectModule {}
