import { BadRequestException, Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { BusinessBaseInfoVersionStatus } from '@/prisma/generated';

import {
  CreateBusinessBaseInfoVersionDto,
  SaveBusinessBaseInfoDto
} from './business-base-info.dto';

@Injectable()
export class BusinessBaseInfoService {
  constructor(private readonly prisma: PrismaService) {}

  private getDiff(
    current: Record<string, any>,
    previous?: Record<string, any>
  ) {
    const resultArr: Array<{ key: any; from: any; to: any }> = [];
    for (const key in current) {
      if (current[key] !== previous?.[key]) {
        resultArr.push({
          key: key,
          from: previous?.[key] ?? null,
          to: current[key]
        });
      }
    }
    return resultArr;
  }

  /**
   * 新增公司基本信息版本
   * @param ceateBusinessBaseInfoDto
   * @returns
   */
  async createVersion(
    ceateBusinessBaseInfoDto: CreateBusinessBaseInfoVersionDto,
    reqUser: IReqUser
  ) {
    const { companyVersion, remark } = ceateBusinessBaseInfoDto;
    const tenantId = reqUser.tenantId;
    const orgId = reqUser.orgId;
    const operatorId = reqUser.id;
    // 判断当前是否重复
    const existBusinessBaseInfo = await this.prisma.businessBaseInfo.findFirst({
      where: {
        companyVersion,
        tenantId,
        orgId,
        isDeleted: false
      }
    });
    if (existBusinessBaseInfo) {
      throw new BadRequestException('名称重复');
    }
    // 查找当前最大 sort
    const maxSortRow = await this.prisma.businessBaseInfo.findFirst({
      where: { tenantId, orgId, isDeleted: false },
      orderBy: { sort: 'desc' }
    });
    const sort = maxSortRow ? maxSortRow.sort + 1 : 1;

    const versionInfo = await this.prisma.businessBaseInfo.create({
      data: {
        tenantId,
        orgId,
        companyVersion,
        remark,
        createBy: operatorId,
        updateBy: operatorId,
        sort
      }
    });

    return versionInfo;
  }

  /**
   * 修改公司基本信息版本
   * @param ceateBusinessBaseInfoDto
   * @returns
   */
  async editVersion(
    id: string,
    ceateBusinessBaseInfoDto: CreateBusinessBaseInfoVersionDto,
    reqUser: IReqUser
  ) {
    const { companyVersion, remark } = ceateBusinessBaseInfoDto;
    const operatorId = reqUser.id;
    const tenantId = reqUser.tenantId;
    const orgId = reqUser.orgId;
    // 判断当前是否重复
    const existBusinessBaseInfo = await this.prisma.businessBaseInfo.findFirst({
      where: {
        companyVersion,
        tenantId,
        orgId,
        isDeleted: false
      }
    });
    if (existBusinessBaseInfo) {
      throw new BadRequestException('名称重复');
    }

    const versionInfo = await this.prisma.businessBaseInfo.update({
      where: {
        id,
        isDeleted: false
      },
      data: {
        companyVersion,
        remark,
        updateBy: operatorId
      }
    });

    return versionInfo;
  }

  /**
   * 保存公司基本信息
   * @param saveBusinessBaseInfoDto
   * @returns
   */
  async saveBussionInfo(
    id: string,
    saveBusinessBaseInfoDto: SaveBusinessBaseInfoDto,
    reqUser: IReqUser
  ) {
    const {
      companyName,
      unifiedSocialCreditCode,
      registeredAddress,
      companyLocation,
      taxpayerType,
      businessPhone,
      bankName,
      bankAddress,
      bankAccount
    } = saveBusinessBaseInfoDto;
    const operatorId = reqUser.id;
    const existBusinessBaseInfo = await this.prisma.businessBaseInfo.findFirst({
      where: {
        id,
        isDeleted: false
      }
    });
    if (!existBusinessBaseInfo) {
      throw new BadRequestException('数据不存在');
    }
    if (
      existBusinessBaseInfo.status === BusinessBaseInfoVersionStatus.PUBLISHED
    ) {
      throw new BadRequestException('当前状态不可发布');
    }

    const updateInfo = await this.prisma.businessBaseInfo.update({
      where: {
        id,
        isDeleted: false
      },
      data: {
        companyName,
        unifiedSocialCreditCode,
        registeredAddress,
        companyLocation,
        taxpayerType,
        businessPhone,
        bankName,
        bankAddress,
        bankAccount,
        status: BusinessBaseInfoVersionStatus.SUBMITTED,
        updateBy: operatorId
      }
    });
    return updateInfo;
  }

  /**
   * 发布公司基本信息
   * @param publishBusinessBaseInfoDto
   * @returns
   */
  async publishBussionInfo(id: string, reqUser: IReqUser) {
    const tenantId = reqUser.tenantId;
    const orgId = reqUser.orgId;
    const operatorId = reqUser.id;
    const operatorName = reqUser.nickname;
    const curBusinessBaseInfo = await this.prisma.businessBaseInfo.findFirst({
      where: {
        id,
        isDeleted: false
      },
      select: {
        companyName: true,
        unifiedSocialCreditCode: true,
        registeredAddress: true,
        companyLocation: true,
        taxpayerType: true,
        businessPhone: true,
        bankName: true,
        bankAddress: true,
        bankAccount: true,
        status: true
      }
    });
    if (!curBusinessBaseInfo) {
      throw new BadRequestException('企业基本信息不存在');
    }
    if (curBusinessBaseInfo.status == BusinessBaseInfoVersionStatus.DRAFT) {
      throw new BadRequestException('请先保存');
    }
    if (!curBusinessBaseInfo.companyName) {
      throw new BadRequestException('企业名称不存在');
    }
    if (!curBusinessBaseInfo.unifiedSocialCreditCode) {
      throw new BadRequestException('统一社会信用代码不存在');
    }
    // 添加一份快照
    const lastVersionRecord =
      await this.prisma.businessBaseInfoPublish.findFirst({
        // 上一份快照的数据
        where: {
          originInfoId: id,
          isDeleted: false
        },
        select: {
          companyName: true,
          unifiedSocialCreditCode: true,
          registeredAddress: true,
          companyLocation: true,
          taxpayerType: true,
          businessPhone: true,
          bankName: true,
          bankAddress: true,
          bankAccount: true,
          versionNumber: true
        },
        orderBy: { versionNumber: 'desc' }
      });

    const {
      companyName,
      unifiedSocialCreditCode,
      registeredAddress,
      companyLocation,
      taxpayerType,
      businessPhone,
      bankName,
      bankAddress,
      bankAccount
    } = curBusinessBaseInfo;
    const versionNumber = lastVersionRecord
      ? lastVersionRecord.versionNumber + 1
      : 1;

    const shortCutData = await this.prisma.businessBaseInfoPublish.create({
      data: {
        tenantId,
        orgId,
        createBy: operatorId,
        updateBy: operatorId,

        originInfoId: id,
        companyName,
        unifiedSocialCreditCode,
        registeredAddress,
        companyLocation,
        taxpayerType,
        businessPhone,
        bankName,
        bankAddress,
        bankAccount,

        versionNumber
      }
    });
    // 添加变更记录
    if (lastVersionRecord) {
      const diffData = this.getDiff(curBusinessBaseInfo, lastVersionRecord);
      // 包含在keys里的字段才产生对比
      const keys = [
        'companyName',
        'unifiedSocialCreditCode',
        'registeredAddress',
        'companyLocation',
        'taxpayerType',
        'businessPhone',
        'bankName',
        'bankAddress',
        'bankAccount'
      ];
      const logData = diffData
        .filter((v) => keys.includes(v.key))
        .map((v) => {
          return {
            tenantId,
            orgId,
            originInfoId: id,
            createBy: operatorId,
            updateBy: operatorId,

            versionNumber,
            createByName: operatorName,
            fieldKey: v.key,
            oldValue: v.from,
            newValue: v.to
          };
        });
      await this.prisma.businessBaseInfoChangeLog.createMany({
        data: logData
      });
    }
    // 修改状态
    const updateInfo = await this.prisma.businessBaseInfo.update({
      where: { id, isDeleted: false },
      data: {
        status: BusinessBaseInfoVersionStatus.PUBLISHED,
        updateBy: operatorId
      }
    });
    return updateInfo;
  }

  /**
   * 取消发布公司基本信息
   * @param publishBusinessBaseInfoDto
   * @returns
   */
  async cancelPublishBussionInfo(id: string, reqUser: IReqUser) {
    const operatorId = reqUser.id;
    // 修改状态
    const updateInfo = await this.prisma.businessBaseInfo.update({
      where: { id, isDeleted: false },
      data: {
        status: BusinessBaseInfoVersionStatus.DRAFT,
        updateBy: operatorId
      }
    });
    return updateInfo;
  }

  /**
   * 查询公司基本信息版本列表
   * @param ceateBusinessBaseInfoDto
   * @returns
   */
  async getList(reqUser: IReqUser) {
    const orgId = reqUser.orgId;
    const tenantId = reqUser.tenantId;
    const data = await this.prisma.businessBaseInfo.findMany({
      where: {
        orgId,
        tenantId,
        isDeleted: false
      },
      orderBy: [{ sort: 'asc' }, { createAt: 'asc' }]
    });

    return data;
  }

  /**
   * 查询修改记录列表
   * @param queryChangeLogListDto
   * @returns
   */
  async getChangeLogList(id: string, reqUser: IReqUser) {
    const tenantId = reqUser.tenantId;
    const orgId = reqUser.orgId;
    const record = await this.prisma.businessBaseInfoChangeLog.findMany({
      where: {
        tenantId,
        orgId,
        originInfoId: id,
        isDeleted: false
      },
      orderBy: [{ createAt: 'asc' }, { versionNumber: 'desc' }]
    });
    return record;
  }

  /**
   * 删除企业基本信息
   * @param id
   * @returns
   */
  async deleteItem(id: string, reqUser: IReqUser) {
    const tenantId = reqUser.tenantId;
    const orgId = reqUser.orgId;
    // 被合同编辑引用后的不可删除
    const existData = await this.prisma.materialContract.findFirst({
      where: {
        OR: [
          {
            partyB: id
          },
          {
            partyA: id
          }
        ],
        isDeleted: false,
        tenantId
      }
    });
    if (existData) {
      throw new BadRequestException('已被合同模块引用，不可删除');
    }
    const existBusinessBaseInfo = await this.prisma.businessBaseInfo.findFirst({
      where: {
        id,
        isDeleted: false
      }
    });
    if (
      existBusinessBaseInfo?.status == BusinessBaseInfoVersionStatus.PUBLISHED
    ) {
      throw new BadRequestException('当前数据已发布,不可删除!');
    }

    // 级联删除
    await this.prisma.$transaction([
      this.prisma.businessBaseInfo.update({
        where: { id, isDeleted: false },
        data: {
          updateBy: reqUser.id,
          isDeleted: true
        }
      }),
      this.prisma.businessBaseInfoPublish.updateMany({
        where: { originInfoId: id, isDeleted: false },
        data: {
          updateBy: reqUser.id,
          isDeleted: true
        }
      }),
      this.prisma.businessBaseInfoChangeLog.updateMany({
        where: {
          tenantId,
          orgId,
          originInfoId: id,
          isDeleted: false
        },
        data: {
          updateBy: reqUser.id,
          isDeleted: true
        }
      })
    ]);

    // await this.prisma.businessBaseInfo.update({
    //   where: { id, isDeleted: false },
    //   data: {
    //     updateBy: reqUser.id,
    //     isDeleted: true
    //   }
    // });

    return true;
  }

  // 上移
  async up(id: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    // 查询当前公司基本信息数据
    const current = await this.prisma.businessBaseInfo.findFirst({
      where: {
        id,
        isDeleted: false
      }
    });
    if (!current) {
      throw new BadRequestException('当前公司基本信息不存在');
    }
    // 查询上一行（sort 小于当前行，最大的那一行）
    const prev = await this.prisma.businessBaseInfo.findFirst({
      where: {
        orgId,
        tenantId,
        isDeleted: false,
        sort: { lt: current.sort }
      },
      orderBy: { sort: 'desc' }
    });

    if (!prev) throw new BadRequestException('已经是最顶部，无法上移');
    // 交换 sort
    await this.prisma.$transaction([
      this.prisma.businessBaseInfo.update({
        where: { id: current.id, isDeleted: false },
        data: { sort: prev.sort, updateBy: reqUser.id }
      }),
      this.prisma.businessBaseInfo.update({
        where: { id: prev.id, isDeleted: false },
        data: { sort: current.sort, updateBy: reqUser.id }
      })
    ]);
  }

  // 下移
  async down(id: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    // 查询当前行
    const current = await this.prisma.businessBaseInfo.findFirst({
      where: { id, isDeleted: false }
    });
    if (!current) throw new BadRequestException('数据不存在');

    // 查询下一行（sort 大于当前行，最小的那一行）
    const next = await this.prisma.businessBaseInfo.findFirst({
      where: {
        orgId,
        tenantId,
        isDeleted: false,
        sort: { gt: current.sort }
      },
      orderBy: { sort: 'asc' }
    });
    if (!next) throw new BadRequestException('已经是最底部，无法下移');

    // 交换 sort
    await this.prisma.$transaction([
      this.prisma.businessBaseInfo.update({
        where: { id: current.id, isDeleted: false },
        data: { sort: next.sort, updateBy: reqUser.id }
      }),
      this.prisma.businessBaseInfo.update({
        where: { id: next.id, isDeleted: false },
        data: { sort: current.sort, updateBy: reqUser.id }
      })
    ]);
    return true;
  }
}
