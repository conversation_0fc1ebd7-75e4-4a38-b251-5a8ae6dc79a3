import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  CreateBusinessBaseInfoVersionDto,
  EditMoveDto,
  SaveBusinessBaseInfoDto
} from './business-base-info.dto';
import { BusinessBaseInfoService } from './business-base-info.service';

@ApiTags('公司基本信息')
@Controller('business-base-info')
export class BusinessBaseInfoController {
  constructor(private readonly service: BusinessBaseInfoService) {}

  @ApiOperation({
    summary: '新增公司基本信息版本',
    description: '新增公司基本信息版本'
  })
  @ApiResponse({ status: 200, description: '新增公司基本信息版本成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/add')
  async create(
    @Body() data: CreateBusinessBaseInfoVersionDto,
    @ReqUser() reqUser: IReqUser
  ) {
    return await this.service.createVersion(data, reqUser);
  }

  @ApiOperation({
    summary: '新增公司基本信息版本',
    description: '新增公司基本信息版本'
  })
  @ApiResponse({ status: 200, description: '新增公司基本信息版本成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/edit/:id')
  async update(
    @Param('id') id: string,
    @Body() data: CreateBusinessBaseInfoVersionDto,
    @ReqUser() reqUser: IReqUser
  ) {
    return await this.service.editVersion(id, data, reqUser);
  }

  @ApiOperation({
    summary: '保存公司基本信息',
    description: '保存公司基本信息'
  })
  @ApiResponse({ status: 200, description: '提交成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Patch('/save/:id')
  async save(
    @Param('id') id: string,
    @Body() data: SaveBusinessBaseInfoDto,
    @ReqUser() reqUser: IReqUser
  ) {
    return await this.service.saveBussionInfo(id, data, reqUser);
  }

  @ApiOperation({
    summary: '发布公司基本信息',
    description: '发布公司基本信息'
  })
  @ApiResponse({ status: 200, description: '提交成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Patch('/publish/:id')
  async publish(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    return await this.service.publishBussionInfo(id, reqUser);
  }

  @ApiOperation({
    summary: '取消发布公司基本信息',
    description: '取消发布公司基本信息'
  })
  @ApiResponse({ status: 200, description: '提交成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Patch('/cancel-publish/:id')
  async cancelPublish(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    return await this.service.cancelPublishBussionInfo(id, reqUser);
  }

  @ApiOperation({
    summary: '获取公司基本信息版本列表',
    description: '获取公司基本信息版本列表'
  })
  @ApiResponse({ status: 200, description: '获取分类列表成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/list')
  async getList(@ReqUser() reqUser: IReqUser) {
    return await this.service.getList(reqUser);
  }

  @ApiOperation({
    summary: '获取公司基本信息变更记录',
    description: '获取公司基本信息变更记录'
  })
  @ApiResponse({ status: 200, description: '获取分类列表成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/change-log-list/:id')
  async getChangLog(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    return await this.service.getChangeLogList(id, reqUser);
  }

  @ApiOperation({
    summary: '修改上移下移',
    description: '修改上移下移'
  })
  @ApiResponse({ status: 200, description: '修改上移下移成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/edit/move')
  async editMove(@ReqUser() reqUser: IReqUser, @Body() data: EditMoveDto) {
    const { id, moveType } = data;
    if (moveType === 'up') {
      // 上移
      await this.service.up(id, reqUser);
    } else {
      // 下移
      await this.service.down(id, reqUser);
    }
    return true;
  }

  @ApiOperation({
    summary: '删除',
    description: '删除'
  })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Delete('/delete/:id')
  async del(@ReqUser() reqUser: IReqUser, @Param('id') id: string) {
    return await this.service.deleteItem(id, reqUser);
  }
}
