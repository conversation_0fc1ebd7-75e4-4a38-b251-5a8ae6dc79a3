import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class CreateBusinessBaseInfoVersionDto {
  @ApiProperty({ description: '公司版本', default: 'xxxx' })
  @IsNotEmpty({ message: '公司版本不能为空' })
  @IsString({ message: '公司版本必须是字符串' })
  companyVersion: string;

  @ApiProperty({ description: '备注', default: 'xxxx' })
  @IsString({ message: '备注必须是字符串' })
  remark: string;
}
export class SaveBusinessBaseInfoDto {
  @ApiProperty({ description: '公司名称信息Id', default: 'xxxx' })
  @IsNotEmpty({ message: '公司名称信息Id不能为空' })
  @IsString({ message: '公司名称信息Id必须是字符串' })
  id: string;

  @ApiProperty({ description: '公司名称', default: 'xxxx' })
  @IsNotEmpty({ message: '公司名称不能为空' })
  @IsString({ message: '公司名称必须是字符串' })
  companyName: string;

  @ApiProperty({ description: '统一社会信用代码', default: 'xxxx' })
  @IsNotEmpty({ message: '统一社会信用代码不能为空' })
  @IsString({ message: '统一社会信用代码必须是字符串' })
  unifiedSocialCreditCode: string;

  @ApiProperty({ description: '注册地址', default: 'xxxx' })
  @IsString({ message: '注册地址必须是字符串' })
  registeredAddress?: string;
  @ApiProperty({ description: '企业所在地', default: 'xxxx' })
  @IsString({ message: '企业所在地必须是字符串' })
  companyLocation?: string;
  @ApiProperty({ description: '纳税人身份', default: 'xxxx' })
  @IsString({ message: '纳税人身份必须是字符串' })
  taxpayerType?: string;
  @ApiProperty({ description: '业务电话', default: 'xxxx' })
  @IsString({ message: '业务电话必须是字符串' })
  businessPhone?: string;
  @ApiProperty({ description: '开户银行', default: 'xxxx' })
  @IsString({ message: '开户银行必须是字符串' })
  bankName?: string;
  @ApiProperty({ description: '开户地址', default: 'xxxx' })
  @IsString({ message: '开户地址必须是字符串' })
  bankAddress?: string;
  @ApiProperty({ description: '开户账号', default: 'xxxx' })
  @IsString({ message: '开户账号必须是字符串' })
  bankAccount?: string;
}

export class PublishBusinessBaseInfoDto {
  @ApiProperty({ description: '公司名称信息Id', default: 'xxxx' })
  @IsNotEmpty({ message: '公司名称信息Id不能为空' })
  @IsString({ message: '公司名称信息Id必须是字符串' })
  id: string;
}

export class QueryChangeLogListDto {
  @ApiProperty({ description: '公司名称信息Id', default: 'xxxx' })
  @IsNotEmpty({ message: '公司名称信息Id不能为空' })
  @IsString({ message: '公司名称信息Id必须是字符串' })
  id: string;
}

export class EditMoveDto {
  @ApiProperty({ description: 'id', default: 'xxxx' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '移动类型', default: 'up:上移，down：下移' })
  @IsNotEmpty({ message: '移动类型不能为空' })
  @IsString({ message: '移动类型必须是字符串' })
  moveType: 'up' | 'down';
}
