import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';

import { BusinessBaseInfoController } from './business-base-info/business-base-info.controller';
import { BusinessBaseInfoService } from './business-base-info/business-base-info.service';

@Module({
  imports: [PrismaModule],
  controllers: [BusinessBaseInfoController],
  providers: [BusinessBaseInfoService]
})
export class BusinessBaseInfoModule {}
