import { ApiProperty, OmitType, PartialType, PickType } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';

import { EnableStatus } from '@/prisma/generated';
/** 基础Dto */
class CostDictionaryVersionBaseDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '名称' })
  @IsNotEmpty({ message: '名称不能为空' })
  @IsString({ message: '名称必须是字符串' })
  name: string;

  @ApiProperty({ description: '业务成本科目版本id' })
  @IsOptional()
  @IsString({ message: 'id必须是字符串' })
  businessCostSubjectVersionId: string;

  @ApiProperty({ description: '启用状态' })
  @IsNotEmpty({ message: '启用状态不能为空' })
  @IsString({ message: '启用状态必须是字符串' })
  @IsEnum(EnableStatus, { message: '启用状态必须是枚举值' })
  status: EnableStatus;
}

/**创建数据Dto */
export class CostDictionaryVersionCreateDto extends PickType(
  CostDictionaryVersionBaseDto,
  ['name', 'businessCostSubjectVersionId'] as const
) {}

/**更新数据Dto */
export class CostDictionaryVersionUpdateDto extends PartialType(
  OmitType(CostDictionaryVersionBaseDto, ['id'] as const)
) {}

/** 查询结果Dto */
export class CostDictionaryVersionResultDto extends CostDictionaryVersionBaseDto {
  @ApiProperty({ description: '项目引用数量' })
  projectRefCount: number;
}
