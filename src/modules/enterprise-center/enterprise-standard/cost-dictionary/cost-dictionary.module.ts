import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';

import { PlatformModule } from '../../../platform/platform.module';
import { BusinessCostSubjectVersionService } from '../business-cost-subject/version/business-cost-subject-version.service';
import { CostDictionaryCategoryController } from './category/cost-dictionary-category.controller';
import { CostDictionaryCategoryService } from './category/cost-dictionary-category.service';
import { CostDictionaryDetailController } from './detail/cost-dictionary-detail.controller';
import { CostDictionaryDetailService } from './detail/cost-dictionary-detail.service';
import { CostDictionaryVersionController } from './version/cost-dictionary-version.controller';
import { CostDictionaryVersionService } from './version/cost-dictionary-version.service';

@Module({
  controllers: [
    CostDictionaryVersionController,
    CostDictionaryCategoryController,
    CostDictionaryDetailController
  ],
  providers: [
    CostDictionaryVersionService,
    CostDictionaryCategoryService,
    CostDictionaryDetailService,
    BusinessCostSubjectVersionService
  ],
  imports: [PrismaModule, PlatformModule]
})
export class CostDictionaryModule {}
