import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { MoveToEnum } from '@/common/enums/common.enum';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  CostDictionaryCategoryCreateDto,
  CostDictionaryCategoryResultDto,
  CostDictionaryCategoryUpdateDto
} from './cost-dictionary-category.dto';
import { CostDictionaryCategoryService } from './cost-dictionary-category.service';

@ApiTags('费用字典/分类')
@Controller('cost-dictionary-category')
export class CostDictionaryCategoryController {
  constructor(private readonly service: CostDictionaryCategoryService) {}

  @ApiOperation({
    summary: '获取分类列表',
    description: '获取分类列表'
  })
  @ApiQuery({
    name: 'versionId',
    description: '版本id',
    required: true,
    type: String
  })
  @ApiResponse({
    status: 200,
    description: '获取分类列表成功',
    type: CostDictionaryCategoryResultDto,
    isArray: true
  })
  @Get()
  async getList(
    @Query('versionId') versionId: string,
    @ReqUser() reqUser: IReqUser
  ) {
    const list = await this.service.getList(reqUser, versionId);
    return list;
  }

  @ApiOperation({
    summary: '创建分类',
    description: '创建分类'
  })
  @ApiResponse({
    status: 200,
    description: '创建分类成功',
    type: CostDictionaryCategoryResultDto
  })
  @Post()
  async createOne(
    @Body() data: CostDictionaryCategoryCreateDto,
    @ReqUser() reqUser: IReqUser
  ) {
    const category = await this.service.createOne(data, reqUser);
    return category;
  }

  @ApiOperation({
    summary: '更新分类',
    description: '更新分类'
  })
  @ApiResponse({
    status: 200,
    description: '更新分类成功',
    type: Boolean
  })
  @Patch(':id')
  async updateOne(
    @Param('id') id: string,
    @Body() data: CostDictionaryCategoryUpdateDto,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.updateOne(id, data, reqUser);
    return true;
  }

  @ApiOperation({
    summary: '删除分类',
    description: '删除分类'
  })
  @ApiResponse({
    status: 200,
    description: '删除分类成功',
    type: Boolean
  })
  @Delete(':id')
  async deleteOne(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    await this.service.deleteOne(id, reqUser);
    return true;
  }

  @ApiOperation({
    summary: '移动分类',
    description: '移动分类'
  })
  @ApiQuery({
    name: 'moveTo',
    description: '移动至',
    required: true,
    enum: MoveToEnum
  })
  @ApiResponse({
    status: 200,
    description: '移动分类成功',
    type: Boolean
  })
  @Patch(':id/_move')
  async moveOne(
    @Param('id') id: string,
    @Query('moveTo') moveTo: MoveToEnum,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.moveOne(reqUser, id, moveTo);
    return true;
  }
}
