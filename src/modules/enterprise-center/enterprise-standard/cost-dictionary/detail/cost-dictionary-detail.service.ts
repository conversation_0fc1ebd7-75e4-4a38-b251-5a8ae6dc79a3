import * as infraCloudSdk from '@ewing/infra-cloud-sdk';
import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable
} from '@nestjs/common';
import { isEmpty as _isEmpty } from 'lodash';

import { MoveToEnum } from '@/common/enums/common.enum';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { EnableStatus } from '@/prisma/generated';
import { CostDictionaryDetailWhereInput } from '@/prisma/generated/models';

import {
  CostDictionaryDetailCreateDto,
  CostDictionaryDetailQueryParamDto,
  CostDictionaryDetailResultDto,
  CostDictionaryDetailUpdateDto
} from './cost-dictionary-detail.dto';

@Injectable()
export class CostDictionaryDetailService {
  constructor(private readonly prisma: PrismaService) {}

  async getList(
    reqUser: IReqUser,
    queryParam: CostDictionaryDetailQueryParamDto
  ) {
    const { tenantId, orgId } = reqUser;
    const { costDictionaryVersionId, costDictionaryCategoryId, name } =
      queryParam;

    const where: CostDictionaryDetailWhereInput = {
      tenantId,
      orgId,
      costDictionaryVersionId,
      isDeleted: false
    };
    // 查询所有明细时，不按分类进行过滤
    if (costDictionaryCategoryId !== '100') {
      where.costDictionaryCategory = {
        fullId: { contains: costDictionaryCategoryId }
      };
    }
    // 按名称搜索时
    if (name) {
      where.name = { contains: name };
    }

    const categoryList = await this.prisma.costDictionaryCategory.findMany({
      select: {
        id: true,
        parentId: true,
        level: true,
        sort: true,
        isActive: true
      },
      where: {
        tenantId,
        orgId,
        costDictionaryVersionId,
        fullId:
          costDictionaryCategoryId !== '100'
            ? { contains: costDictionaryCategoryId }
            : undefined
      },
      orderBy: [{ isActive: 'desc' }, { sort: 'asc' }]
    });
    let detailList = await this.prisma.costDictionaryDetail.findMany({
      select: {
        id: true,
        costDictionaryVersionId: true,
        costDictionaryCategoryId: true,
        code: true,
        name: true,
        remark: true,
        accountExplanation: true,
        isActive: true,
        sort: true,
        costDictionaryCategory: {
          select: {
            isActive: true
          }
        },
        costDetailBusinessCostSubjectDetail: {
          select: {
            businessCostSubjectDetailId: true
          },
          where: {
            isDeleted: false
          }
        }
      },
      where,
      orderBy: [{ isActive: 'desc' }, { sort: 'asc' }]
    });
    detailList = detailList.map((item) => ({
      ...item,
      parentId: item.costDictionaryCategoryId,
      businessCostSubjectDetailsIds:
        item.costDetailBusinessCostSubjectDetail.map(
          (id) => id.businessCostSubjectDetailId
        )
    }));

    const { parentRecords: rootNodes } = infraCloudSdk.initTree([
      ...categoryList,
      ...detailList
    ] as any);
    let list = infraCloudSdk
      .flattenTree(rootNodes, { traversal: 'depth' })
      .filter((item) => item.isLeaf && item.costDictionaryCategoryId != null);
    // 将“废弃”数据排在最后
    list = [
      ...list.filter((item) => item.isActive),
      ...list.filter((item) => !item.isActive)
    ];

    return list as any as CostDictionaryDetailResultDto[];
  }

  async createOne(reqUser: IReqUser, data: CostDictionaryDetailCreateDto) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 校验启用状态
    await this.checkVersionStatus(data.costDictionaryVersionId);

    await this.checkUnique({ tenantId, orgId, data });

    const maxSort = await this.prisma.costDictionaryDetail.aggregate({
      _max: {
        sort: true
      },
      where: {
        tenantId,
        orgId,
        costDictionaryVersionId: data.costDictionaryVersionId,
        costDictionaryCategoryId: data.costDictionaryCategoryId,
        isDeleted: false
      }
    });
    const { businessCostSubjectDetailsIds, ...detailData } = data;
    // 创建明细数据
    const detail = await this.prisma.costDictionaryDetail.create({
      data: {
        ...detailData,
        sort: maxSort._max.sort ? maxSort._max.sort + 1 : 1,
        tenantId,
        orgId,
        createBy: userId,
        updateBy: userId
      }
    });

    // 写入中间表
    if (businessCostSubjectDetailsIds?.length) {
      await this.prisma.costDetailBusinessCostSubjectDetail.createMany({
        data: businessCostSubjectDetailsIds.map((item) => {
          return {
            tenantId,
            orgId,
            createBy: userId,
            costDictionaryDetailId: item,
            businessCostSubjectDetailId: detail.id,
            updateBy: userId
          };
        })
      });
    }

    return detail;
  }

  // 查询对应的版本
  async checkVersionStatus(id: string) {
    const version = await this.prisma.costDictionaryVersion.findFirst({
      select: {
        status: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
    if (version?.status === EnableStatus.ENABLED) {
      throw new HttpException(
        `版本已启用，无法进行操作`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async getOne(id: string) {
    return await this.prisma.costDictionaryDetail.findUnique({
      select: {
        costDictionaryVersionId: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
  }

  async updateOne(
    id: string,
    data: CostDictionaryDetailUpdateDto,
    reqUser: IReqUser
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询明细的版本
    const detailOne = await this.getOne(id);

    if (!detailOne) {
      throw new HttpException(`明细信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(detailOne.costDictionaryVersionId);

    await this.checkUnique({ tenantId, orgId, data, id });

    const { businessCostSubjectDetailsIds, ...detailData } = data;

    // 更新数据
    const detail = await this.prisma.costDictionaryDetail.update({
      data: {
        ...detailData,
        updateBy: userId
      },
      where: {
        id,
        tenantId,
        orgId,
        isDeleted: false
      }
    });

    // 更新中间表数据
    if (businessCostSubjectDetailsIds != null) {
      const existingRelations =
        await this.prisma.costDetailBusinessCostSubjectDetail.findMany({
          where: {
            tenantId,
            orgId,
            costDictionaryDetailId: detail.id,
            isDeleted: false
          },
          select: {
            businessCostSubjectDetailId: true
          }
        });
      const existingIds = existingRelations.map(
        (item) => item.businessCostSubjectDetailId
      );
      const newIds = businessCostSubjectDetailsIds;

      // 找出需要删除的（存在于 old，但不在 new 中）
      const toDelete = existingIds.filter((id) => !newIds.includes(id));

      // 找出需要新增的（存在于 new，但不在 old 中）
      const toCreate = newIds.filter((id) => !existingIds.includes(id));

      // 删除旧的
      if (toDelete.length > 0) {
        await this.prisma.costDetailBusinessCostSubjectDetail.updateMany({
          where: {
            tenantId,
            orgId,
            costDictionaryDetailId: detail.id,
            businessCostSubjectDetailId: {
              in: toDelete
            },
            isDeleted: false
          },
          data: {
            isDeleted: true,
            updateBy: userId
          }
        });
      }

      // 新增新的
      if (toCreate.length > 0) {
        await this.prisma.costDetailBusinessCostSubjectDetail.createMany({
          data: toCreate.map((item) => ({
            tenantId,
            orgId,
            createBy: userId,
            updateBy: userId,
            costDictionaryDetailId: detail.id,
            businessCostSubjectDetailId: item
          }))
        });
      }
    }
  }

  async deleteOne(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询明细的版本
    const detailOne = await this.getOne(id);

    if (!detailOne) {
      throw new HttpException(`明细信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(detailOne.costDictionaryVersionId);

    // 检查明细是否被引用
    const projectRefCount = await this.getProjectRefInfo(tenantId, id);
    if (projectRefCount.projectRefCount > 0) {
      throw new BadRequestException('明细被引用，无法删除！');
    }

    // 逻辑删除
    await this.prisma.costDictionaryDetail.update({
      data: {
        isDeleted: true,
        updateBy: userId
      },
      where: {
        id,
        tenantId,
        orgId,
        isDeleted: false
      }
    });

    // 逻辑删除中间表数据
    await this.prisma.costDetailBusinessCostSubjectDetail.updateMany({
      where: {
        tenantId,
        orgId,
        costDictionaryDetailId: id
      },
      data: {
        isDeleted: true,
        updateBy: userId
      }
    });
  }

  async moveOne(id: string, moveTo: MoveToEnum, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询明细的版本
    const detailOne = await this.getOne(id);

    if (!detailOne) {
      throw new HttpException(`明细信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(detailOne.costDictionaryVersionId);

    const currentRecord = await this.prisma.costDictionaryDetail.findFirst({
      select: {
        id: true,
        costDictionaryVersionId: true,
        costDictionaryCategoryId: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        id,
        isDeleted: false
      }
    });
    if (!currentRecord) {
      throw new BadRequestException('当前操作数据不存在！');
    }

    if (moveTo === MoveToEnum.UP) {
      await this.moveUp(tenantId, orgId, currentRecord);
    }
    if (moveTo === MoveToEnum.DOWN) {
      await this.moveDown(tenantId, orgId, currentRecord);
    }
  }

  /**
   * 同一版本与分类下，检查唯一性
   * 编码唯一，名称+规格型号唯一
   */
  private async checkUnique(args: {
    tenantId: string;
    orgId: string;
    data: CostDictionaryDetailCreateDto | CostDictionaryDetailUpdateDto;
    id?: string;
  }) {
    const { tenantId, orgId, data, id = '' } = args;
    const { code, name } = data;

    // 检查编码唯一性
    const duplicateCode: any[] = await this.prisma.$queryRaw`
      select
        cdd.code
        ,cdc.name as category_name
        ,cdc.code as category_code
      from cost_dictionary_detail cdd
      join cost_dictionary_category cdc
        on cdc.is_deleted = false
        and cdc.tenant_id = cdd.tenant_id
        and cdc.org_id = cdd.org_id
        and cdc.id = cdd.cost_dictionary_category_id
      where cdd.is_deleted = false
        and cdd.tenant_id = ${tenantId}
        and cdd.org_id = ${orgId}
        and cdd.cost_dictionary_version_id = ${data.costDictionaryVersionId}
        and cdd.id <> ${id}
        and cdd.code = ${code}
    `;

    if (!_isEmpty(duplicateCode)) {
      const { categoryName, categoryCode, code } = duplicateCode[0];
      throw new BadRequestException(
        `费用字典分类[${categoryCode}-${categoryName}], 已存在编码[${code}]`
      );
    }

    const duplicateName: any[] = await this.prisma.$queryRaw`
      select
        cdd.name
        ,cdc.name as category_name
        ,cdc.code as category_code
      from cost_dictionary_detail cdd
      join cost_dictionary_category cdc
        on cdc.is_deleted = false
        and cdc.tenant_id = cdd.tenant_id
        and cdc.org_id = cdd.org_id
        and cdc.id = cdd.cost_dictionary_category_id
      where cdd.is_deleted = false
        and cdd.tenant_id = ${tenantId}
        and cdd.org_id = ${orgId}
        and cdd.cost_dictionary_version_id = ${data.costDictionaryVersionId}
        and cdd.id <> ${id}
        and cdd.name = ${name}
    `;

    if (!_isEmpty(duplicateName)) {
      const { categoryName, categoryCode, name } = duplicateName[0];
      throw new BadRequestException(
        `费用字典分类[${categoryCode}-${categoryName}], 已存在名称=[${name}]`
      );
    }
  }

  /** 上移 */
  private async moveUp(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      costDictionaryVersionId: string;
      costDictionaryCategoryId: string;
      sort: number;
    }
  ) {
    const { costDictionaryVersionId, costDictionaryCategoryId } = currentRecord;

    // 找到上一条数据
    const prevRecord = await this.prisma.costDictionaryDetail.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        costDictionaryVersionId,
        costDictionaryCategoryId,
        isDeleted: false,
        id: {
          not: currentRecord.id
        },
        sort: {
          lt: currentRecord.sort
        }
      },
      orderBy: {
        sort: 'desc'
      }
    });

    if (!prevRecord || prevRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.costDictionaryDetail.update({
        where: {
          tenantId,
          orgId,
          costDictionaryVersionId,
          id: currentRecord.id,
          isDeleted: false
        },
        data: {
          sort: prevRecord.sort
        }
      }),
      this.prisma.costDictionaryDetail.update({
        where: {
          tenantId,
          orgId,
          costDictionaryVersionId,
          id: prevRecord.id,
          isDeleted: false
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  /** 下移 */
  private async moveDown(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      costDictionaryVersionId: string;
      costDictionaryCategoryId: string;
      sort: number;
    }
  ) {
    const { costDictionaryVersionId, costDictionaryCategoryId } = currentRecord;

    // 找到下一条数据
    const nextRecord = await this.prisma.costDictionaryDetail.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        costDictionaryVersionId,
        costDictionaryCategoryId,
        isDeleted: false,
        id: {
          not: currentRecord.id
        },
        sort: {
          gt: currentRecord.sort
        }
      },
      orderBy: {
        sort: 'asc'
      }
    });

    if (!nextRecord || nextRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.costDictionaryDetail.update({
        where: {
          tenantId,
          orgId,
          // costDictionaryVersionId,
          id: currentRecord.id,
          isDeleted: false
        },
        data: {
          sort: nextRecord.sort
        }
      }),
      this.prisma.costDictionaryDetail.update({
        where: {
          tenantId,
          orgId,
          // costDictionaryVersionId,
          id: nextRecord.id,
          isDeleted: false
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  // 获取明细项目引用数
  async getProjectRefInfo(
    tenantId: string,
    id?: string
  ): Promise<{
    projectRefCount: number;
  }> {
    // 项目引用数统计
    const projectRefCount = await this.prisma.accountCostDictionaryDetail.count(
      {
        where: {
          tenantId,
          isDeleted: false,
          detailId: id
        }
      }
    );
    return { projectRefCount: projectRefCount || 0 };
  }
}
