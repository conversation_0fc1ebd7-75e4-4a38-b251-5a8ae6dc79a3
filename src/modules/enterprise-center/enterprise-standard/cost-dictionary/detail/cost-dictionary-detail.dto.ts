import {
  ApiProperty,
  IntersectionType,
  PartialType,
  PickType
} from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min
} from 'class-validator';

class CostDictionaryDetailBaseDto {
  @ApiProperty({ description: '版本id' })
  @IsNotEmpty({ message: '版本id不能为空' })
  @IsString({ message: '版本id必须是字符串' })
  costDictionaryVersionId: string;

  @ApiProperty({ description: '分类id' })
  @IsNotEmpty({ message: '分类id不能为空' })
  @IsString({ message: '分类id必须是字符串' })
  costDictionaryCategoryId: string;

  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '编码' })
  @IsNotEmpty({ message: '编码不能为空' })
  @IsString({ message: '编码必须是字符串' })
  code: string;

  @ApiProperty({ description: '名称' })
  @IsNotEmpty({ message: '名称不能为空' })
  @IsString({ message: '名称必须是字符串' })
  name: string;

  @ApiProperty({ description: '备注' })
  @IsOptional({ message: '备注可以为空' })
  remark: string | null = null;

  @ApiProperty({ description: '业务成本科目', type: [String] })
  @IsOptional({ message: '业务成本科目可以为空' })
  @IsArray({ message: '业务成本科目必须是数组' })
  @IsString({ each: true, message: '每个业务成本科目必须是字符串' })
  businessCostSubjectDetailsIds?: string[];

  @ApiProperty({ description: '核算说明' })
  @IsOptional({ message: '核算说明可以为空' })
  @IsString({ message: '核算说明必须是字符串' })
  accountExplanation: string | null = null;

  @ApiProperty({ description: '排序', default: 1 })
  @IsOptional({ message: '排序可以为空' })
  @IsInt({ message: '排序必须是数字' })
  @Min(1, { message: '排序不能小于1' })
  sort: number = 1;

  @ApiProperty({ description: '是否启用', default: true })
  @IsOptional({ message: '是否启用可以为空' })
  @IsBoolean({ message: '是否启用必须是布尔值' })
  isActive: boolean = true;
}

/** 创建数据Dto */
export class CostDictionaryDetailCreateDto extends PickType(
  CostDictionaryDetailBaseDto,
  [
    'costDictionaryVersionId',
    'costDictionaryCategoryId',
    'code',
    'name',
    'remark',
    'businessCostSubjectDetailsIds',
    'accountExplanation'
  ] as const
) {}

/** 更新数据Dto */
export class CostDictionaryDetailUpdateDto extends IntersectionType(
  PickType(CostDictionaryDetailBaseDto, [
    'costDictionaryVersionId',
    'costDictionaryCategoryId'
  ] as const),
  PartialType(
    PickType(CostDictionaryDetailBaseDto, [
      'costDictionaryVersionId',
      'costDictionaryCategoryId',
      'code',
      'name',
      'remark',
      'businessCostSubjectDetailsIds',
      'accountExplanation',
      'isActive'
    ] as const)
  )
) {}

/** 查询结果Dto */
export class CostDictionaryDetailResultDto extends CostDictionaryDetailBaseDto {}

/** 查询列表接口query参数Dto */
export class CostDictionaryDetailQueryParamDto {
  @ApiProperty({ description: '版本id' })
  @IsNotEmpty({ message: '版本id不能为空' })
  @IsString({ message: '版本id必须是字符串' })
  costDictionaryVersionId: string;

  @ApiProperty({ description: '分类id' })
  @IsNotEmpty({ message: '分类id不能为空' })
  @IsString({ message: '分类id必须是字符串' })
  costDictionaryCategoryId: string;

  @ApiProperty({ description: '名称' })
  @IsOptional({ message: '名称可以为空，当为空时查询全部明细' })
  @IsString({ message: '名称必须是字符串' })
  name?: string;
}
