import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import { Utils } from '../../../../common/utils';
import {
  FinancialCostSubjectResultDto,
  FinancialCostSubjectTreeResultDto
} from './financial-cost-subject.dto';

@Injectable()
export class FinancialCostSubjectService {
  constructor(private readonly prisma: PrismaService) {}

  async getList(reqUser: IReqUser): Promise<FinancialCostSubjectResultDto[]> {
    const { tenantId, orgId } = reqUser;

    const list = await this.prisma.financialCostSubject.findMany({
      select: {
        id: true,
        parentId: true,
        code: true,
        name: true
      },
      where: {
        tenantId,
        orgId,
        isDeleted: false
      },
      orderBy: {
        sort: 'asc'
      }
    });

    return list;
  }

  async getTree(
    reqUser: IReqUser
  ): Promise<FinancialCostSubjectTreeResultDto[]> {
    const { tenantId, orgId } = reqUser;

    const list = await this.prisma.financialCostSubject.findMany({
      select: {
        id: true,
        parentId: true,
        code: true,
        name: true,
        isLeaf: true
      },
      where: {
        tenantId,
        orgId,
        isDeleted: false
      },
      orderBy: {
        sort: 'asc'
      }
    });

    return Utils.buildTree(list, null);
  }
}
