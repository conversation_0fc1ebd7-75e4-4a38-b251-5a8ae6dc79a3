import { ApiProperty } from '@nestjs/swagger';

/** 查询结果Dto */
export class FinancialCostSubjectResultDto {
  @ApiProperty({ description: 'id' })
  id: string;

  @ApiProperty({ description: '父级id', default: null })
  parentId: string | null;

  @ApiProperty({ description: '编码' })
  code: string;

  @ApiProperty({ description: '名称' })
  name: string;
}

/** 树形查询结果Dto */
export class FinancialCostSubjectTreeResultDto {
  @ApiProperty({ description: 'id' })
  id: string;

  @ApiProperty({ description: '父级id', default: null })
  parentId: string | null;

  @ApiProperty({ description: '编码' })
  code: string;

  @ApiProperty({ description: '名称' })
  name: string;

  @ApiProperty({
    description: '子节点',
    type: () => FinancialCostSubjectTreeResultDto,
    isArray: true
  })
  children?: FinancialCostSubjectTreeResultDto[];
}
