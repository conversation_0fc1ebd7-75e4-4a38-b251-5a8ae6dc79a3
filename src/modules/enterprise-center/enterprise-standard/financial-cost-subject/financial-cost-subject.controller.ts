import { Controller, Get } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  FinancialCostSubjectResultDto,
  FinancialCostSubjectTreeResultDto
} from './financial-cost-subject.dto';
import { FinancialCostSubjectService } from './financial-cost-subject.service';

@ApiTags('财务成本科目')
@Controller('financial-cost-subject')
export class FinancialCostSubjectController {
  constructor(private readonly service: FinancialCostSubjectService) {}

  @ApiOperation({
    summary: '获取财务成本科目列表',
    description: '获取财务成本科目列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取财务成本科目列表成功',
    type: FinancialCostSubjectResultDto,
    isArray: true
  })
  @Get()
  async getList(
    @ReqUser() reqUser: IReqUser
  ): Promise<FinancialCostSubjectResultDto[]> {
    const list = await this.service.getList(reqUser);
    return list;
  }

  @ApiOperation({
    summary: '获取财务成本科目树',
    description: '获取财务成本科目树'
  })
  @ApiResponse({
    status: 200,
    description: '获取财务成本科目树成功',
    type: FinancialCostSubjectTreeResultDto,
    isArray: true
  })
  @Get('/tree')
  async getTree(
    @ReqUser() reqUser: IReqUser
  ): Promise<FinancialCostSubjectTreeResultDto[]> {
    const list = await this.service.getTree(reqUser);
    return list;
  }
}
