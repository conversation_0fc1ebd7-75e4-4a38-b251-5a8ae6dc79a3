import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';

import { FinancialCostSubjectController } from './financial-cost-subject.controller';
import { FinancialCostSubjectService } from './financial-cost-subject.service';

@Module({
  imports: [PrismaModule],
  controllers: [FinancialCostSubjectController],
  providers: [FinancialCostSubjectService]
})
export class FinancialCostSubjectModule {}
