import * as infraCloudSdk from '@ewing/infra-cloud-sdk';
import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable
} from '@nestjs/common';
import { isEmpty as _isEmpty } from 'lodash';

import { MoveToEnum } from '@/common/enums/common.enum';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { EnableStatus } from '@/prisma/generated';
import { MaterialDictionaryDetailWhereInput } from '@/prisma/generated/models';

import {
  MaterialDictionaryDetailCreateDto,
  MaterialDictionaryDetailQueryParamDto,
  MaterialDictionaryDetailResultDto,
  MaterialDictionaryDetailUpdateDto
} from './material-dictionary-detail.dto';

@Injectable()
export class MaterialDictionaryDetailService {
  constructor(private readonly prisma: PrismaService) {}

  async getList(
    reqUser: IReqUser,
    queryParam: MaterialDictionaryDetailQueryParamDto
  ) {
    const { tenantId, orgId } = reqUser;
    const { materialDictionaryVersionId, materialDictionaryCategoryId, name } =
      queryParam;

    const where: MaterialDictionaryDetailWhereInput = {
      tenantId,
      orgId,
      materialDictionaryVersionId,
      isDeleted: false
    };
    // 查询所有明细时，不按分类进行过滤
    if (materialDictionaryCategoryId !== '100') {
      where.materialDictionaryCategory = {
        fullId: { contains: materialDictionaryCategoryId }
      };
    }
    // 按名称搜索时
    if (name) {
      where.name = { contains: name };
    }

    const categoryList = await this.prisma.materialDictionaryCategory.findMany({
      select: {
        id: true,
        parentId: true,
        level: true,
        sort: true,
        isActive: true
      },
      where: {
        tenantId,
        orgId,
        materialDictionaryVersionId,
        fullId:
          materialDictionaryCategoryId !== '100'
            ? { contains: materialDictionaryCategoryId }
            : undefined
      },
      orderBy: [{ isActive: 'desc' }, { sort: 'asc' }]
    });
    let detailList = await this.prisma.materialDictionaryDetail.findMany({
      select: {
        id: true,
        materialDictionaryVersionId: true,
        materialDictionaryCategoryId: true,
        code: true,
        name: true,
        specificationModel: true,
        meteringUnit: true,
        type: true,
        remark: true,
        accountExplanation: true,
        isActive: true,
        sort: true,
        materialDictionaryCategory: {
          select: {
            isActive: true
          }
        },
        materialDetailBusinessCostSubjectDetail: {
          select: {
            businessCostSubjectDetailId: true
          },
          where: {
            isDeleted: false
          }
        }
      },
      where,
      orderBy: [{ isActive: 'desc' }, { sort: 'asc' }]
    });
    detailList = detailList.map((item) => ({
      ...item,
      parentId: item.materialDictionaryCategoryId,
      businessCostSubjectDetailsIds:
        item.materialDetailBusinessCostSubjectDetail.map(
          (id) => id.businessCostSubjectDetailId
        )
    }));

    const { parentRecords: rootNodes } = infraCloudSdk.initTree([
      ...categoryList,
      ...detailList
    ] as any);
    let list = infraCloudSdk
      .flattenTree(rootNodes, { traversal: 'depth' })
      .filter(
        (item) => item.isLeaf && item.materialDictionaryCategoryId != null
      );
    // 将“废弃”数据排在最后
    list = [
      ...list.filter((item) => item.isActive),
      ...list.filter((item) => !item.isActive)
    ];

    return list as any as MaterialDictionaryDetailResultDto[];
  }

  async createOne(reqUser: IReqUser, data: MaterialDictionaryDetailCreateDto) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 校验启用状态
    await this.checkVersionStatus(data.materialDictionaryVersionId);

    await this.checkUnique({ tenantId, orgId, data });

    const maxSort = await this.prisma.materialDictionaryDetail.aggregate({
      _max: {
        sort: true
      },
      where: {
        tenantId,
        orgId,
        materialDictionaryVersionId: data.materialDictionaryVersionId,
        materialDictionaryCategoryId: data.materialDictionaryCategoryId,
        isDeleted: false
      }
    });
    const { businessCostSubjectDetailsIds, ...detailData } = data;
    // 创建明细数据
    const detail = await this.prisma.materialDictionaryDetail.create({
      data: {
        ...detailData,
        sort: maxSort._max.sort ? maxSort._max.sort + 1 : 1,
        tenantId,
        orgId,
        createBy: userId,
        updateBy: userId
      }
    });

    // 写入中间表
    if (businessCostSubjectDetailsIds?.length) {
      await this.prisma.materialDetailBusinessCostSubjectDetail.createMany({
        data: businessCostSubjectDetailsIds.map((item) => {
          return {
            tenantId,
            orgId,
            createBy: userId,
            materialDictionaryDetailId: item,
            businessCostSubjectDetailId: detail.id,
            updateBy: userId
          };
        })
      });
    }

    return detail;
  }

  // 查询对应的版本
  async checkVersionStatus(id: string) {
    const version = await this.prisma.materialDictionaryVersion.findFirst({
      select: {
        status: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
    if (version?.status === EnableStatus.ENABLED) {
      throw new HttpException(
        `版本已启用，无法进行操作`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async getOne(id: string) {
    return await this.prisma.materialDictionaryDetail.findUnique({
      select: {
        materialDictionaryVersionId: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
  }

  async updateOne(
    id: string,
    data: MaterialDictionaryDetailUpdateDto,
    reqUser: IReqUser
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询明细的版本
    const detailOne = await this.getOne(id);

    if (!detailOne) {
      throw new HttpException(`明细信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(detailOne.materialDictionaryVersionId);

    await this.checkUnique({ tenantId, orgId, data, id });

    const { businessCostSubjectDetailsIds, ...detailData } = data;

    // 更新数据
    const detail = await this.prisma.materialDictionaryDetail.update({
      data: {
        ...detailData,
        updateBy: userId
      },
      where: {
        id,
        tenantId,
        orgId,
        isDeleted: false
      }
    });

    // 更新中间表数据
    if (businessCostSubjectDetailsIds != null) {
      const existingRelations =
        await this.prisma.materialDetailBusinessCostSubjectDetail.findMany({
          where: {
            tenantId,
            orgId,
            materialDictionaryDetailId: detail.id,
            isDeleted: false
          },
          select: {
            businessCostSubjectDetailId: true
          }
        });
      const existingIds = existingRelations.map(
        (item) => item.businessCostSubjectDetailId
      );
      const newIds = businessCostSubjectDetailsIds;

      // 找出需要删除的（存在于 old，但不在 new 中）
      const toDelete = existingIds.filter((id) => !newIds.includes(id));

      // 找出需要新增的（存在于 new，但不在 old 中）
      const toCreate = newIds.filter((id) => !existingIds.includes(id));

      // 删除旧的
      if (toDelete.length > 0) {
        await this.prisma.materialDetailBusinessCostSubjectDetail.updateMany({
          where: {
            tenantId,
            orgId,
            materialDictionaryDetailId: detail.id,
            businessCostSubjectDetailId: {
              in: toDelete
            },
            isDeleted: false
          },
          data: {
            isDeleted: true,
            updateBy: userId
          }
        });
      }

      // 新增新的
      if (toCreate.length > 0) {
        await this.prisma.materialDetailBusinessCostSubjectDetail.createMany({
          data: toCreate.map((item) => ({
            tenantId,
            orgId,
            createBy: userId,
            updateBy: userId,
            materialDictionaryDetailId: detail.id,
            businessCostSubjectDetailId: item
          }))
        });
      }
    }
  }

  async deleteOne(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询明细的版本
    const detailOne = await this.getOne(id);

    if (!detailOne) {
      throw new HttpException(`明细信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(detailOne.materialDictionaryVersionId);

    // 检查明细是否被引用
    const projectRefCount = await this.getProjectRefInfo(tenantId, id);
    if (projectRefCount.projectRefCount > 0) {
      throw new BadRequestException('明细被引用，无法删除！');
    }

    await this.prisma.$transaction(async (tx) => {
      await tx.materialDictionaryDetail.update({
        data: {
          isDeleted: true,
          updateBy: userId
        },
        where: {
          id,
          tenantId,
          orgId,
          isDeleted: false
        }
      });
      // 删除单位换算
      await tx.materialDictionaryUnitCalculation.updateMany({
        data: {
          isDeleted: true,
          updateBy: userId
        },
        where: {
          tenantId,
          orgId,
          materialDictionaryDetailId: id
        }
      });
      // 删除业务成本科目关联关系
      await tx.materialDetailBusinessCostSubjectDetail.updateMany({
        data: {
          isDeleted: true,
          updateBy: userId
        },
        where: {
          tenantId,
          orgId,
          materialDictionaryDetailId: id
        }
      });
    });
  }

  async moveOne(id: string, moveTo: MoveToEnum, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询明细的版本
    const detailOne = await this.getOne(id);

    if (!detailOne) {
      throw new HttpException(`明细信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(detailOne.materialDictionaryVersionId);

    const currentRecord = await this.prisma.materialDictionaryDetail.findFirst({
      select: {
        id: true,
        materialDictionaryVersionId: true,
        materialDictionaryCategoryId: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        id,
        isDeleted: false
      }
    });
    if (!currentRecord) {
      throw new BadRequestException('当前操作数据不存在！');
    }

    if (moveTo === MoveToEnum.UP) {
      await this.moveUp(tenantId, orgId, currentRecord);
    }
    if (moveTo === MoveToEnum.DOWN) {
      await this.moveDown(tenantId, orgId, currentRecord);
    }
  }

  /**
   * 同一版本与分类下，检查唯一性
   * 编码唯一，名称+规格型号唯一
   */
  private async checkUnique(args: {
    tenantId: string;
    orgId: string;
    data: MaterialDictionaryDetailCreateDto | MaterialDictionaryDetailUpdateDto;
    id?: string;
  }) {
    const { tenantId, orgId, data, id = '' } = args;
    const { code, name, specificationModel } = data;

    // 检查编码唯一性
    const duplicateCode: any[] = await this.prisma.$queryRaw`
      select
        mdd.code
        ,mdc.name as category_name
        ,mdc.code as category_code
      from material_dictionary_detail as mdd
      join material_dictionary_category as mdc
        on mdc.is_deleted = false
        and mdc.org_id = mdd.org_id
        and mdc.tenant_id = mdd.tenant_id
        and mdc.id = mdd.material_dictionary_category_id
      where mdd.is_deleted = false
        and mdd.tenant_id = ${tenantId}
        and mdd.org_id = ${orgId}
        and mdd.material_dictionary_version_id = ${data.materialDictionaryVersionId}
        and mdd.id <> ${id}
        and mdd.code = ${code}
    `;

    if (!_isEmpty(duplicateCode)) {
      const { categoryName, categoryCode, code } = duplicateCode[0];
      throw new BadRequestException(
        `材料字典分类[${categoryCode}-${categoryName}], 已存在材料编码[${code}]`
      );
    }

    // 检查名称+规格型号唯一性
    const duplicateNameAndSpec: any[] = await this.prisma.$queryRaw`
      select
        mdd.name
        ,mdd.specification_model
        ,mdc.name as category_name
        ,mdc.code as category_code
      from material_dictionary_detail as mdd
      join material_dictionary_category as mdc
        on mdc.is_deleted = false
        and mdc.org_id = mdd.org_id
        and mdc.tenant_id = mdd.tenant_id
        and mdc.id = mdd.material_dictionary_category_id
      where mdd.is_deleted = false
        and mdd.tenant_id = ${tenantId}
        and mdd.org_id = ${orgId}
        and mdd.material_dictionary_version_id = ${data.materialDictionaryVersionId}
        and mdd.id <> ${id}
        and mdd.name = ${name}
        and mdd.specification_model = ${specificationModel}
    `;

    if (!_isEmpty(duplicateNameAndSpec)) {
      const { categoryName, categoryCode, name, specificationModel } =
        duplicateNameAndSpec[0];
      throw new BadRequestException(
        `材料字典分类[${categoryCode}-${categoryName}], 已存在材料名称=[${name}]&材料规格型号=[${specificationModel}]`
      );
    }
  }

  /** 上移 */
  private async moveUp(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      materialDictionaryVersionId: string;
      materialDictionaryCategoryId: string;
      sort: number;
    }
  ) {
    const { materialDictionaryVersionId, materialDictionaryCategoryId } =
      currentRecord;

    // 找到上一条数据
    const prevRecord = await this.prisma.materialDictionaryDetail.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        materialDictionaryVersionId,
        materialDictionaryCategoryId,
        isDeleted: false,
        id: {
          not: currentRecord.id
        },
        sort: {
          lt: currentRecord.sort
        }
      },
      orderBy: {
        sort: 'desc'
      }
    });

    if (!prevRecord || prevRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.materialDictionaryDetail.update({
        where: {
          tenantId,
          orgId,
          materialDictionaryVersionId,
          id: currentRecord.id,
          isDeleted: false
        },
        data: {
          sort: prevRecord.sort
        }
      }),
      this.prisma.materialDictionaryDetail.update({
        where: {
          tenantId,
          orgId,
          materialDictionaryVersionId,
          id: prevRecord.id,
          isDeleted: false
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  /** 下移 */
  private async moveDown(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      materialDictionaryVersionId: string;
      materialDictionaryCategoryId: string;
      sort: number;
    }
  ) {
    const { materialDictionaryVersionId, materialDictionaryCategoryId } =
      currentRecord;

    // 找到下一条数据
    const nextRecord = await this.prisma.materialDictionaryDetail.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        materialDictionaryVersionId,
        materialDictionaryCategoryId,
        isDeleted: false,
        id: {
          not: currentRecord.id
        },
        sort: {
          gt: currentRecord.sort
        }
      },
      orderBy: {
        sort: 'asc'
      }
    });

    if (!nextRecord || nextRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.materialDictionaryDetail.update({
        where: {
          tenantId,
          orgId,
          materialDictionaryVersionId,
          id: currentRecord.id,
          isDeleted: false
        },
        data: {
          sort: nextRecord.sort
        }
      }),
      this.prisma.materialDictionaryDetail.update({
        where: {
          tenantId,
          orgId,
          materialDictionaryVersionId,
          id: nextRecord.id,
          isDeleted: false
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  // 获取明细项目引用数
  async getProjectRefInfo(
    tenantId: string,
    id?: string
  ): Promise<{
    projectRefCount: number;
  }> {
    // 项目引用数统计
    const projectRefCount =
      await this.prisma.accountMaterialDictionaryDetail.count({
        where: {
          tenantId,
          isDeleted: false,
          detailId: id
        }
      });
    return { projectRefCount: projectRefCount || 0 };
  }
}
