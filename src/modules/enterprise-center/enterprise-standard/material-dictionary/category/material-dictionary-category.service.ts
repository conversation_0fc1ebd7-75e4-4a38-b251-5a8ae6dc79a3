import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable
} from '@nestjs/common';
import { v7 as uuidv7 } from 'uuid';

import { MoveToEnum } from '@/common/enums/common.enum';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { EnableStatus, MaterialDictionaryCategory } from '@/prisma/generated';
import { MaterialDictionaryCategoryWhereInput } from '@/prisma/generated/models';

import {
  MaterialDictionaryCategoryCreateDto,
  MaterialDictionaryCategoryResultDto,
  MaterialDictionaryCategoryUpdateDto
} from './material-dictionary-category.dto';

@Injectable()
export class MaterialDictionaryCategoryService {
  constructor(private readonly prisma: PrismaService) {}

  async getList(
    reqUser: IReqUser,
    versionId: string
  ): Promise<MaterialDictionaryCategoryResultDto[]> {
    const { tenantId, orgId } = reqUser;

    const list = await this.prisma.materialDictionaryCategory.findMany({
      select: {
        materialDictionaryVersionId: true,
        id: true,
        code: true,
        name: true,
        type: true,
        remark: true,
        isActive: true,
        parentId: true,
        isLeaf: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        materialDictionaryVersionId: versionId,
        isDeleted: false
      },
      orderBy: [{ isActive: 'desc' }, { sort: 'asc' }]
    });

    return list;
  }

  async createOne(
    data: MaterialDictionaryCategoryCreateDto,
    reqUser: IReqUser
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 校验启用状态
    await this.checkVersionStatus(data.materialDictionaryVersionId);

    // 检查数据是否重复
    await this.checkUnique({ tenantId, orgId, data });

    const newRecord = {
      ...data,
      tenantId,
      orgId,
      id: uuidv7(),
      createBy: userId,
      updateBy: userId
    } as MaterialDictionaryCategory;

    await this.processTreeInfo(tenantId, orgId, newRecord);

    const category = await this.prisma.materialDictionaryCategory.create({
      data: {
        ...newRecord,
        tenantId,
        orgId,
        createBy: userId,
        updateBy: userId
      }
    });

    return category;
  }

  // 查询对应的版本
  async checkVersionStatus(id: string) {
    const version = await this.prisma.materialDictionaryVersion.findFirst({
      select: {
        status: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
    if (version?.status === EnableStatus.ENABLED) {
      throw new HttpException(
        `版本已启用，无法进行操作`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async getOne(id: string) {
    return await this.prisma.materialDictionaryCategory.findUnique({
      select: {
        materialDictionaryVersionId: true,
        parentId: true,
        fullName: true,
        name: true,
        level: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
  }

  async updateOne(
    id: string,
    data: MaterialDictionaryCategoryUpdateDto,
    reqUser: IReqUser
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询分类的版本
    const category = await this.getOne(id);

    if (!category) {
      throw new HttpException(`分类信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(category.materialDictionaryVersionId);

    // 检查数据是否重复
    await this.checkUnique({
      tenantId,
      orgId,
      data,
      id,
      materialDictionaryVersionId: category.materialDictionaryVersionId
    });

    // 更新fullName
    if (data.name !== category.name) {
      const fullNameArr = category.fullName.split('|').slice(0, -1);
      fullNameArr.push(data.name);
      data.fullName = fullNameArr.join('|');
    }

    await this.prisma.$transaction(async (tx) => {
      await tx.materialDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          id,
          isDeleted: false
        },
        data: {
          ...data,
          updateBy: userId
        }
      });

      if (data.isActive != null) {
        const childCategoryList =
          await tx.materialDictionaryCategory.updateManyAndReturn({
            select: {
              id: true
            },
            where: {
              tenantId,
              orgId,
              id: { not: id },
              fullId: { contains: id },
              isDeleted: false
            },
            data: {
              isActive: data.isActive,
              updateBy: userId
            }
          });
        const categoryIds = [id, ...childCategoryList.map((cat) => cat.id)];
        await tx.materialDictionaryDetail.updateMany({
          where: {
            tenantId,
            orgId,
            id: { not: id },
            materialDictionaryCategoryId: { in: categoryIds },
            isDeleted: false
          },
          data: {
            isActive: data.isActive,
            updateBy: userId
          }
        });
      }

      if (data.type != null) {
        const childCategoryList =
          await tx.materialDictionaryCategory.updateManyAndReturn({
            select: {
              id: true
            },
            where: {
              tenantId,
              orgId,
              id: { not: id },
              fullId: { contains: id },
              isDeleted: false
            },
            data: {
              type: data.type,
              updateBy: userId
            }
          });
        const categoryIds = [id, ...childCategoryList.map((cat) => cat.id)];
        await tx.materialDictionaryDetail.updateMany({
          where: {
            tenantId,
            orgId,
            materialDictionaryCategoryId: { in: categoryIds },
            isDeleted: false
          },
          data: {
            type: data.type,
            updateBy: userId
          }
        });
      }

      if (data.name !== category.name) {
        await tx.$executeRaw`
          WITH RECURSIVE category_path AS (
            SELECT
              id,
              name,
              parent_id,
              level,
              full_name
            FROM material_dictionary_category
            WHERE is_deleted = false
              AND tenant_id = ${tenantId}
              AND org_id = ${orgId}
              AND id = ${id}
            UNION ALL
            SELECT
              child.id,
              child.name,
              child.parent_id,
              child.level,
              cp.full_name || '|' || child.name AS full_name
            FROM material_dictionary_category child
            JOIN category_path cp 
              ON child.parent_id = cp.id
            WHERE child.is_deleted = false
              AND child.tenant_id = ${tenantId}
              AND child.org_id = ${orgId}
              AND child.level > ${category.level}
          )
          UPDATE material_dictionary_category t SET 
            full_name = cp.full_name
          FROM category_path cp
          WHERE t.is_deleted = false
            AND t.tenant_id = ${tenantId}
            AND t.org_id = ${orgId}
            AND t.level > ${category.level}
            AND t.id = cp.id
        `;
      }
    });

    return true;
  }

  async deleteOne(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询分类的版本
    const category = await this.getOne(id);

    if (!category) {
      throw new HttpException(`分类信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(category.materialDictionaryVersionId);

    // 检查删除约束
    await this.checkDeleteConstraint(tenantId, orgId, id);

    // 检查分类是否被引用
    const projectRefCount = await this.getProjectRefInfo(tenantId, id);
    if (projectRefCount.projectRefCount > 0) {
      throw new BadRequestException('分类被引用，无法删除！');
    }

    const ids = [id];

    // 如果有子级，则级联删除
    const children = await this.prisma.materialDictionaryCategory.findMany({
      select: {
        id: true
      },
      where: {
        tenantId,
        orgId,
        id: { not: id },
        fullId: { contains: id }
      }
    });
    ids.push(...children.map((item) => item.id));

    await this.prisma.$transaction(async (tx) => {
      await tx.materialDictionaryCategory.updateMany({
        where: {
          tenantId,
          orgId,
          id: { in: ids }
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
      // 删除分类后查询是否有父级分类，有则改变父级分类状态为非叶子节点
      await this.updateParentLeaf(
        tx as PrismaService,
        category.parentId || null,
        orgId,
        tenantId,
        reqUser.id
      );
    });
  }

  async updateParentLeaf(
    tx: PrismaService,
    parentId: string | null,
    orgId: string,
    tenantId: string,
    userId: string
  ) {
    if (parentId) {
      // 查询父级
      const parentCategory = await tx.materialDictionaryCategory.findFirst({
        where: {
          id: parentId,
          isDeleted: false,
          orgId,
          tenantId
        },
        select: {
          id: true,
          children: {
            select: {
              id: true
            },
            where: {
              isDeleted: false
            }
          }
        }
      });
      if (!parentCategory?.children.length) {
        await tx.materialDictionaryCategory.update({
          where: {
            id: parentId,
            isDeleted: false
          },
          data: {
            isLeaf: true,
            updateBy: userId,
            updateAt: new Date()
          }
        });
      }
    }
  }

  async moveOne(reqUser: IReqUser, id: string, moveTo: MoveToEnum) {
    const { tenantId, orgId } = reqUser;

    // 查询分类的版本
    const category = await this.getOne(id);

    if (!category) {
      throw new HttpException(`分类信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(category.materialDictionaryVersionId);

    const currentRecord =
      await this.prisma.materialDictionaryCategory.findFirst({
        select: {
          id: true,
          materialDictionaryVersionId: true,
          parentId: true,
          sort: true
        },
        where: {
          tenantId,
          orgId,
          id,
          isDeleted: false
        }
      });
    if (!currentRecord) {
      throw new BadRequestException('当前操作数据不存在！');
    }

    if (moveTo === MoveToEnum.UP) {
      await this.moveUp(tenantId, orgId, currentRecord);
    }
    if (moveTo === MoveToEnum.DOWN) {
      await this.moveDown(tenantId, orgId, currentRecord);
    }
  }

  /**
   * 处理树形信息
   */
  private async processTreeInfo(
    tenantId: string,
    orgId: string,
    data: MaterialDictionaryCategory
  ) {
    // 查询同级节点的最后一个数据，用于给当前数据生成排序号
    const findSiblingNodeLastWhereInput: MaterialDictionaryCategoryWhereInput =
      {
        tenantId,
        orgId,
        materialDictionaryVersionId: data.materialDictionaryVersionId,
        isDeleted: false
      };
    if (data.parentId) {
      findSiblingNodeLastWhereInput.parentId = data.parentId;
    }
    const maxSort = await this.prisma.materialDictionaryCategory.aggregate({
      _max: {
        sort: true
      },
      where: {
        tenantId,
        orgId,
        materialDictionaryVersionId: data.materialDictionaryVersionId,
        parentId: data.parentId,
        isDeleted: false
      }
    });
    data.sort = (maxSort._max.sort || 0) + 1;

    if (data.parentId) {
      const parent = await this.prisma.materialDictionaryCategory.findFirst({
        select: {
          fullId: true,
          fullName: true,
          level: true
        },
        where: {
          tenantId,
          orgId,
          materialDictionaryVersionId: data.materialDictionaryVersionId,
          id: data.parentId,
          isDeleted: false
        }
      });
      if (!parent) {
        throw new BadRequestException('父级不存在');
      }

      data.fullId = `${parent.fullId}|${data.id}`;
      data.fullName = `${parent.fullName}|${data.name}`;
      data.level = parent.level + 1;
    } else {
      data.fullId = data.id;
      data.fullName = data.name;
    }

    // 默认初始都为叶子节点,存在父级数据后修改
    data.isLeaf = true;
    if (data.parentId) {
      await this.prisma.materialDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          materialDictionaryVersionId: data.materialDictionaryVersionId,
          id: data.parentId,
          isDeleted: false
        },
        data: {
          isLeaf: false
        }
      });
    }
  }

  /**
   * 检查唯一性
   */
  private async checkUnique(args: {
    tenantId: string;
    orgId: string;
    data:
      | MaterialDictionaryCategoryCreateDto
      | MaterialDictionaryCategoryUpdateDto;
    id?: string;
    materialDictionaryVersionId?: string;
  }) {
    const { tenantId, orgId, data, id, materialDictionaryVersionId } = args;

    const code = data.code;
    const name = data.name;

    const duplicateCodeRecod =
      await this.prisma.materialDictionaryCategory.findFirst({
        select: {
          id: true
        },
        where: {
          tenantId,
          orgId,
          materialDictionaryVersionId: data.materialDictionaryVersionId
            ? data.materialDictionaryVersionId
            : materialDictionaryVersionId,
          id: { not: id },
          code,
          isDeleted: false
        }
      });
    const duplicateNameRecod =
      await this.prisma.materialDictionaryCategory.findFirst({
        select: {
          id: true
        },
        where: {
          tenantId,
          orgId,
          materialDictionaryVersionId: data.materialDictionaryVersionId
            ? data.materialDictionaryVersionId
            : materialDictionaryVersionId,
          id: { not: id },
          name,
          isDeleted: false
        }
      });
    if (duplicateCodeRecod) {
      throw new BadRequestException('编码重复，请重新输入！');
    }
    if (duplicateNameRecod) {
      throw new BadRequestException('名称重复，请重新输入！');
    }
  }

  /**
   * 检查删除约束
   */
  private async checkDeleteConstraint(
    tenantId: string,
    orgId: string,
    id: string
  ) {
    const childLevel = await this.prisma.materialDictionaryCategory.findFirst({
      select: {
        id: true
      },
      where: {
        tenantId,
        orgId,
        parentId: id,
        isDeleted: false
      }
    });
    if (childLevel) {
      throw new BadRequestException('分类下存在下级分类，不可删除！');
    }
    const detail = await this.prisma.materialDictionaryDetail.findFirst({
      select: {
        id: true
      },
      where: {
        tenantId,
        orgId,
        materialDictionaryCategoryId: id,
        isDeleted: false
      }
    });

    if (detail) {
      throw new BadRequestException('分类下存在明细，不可删除！');
    }
  }

  /**
   * 向上移动
   */
  private async moveUp(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      materialDictionaryVersionId: string;
      parentId: string | null;
      sort: number;
    }
  ) {
    const { materialDictionaryVersionId } = currentRecord;

    const prevRecordWhere: MaterialDictionaryCategoryWhereInput = {
      tenantId,
      orgId,
      materialDictionaryVersionId,
      isDeleted: false,
      id: {
        not: currentRecord.id
      },
      sort: {
        lt: currentRecord.sort
      }
    };
    if (currentRecord.parentId) {
      prevRecordWhere.parentId = currentRecord.parentId;
    }

    // 找到上一条数据
    const prevRecord = await this.prisma.materialDictionaryCategory.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: prevRecordWhere,
      orderBy: {
        sort: 'desc'
      }
    });

    if (!prevRecord || prevRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.materialDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          materialDictionaryVersionId,
          id: currentRecord.id,
          isDeleted: false
        },
        data: {
          sort: prevRecord.sort
        }
      }),
      this.prisma.materialDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          materialDictionaryVersionId,
          id: prevRecord.id,
          isDeleted: false
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  /**
   * 向下移动
   */
  private async moveDown(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      materialDictionaryVersionId: string;
      parentId: string | null;
      sort: number;
    }
  ) {
    const { materialDictionaryVersionId } = currentRecord;

    const nextRecordWhere: MaterialDictionaryCategoryWhereInput = {
      tenantId,
      orgId,
      materialDictionaryVersionId,
      isDeleted: false,
      id: {
        not: currentRecord.id
      },
      sort: {
        gt: currentRecord.sort
      }
    };
    if (currentRecord.parentId) {
      nextRecordWhere.parentId = currentRecord.parentId;
    }

    // 找到下一条数据
    const nextRecord = await this.prisma.materialDictionaryCategory.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: nextRecordWhere,
      orderBy: {
        sort: 'asc'
      }
    });

    if (!nextRecord || nextRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.materialDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          materialDictionaryVersionId,
          id: currentRecord.id,
          isDeleted: false
        },
        data: {
          sort: nextRecord.sort
        }
      }),
      this.prisma.materialDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          materialDictionaryVersionId,
          id: nextRecord.id,
          isDeleted: false
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  // 获取分类项目引用数
  async getProjectRefInfo(
    tenantId: string,
    id?: string
  ): Promise<{
    projectRefCount: number;
  }> {
    // 项目引用数统计
    const projectRefCount =
      await this.prisma.accountMaterialDictionaryCategory.count({
        where: {
          tenantId,
          isDeleted: false,
          categoryId: id
        }
      });
    return { projectRefCount: projectRefCount || 0 };
  }
}
