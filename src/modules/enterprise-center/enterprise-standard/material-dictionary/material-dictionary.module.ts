import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';

import { PlatformModule } from '../../../platform/platform.module';
import { MaterialDictionaryCategoryController } from './category/material-dictionary-category.controller';
import { MaterialDictionaryCategoryService } from './category/material-dictionary-category.service';
import { MaterialDictionaryDetailController } from './detail/material-dictionary-detail.controller';
import { MaterialDictionaryDetailService } from './detail/material-dictionary-detail.service';
import { MaterialDictionaryUnitConvertController } from './unit/material-dictionary-unit-convert.controller';
import { MaterialDictionaryUnitConvertService } from './unit/material-dictionary-unit-convert.service';
import { MaterialDictionaryVersionController } from './version/material-dictionary-version.controller';
import { MaterialDictionaryVersionService } from './version/material-dictionary-version.service';

@Module({
  imports: [PrismaModule, PlatformModule],
  controllers: [
    MaterialDictionaryVersionController,
    MaterialDictionaryCategoryController,
    MaterialDictionaryDetailController,
    MaterialDictionaryUnitConvertController
  ],
  providers: [
    MaterialDictionaryVersionService,
    MaterialDictionaryCategoryService,
    MaterialDictionaryDetailService,
    MaterialDictionaryUnitConvertService
  ]
})
export class MaterialDictionaryModule {}
