import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { MoveToEnum } from '@/common/enums/common.enum';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  MaterialDictionaryUnitConvertCreateDto,
  MaterialDictionaryUnitConvertQueryParamDto,
  MaterialDictionaryUnitConvertResultDto,
  MaterialDictionaryUnitConvertUpdateDto
} from './material-dictionary-unit-convert.dto';
import { MaterialDictionaryUnitConvertService } from './material-dictionary-unit-convert.service';

@ApiTags('材料字典/单位换算')
@Controller('material-dictionary-unit-convert')
export class MaterialDictionaryUnitConvertController {
  constructor(private readonly service: MaterialDictionaryUnitConvertService) {}

  @ApiOperation({
    summary: '获取单位换算列表',
    description: '获取单位换算列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取列表成功',
    type: MaterialDictionaryUnitConvertResultDto,
    isArray: true
  })
  @Get()
  async getList(
    @Query() queryParam: MaterialDictionaryUnitConvertQueryParamDto,
    @ReqUser() reqUser: IReqUser
  ) {
    const list = await this.service.getList(queryParam, reqUser);
    return list;
  }

  @ApiOperation({
    summary: '新增单位换算',
    description: '新增单位换算'
  })
  @ApiResponse({
    status: 200,
    description: '新增成功',
    type: Boolean
  })
  @Post()
  async createOne(
    @Body() data: MaterialDictionaryUnitConvertCreateDto,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.createOne(data, reqUser);
    return true;
  }

  @ApiOperation({
    summary: '更新单位换算',
    description: '更新单位换算'
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: Boolean
  })
  @Patch(':id')
  async updateOne(
    @Param('id') id: string,
    @Body() data: MaterialDictionaryUnitConvertUpdateDto,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.updateOne(id, data, reqUser);
    return true;
  }

  @ApiOperation({
    summary: '移动明细',
    description: '移动明细'
  })
  @ApiQuery({
    name: 'moveTo',
    description: '移动至',
    required: true,
    enum: MoveToEnum
  })
  @ApiResponse({
    status: 200,
    description: '移动明细成功',
    type: Boolean
  })
  @Patch(':id/_move')
  async moveOne(
    @Param('id') id: string,
    @Query('moveTo') moveTo: MoveToEnum,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.moveOne(reqUser, id, moveTo);
    return true;
  }

  @ApiOperation({
    summary: '删除单位换算',
    description: '删除单位换算'
  })
  @ApiResponse({
    status: 200,
    description: '删除成功',
    type: Boolean
  })
  @Delete(':id')
  async deleteOne(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    await this.service.deleteOne(id, reqUser);
    return true;
  }
}
