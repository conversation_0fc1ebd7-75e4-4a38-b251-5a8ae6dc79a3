import { ApiProperty, PickType } from '@nestjs/swagger';
import {
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsPositive,
  IsString,
  Min
} from 'class-validator';

/** 单位换算基础Dto */
class MaterialDictionaryUnitConvertBaseDto {
  @ApiProperty({ description: '明细id' })
  @IsNotEmpty({ message: '明细id不能为空' })
  @IsString({ message: '明细id必须是字符串' })
  materialDictionaryDetailId: string;

  @ApiProperty({ description: '换算单位' })
  @IsNotEmpty({ message: '换算单位不能为空' })
  @IsString({ message: '换算单位必须是字符串' })
  unit: string;

  @ApiProperty({ description: '换算系数' })
  @IsNotEmpty({ message: '换算系数不能为空' })
  @IsPositive({ message: '换算系数必须大于0' })
  factor: number;

  @ApiProperty({ description: '排序', default: 1 })
  @IsOptional({ message: '排序可以为空' })
  @IsInt({ message: '排序必须是数字' })
  @Min(1, { message: '排序不能小于1' })
  sort: number = 1;

  @ApiProperty({ description: '备注' })
  @IsOptional({ message: '备注可以为空' })
  @IsString({ message: '备注必须是字符串' })
  remark?: string;
}

/** 创建数据Dto */
export class MaterialDictionaryUnitConvertCreateDto extends PickType(
  MaterialDictionaryUnitConvertBaseDto,
  ['materialDictionaryDetailId', 'unit', 'factor', 'remark'] as const
) {}

/** 更新数据Dto */
export class MaterialDictionaryUnitConvertUpdateDto extends PickType(
  MaterialDictionaryUnitConvertBaseDto,
  ['unit', 'factor', 'remark', 'sort'] as const
) {}

/** 查询结果Dto */
export class MaterialDictionaryUnitConvertResultDto extends MaterialDictionaryUnitConvertBaseDto {}

/** 查询列表接口query参数Dto */
export class MaterialDictionaryUnitConvertQueryParamDto {
  @ApiProperty({ description: '明细id' })
  @IsNotEmpty({ message: '明细id不能为空' })
  @IsString({ message: '明细id必须是字符串' })
  materialDictionaryDetailId: string;
}
