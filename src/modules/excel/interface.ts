// interfaces.ts
export interface FieldValidationRule {
  dependentRequired?: string[]; // 依赖字段必填校验
  required: boolean; // 必填校验
  type:
    | 'string'
    | 'number'
    | 'boolean'
    | 'date'
    | 'enum'
    | 'float'
    | 'arrayEnum'; // 类型校验
  enum?: any[]; // 枚举校验
  booleanValues?: { [key: string]: boolean };
  // notSave?: boolean; // 字段不保存
  // unique?: boolean; // 唯一性校验
  // uniqueDataList?: string[]; // 唯一性数据列表
  // operation?: 'add' | 'update' | 'delete'; // 操作类型校验
  // operationColumn?: string[]; // 要操作列校验
  minLength?: number; // 最小长度
  maxLength?: number; // 最大长度
  pattern?: RegExp; // 正则校验
  custom?: (value: any) => boolean | string; // 自定义校验函数
}

export interface ImportExcelConfig {
  module: string; // 模块名
  sheetName: string; // Excel工作表名
  headerRow: number; // 表头行
  startRow: number; // 数据起始行
  batchSize: number; // 批量处理行数
  fieldMapping: {
    // 字段映射
    [excelCol: string]: {
      targetField: string; // 数据库字段
      targetName: string; // 数据库字段名称
      rules?: FieldValidationRule;
      transform?: (value: any, context: TransformContext) => any; // 数据转换函数
    };
  };
  defaultValue?: {
    // 字段映射
    targetField: string; // 数据库字段
    relationField?: string[]; // 关联字段
    targetName: string; // 数据库字段名称
    rules?: FieldValidationRule;
    transform?: (value: any) => any; // 数据转换函数
  };
}

// 导入结果类型
export interface ImportResult {
  total: number; // 总处理行数
  successCount: number; // 成功数量
  errors: RowError[];
}

// 行错误详情
export interface RowError {
  row: number; // 行号
  rowData: any;
  oldRowData: any;
  errors: Error[]; // 错误信息列表
}

export interface Error {
  key: string;
  message: string;
}

// 字段转换上下文
export interface TransformContext {
  rowData: any; // 原始行数据
  config: ImportExcelConfig; // 当前配置
}
