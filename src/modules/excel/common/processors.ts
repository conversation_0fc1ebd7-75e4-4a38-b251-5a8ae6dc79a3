import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import { ImportExcelConfig, RowError, TransformContext } from '../interface';
import { ValidationEngine } from './validators';

export abstract class BaseProcessor {
  constructor(protected readonly prisma: PrismaService) {}

  // 克隆
  abstract clone(): BaseProcessor;

  // 抽象方法：由子类实现唯一性检查
  abstract checkUnique(
    uniqueData: RowError[],
    importType: string
  ): Promise<RowError[]>;

  // 数据转换方法（抽象方法）
  abstract transformData(rowData: any, config: ImportExcelConfig): any;

  abstract transformOldData(rowData: any, config: ImportExcelConfig): any;

  // 数据持久化方法（抽象方法）
  abstract saveData(
    validData: any[],
    reqUser: IReqUser,
    params: Record<string, any> | null
  ): Promise<void>;

  /**
   * 字段值转换（带上下文）
   */
  public transformValue(
    value: any,
    excelCol: any,
    context: TransformContext
  ): any {
    const mapping = context.config.fieldMapping[excelCol as string];
    return mapping?.transform && value
      ? mapping.transform(value, context)
      : value;
  }

  // 公共校验方法
  async validateRow(
    rowNumber: number,
    importType: string,
    params: Record<string, any> | null,
    rowData: any,
    config: ImportExcelConfig,
    processor: BaseProcessor
  ): Promise<RowError> {
    // 每个校验创建独立实例
    const validator = new ValidationEngine(
      { ...rowData }, // 深拷贝数据
      config,
      processor
    );
    const result = await validator.validateRow();
    return {
      row: rowNumber,
      rowData: result.rowData,
      oldRowData: result.oldRowData,
      errors: result.errors
    };
  }
}
