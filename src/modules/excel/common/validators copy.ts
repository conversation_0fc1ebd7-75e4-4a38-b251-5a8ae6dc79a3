// import * as dayjs from 'dayjs';
// import * as customParseFormat from 'dayjs/plugin/customParseFormat';
// import { forEach, isEqual } from 'lodash';

// import { ImportType } from '../import.dto';
// import { DateFormatArr } from '../import-config/supplierDirectory/supplier-directory.dto';
// import {
//   FieldValidationRule,
//   ImportExcelConfig,
//   UniqueData,
//   ValidationContext
// } from '../interface';
// import { BaseProcessor } from './processors';
// dayjs.extend(customParseFormat);

// export class ValidationEngine {
//   private errors: any[] = [];
//   constructor(
//     private rowData: any,
//     private importType: string,
//     private params: Record<string, any> | null,
//     private config: ImportExcelConfig,
//     private processor: BaseProcessor,
//     private validationContext: ValidationContext,
//     private checkUnique: (uniqueData: UniqueData) => Promise<boolean>
//   ) {}

//   async validateRow(): Promise<{
//     isValid: boolean;
//     errors: string[];
//     rowData: any;
//     oldRowData: any;
//   }> {
//     this.errors = [];
//     // 拷贝一份旧的数据
//     const oldRowData = JSON.parse(JSON.stringify(this.rowData));
//     // 然后将数据进行转换
//     this.rowData = this.processor.transformOldData(this.rowData, this.config);
//     // 基础校验
//     for (const [excelCol, mapping] of Object.entries(
//       this.config.fieldMapping
//     )) {
//       const { targetField, rules } = mapping;
//       // 执行数据转换
//       this.rowData[targetField] = this.processor.transformValue(
//         this.rowData[targetField],
//         excelCol,
//         {
//           rowData: this.rowData,
//           config: this.config
//         }
//       );

//       // 必填校验
//       if (rules?.required && !this.rowData[targetField]) {
//         this.addError(
//           targetField,
//           `${this.config.fieldMapping[excelCol].targetName}：不能为空`
//         );
//         continue;
//       }

//       // 类型校验
//       if (rules?.type && this.rowData[targetField]) {
//         const isExit = await this.checkType(
//           this.rowData[targetField],
//           rules,
//           mapping
//         );
//         if (!isExit) continue;
//       }

//       // 自定义校验
//       const isExitCustom = this.checkCustom(targetField, mapping);
//       if (!isExitCustom) continue;

//       // 关联校验
//       if (this.rowData[targetField] && mapping.rules?.dependentRequired) {
//         for (const depCol of mapping.rules.dependentRequired) {
//           if (!this.rowData[this.config.fieldMapping[depCol].targetField]) {
//             this.addError(
//               targetField,
//               `${this.config.fieldMapping[excelCol].targetName}：当${this.config.fieldMapping[excelCol].targetName}存在时，${this.config.fieldMapping[depCol].targetName}必须填写`
//             );
//             continue;
//           }
//         }
//       }

//       // 唯一性校验
//       await this.checkUniqueData(mapping, excelCol, this.importType);
//     }

//     // 校验默认值
//     this.checkDefaultValue();

//     return {
//       isValid: this.errors.length === 0,
//       errors: this.errors,
//       rowData: this.rowData,
//       oldRowData
//     };
//   }

//   /**
//    * 唯一校验
//    * @param mapping
//    * @param excelCol
//    * @returns
//    */
//   async checkUniqueData(mapping: any, excelCol: string, importType: string) {
//     if (
//       mapping.rules?.unique &&
//       this.rowData[mapping.targetField] &&
//       mapping.rules?.uniqueField?.length
//     ) {
//       if (importType === (ImportType.REPEAT_THROW as string)) {
//         // 重复抛出
//         return await this.checkUniqueDataRepeatThrow(mapping, excelCol);
//       }
//       if (importType === (ImportType.REPEAT_UPDATE as string)) {
//         // 重复更新
//         this.rowData['update'] = true;
//       }
//       // 覆盖不做处理执行先增后加
//     } else {
//       return false;
//     }
//   }

//   /**
//    * 重复抛出
//    * @param mapping
//    * @param excelCol
//    * @returns
//    */
//   async checkUniqueDataRepeatThrow(mapping: any, excelCol: string) {
//     const uniqueData: UniqueData = {};
//     mapping.rules?.uniqueField?.forEach((field: string) => {
//       uniqueData[this.config.fieldMapping[field].targetField] =
//         this.rowData[field];
//     });
//     // 判断唯一性数据列表是否已存在该条数据
//     const isExist = this.validationContext.UniqueDataList.some(
//       (item: UniqueData) => isEqual(item, uniqueData)
//     );
//     const isUnique = await this.checkUnique(uniqueData);
//     if (isUnique || isExist) {
//       this.addError(
//         excelCol,
//         `${this.config.fieldMapping[excelCol].targetName}：${this.rowData[mapping.targetField]}已经存在`
//       );
//       return false;
//     } else {
//       // 加入唯一性数据列表
//       this.validationContext.UniqueDataList.push(uniqueData);
//     }
//     return isUnique ? true : false;
//   }

//   /**
//    * 自定义校验
//    * @param excelCol 下标
//    * @returns
//    */
//   checkCustom(targetField: string, mapping: any) {
//     if (this.rowData[targetField] && mapping.rules?.custom) {
//       // 存在自定义校验方法且值不为空
//       const value: any[] = [];
//       if (mapping.rules?.dependentRequired) {
//         // 检查是否存在依赖字段，存在则遍历
//         mapping.rules?.dependentRequired.forEach((item: string | number) => {
//           if (this.rowData[item]) {
//             value.push(this.rowData[item]);
//           }
//         });
//         value.push(this.rowData[targetField]);
//       }
//       // 若上述条件都不满足，则将当前字段的值传入校验
//       const res = mapping.rules.custom(
//         value.join(',').length ? value.join(',') : this.rowData[targetField]
//       );

//       // 验证通过返回true，否则添加错误信息返回false
//       return typeof res === 'string'
//         ? this.addError(targetField, `${mapping.targetName}：${res}`)
//         : true;
//     } else {
//       return true;
//     }
//   }

//   // 校验默认值
//   checkDefaultValue() {
//     // 若存在默认值配置，则进行数据处理
//     const value: any[] = [];
//     this.config?.defaultValue?.relationField?.forEach(
//       (item: string | number) => {
//         value.push(this.rowData[item]);
//       }
//     );
//     if (this.config?.defaultValue?.targetField) {
//       const res = this.config?.defaultValue?.transform?.(value.join(','));
//       this.rowData[this.config?.defaultValue?.targetField] = res;
//     }
//   }

//   /**
//    * 错误记录
//    * @param message 错误日志
//    */
//   async addError(targetField: string, message: string) {
//     this.errors.push({
//       key: targetField,
//       message
//     });
//     return false;
//   }

//   /**
//    * @param value 数据值
//    * @param rule 规则
//    * @param excelCol 下标
//    * @returns
//    */
//   async checkType(value: any, rule: FieldValidationRule, mapping: any) {
//     switch (rule.type) {
//       case 'string':
//         // 字符串类型检验
//         return this.checkString(value, rule, mapping);
//       case 'boolean':
//         // boolean校验
//         return this.checkBoolean(value, rule.booleanValues, mapping);
//       case 'date':
//         // 时间校验
//         return this.checkDate(value, mapping);
//       case 'enum':
//         // 枚举校验
//         return this.checkEnum(value, rule?.enum || [], mapping);
//       case 'arrayEnum':
//         // 数组枚举校验
//         return this.checkArrayEnum(value, rule?.enum || [], mapping);
//       case 'float':
//         // 浮点数校验
//         return this.checkFloat(value, rule, mapping);
//       case 'number':
//         // 数值校验
//         return this.checkNumber(value, rule, mapping);
//       // ...其他类型校验
//       default:
//         this.addError(
//           mapping.targetField,
//           `${mapping.targetName}：存在未知校验规则类型：${rule.type as string}`
//         );
//     }
//   }

//   /**
//    * 校验时间格式
//    * @param value 数据值
//    * @param rule 规则
//    * @param excelCol 下标
//    */
//   async checkDate(value: any, mapping: any): Promise<boolean> {
//     let date: Date | null = null;

//     // 处理 Excel 日期对象
//     if (typeof value === 'object') {
//       date = dayjs(value).toDate();
//     } else if (typeof value === 'string') {
//       // 处理字符串
//       const validFormat = DateFormatArr.find((format) =>
//         dayjs(value, format, true).isValid()
//       );

//       if (validFormat) {
//         date = dayjs(value, validFormat).toDate();
//       }
//     }

//     // 验证日期有效性
//     if (!date || isNaN(date.getTime())) {
//       this.addError(
//         mapping.targetField,
//         `${mapping.targetName}：无效的日期格式：${JSON.stringify(value)}`
//       );
//       return false;
//     }

//     // 存储转换后的日期
//     this.rowData[mapping.targetField] = date;
//     return true;
//   }

//   /**
//    * 校验数字格式
//    * @param value 数据值
//    * @param rule 规则
//    * @param excelCol 下标
//    * @returns
//    */
//   async checkNumber(
//     value: any,
//     rule: FieldValidationRule,
//     mapping: any
//   ): Promise<boolean> {
//     if (isNaN(parseInt(value))) {
//       this.addError(
//         mapping.targetField,
//         `${mapping.targetName}：数据不为数字型：${value}`
//       );
//       return false;
//     }
//     if (rule.minLength && value.toString().length < rule.minLength) {
//       this.addError(
//         mapping.targetField,
//         `${mapping.targetName}：长度不能小于${rule.minLength}`
//       );
//       return false;
//     }
//     if (rule.maxLength && value.toString().length > rule.maxLength) {
//       this.addError(
//         mapping.targetField,
//         `${mapping.targetName}：长度不能大于${rule.maxLength}`
//       );
//       return false;
//     }
//     this.rowData[mapping.targetField] = parseFloat(value);
//     return true;
//   }

//   /**
//    * 浮点数校验
//    * @param value 数据值
//    * @param excelCol 下标
//    */
//   async checkFloat(
//     value: any,
//     rule: FieldValidationRule,
//     mapping: any
//   ): Promise<boolean> {
//     if (isNaN(parseFloat(value))) {
//       this.addError(
//         mapping.targetField,
//         `${mapping.targetName}：数据不为浮点型：${value}`
//       );
//       return false;
//     }
//     if (rule.minLength && value.toString().length < rule.minLength) {
//       this.addError(
//         mapping.targetField,
//         `${mapping.targetName}：长度不能小于${rule.minLength}`
//       );
//       return false;
//     }
//     if (rule.maxLength && value.toString().length > rule.maxLength) {
//       this.addError(
//         mapping.targetField,
//         `${mapping.targetName}：长度不能大于${rule.maxLength}`
//       );
//       return false;
//     }
//     this.rowData[mapping.targetField] = parseFloat(value);
//     return true;
//   }

//   /**
//    * 枚举校验
//    * @param value 数据值
//    * @param enumValues 枚举值
//    * @param excelCol 下标
//    */
//   async checkEnum(
//     value: any,
//     enumValues: any[],
//     mapping: any
//   ): Promise<boolean> {
//     if (!enumValues.length) {
//       this.addError(mapping.targetField, `${mapping.targetName}：枚举值未配置`);
//       return false;
//     }
//     if (enumValues.indexOf(value) === -1) {
//       this.addError(
//         mapping.targetField,
//         `${mapping.targetName}：值不在枚举值范围内`
//       );
//       return false;
//     }
//     return true;
//   }

//   /**
//    * 枚举数组校验
//    * @param value 数据值
//    * @param enumValues 枚举值
//    * @param excelCol 下标
//    */
//   async checkArrayEnum(
//     value: any[],
//     enumValues: any[],
//     mapping: any
//   ): Promise<boolean> {
//     forEach(value, (item) => {
//       if (!enumValues.length) {
//         this.addError(
//           mapping.targetField,
//           `${mapping.targetName}：枚举值未配置`
//         );
//         return false;
//       }
//       if (enumValues.indexOf(item) === -1) {
//         this.addError(
//           mapping.targetField,
//           `${mapping.targetName}：值不在枚举值范围内`
//         );
//         return false;
//       }
//     });
//     return true;
//   }

//   /**
//    * boolean值校验
//    * @param value 数据值
//    * @param booleanValues 枚举值
//    * @param excelCol 下标
//    */
//   async checkBoolean(
//     value: any,
//     booleanValues: any,
//     mapping: any
//   ): Promise<boolean> {
//     let isExit = true;
//     if (!booleanValues) {
//       // 错误记录
//       this.addError(
//         mapping.targetField,
//         `${mapping.targetName}：boolean枚举值未配置`
//       );
//       isExit = false;
//     } else {
//       isExit = booleanValues
//         ? Object.keys(booleanValues).includes(value)
//         : false;
//       if (!isExit)
//         this.addError(
//           mapping.targetField,
//           `${mapping.targetName}：boolean枚举值错误`
//         );
//       this.rowData[mapping.targetField] = isExit ? booleanValues[value] : value;
//     }
//     return isExit;
//   }

//   /**
//    * 字符串校验
//    * @param value 数据值
//    * @param rule 校验规则
//    * @param excelCol 下标
//    */
//   async checkString(
//     value: any,
//     rule: FieldValidationRule,
//     mapping: any
//   ): Promise<boolean> {
//     if (rule.minLength && value.length < rule.minLength) {
//       this.addError(
//         mapping.targetField,
//         `${mapping.targetName}：长度不能小于${rule.minLength}`
//       );
//       return false;
//     }
//     if (rule.maxLength && value.length > rule.maxLength) {
//       this.addError(
//         mapping.targetField,
//         `${mapping.targetName}：长度不能大于${rule.maxLength}`
//       );
//       return false;
//     }
//     if (rule.pattern && !rule.pattern.test(value)) {
//       this.addError(mapping.targetField, `${mapping.targetName}：格式错误`);
//       return false;
//     }
//     return true;
//   }
// }
