import { BadRequestException } from '@nestjs/common';
import * as ExcelJS from 'exceljs';
import { Readable } from 'stream';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { FileManageService } from '@/modules/file-manage/file-manage.service';

import {
  getModuleConfig,
  getModuleProcessor
} from '../decorator/module.decorator';
import { ImportExcelDto } from '../import.dto';
import {
  ImportExcelConfig,
  ImportResult,
  RowError
  // ValidationContext
} from '../interface';
import { BaseProcessor } from './processors';

export class ImportManager {
  constructor(
    // 自动加载所有模块（需在模块入口导入）
    private readonly fileService: FileManageService,
    private readonly prisma: PrismaService
  ) {}

  private allErrors: any[] = [];
  private validData: any[] = [];

  async import(data: ImportExcelDto, reqUser: IReqUser): Promise<ImportResult> {
    const { moduleName, fileName, importType, params } = data;

    // 动态获取配置
    const config = getModuleConfig(moduleName);

    // 动态实例化处理器（自动注入依赖）
    const processor = getModuleProcessor(moduleName, this.prisma);

    // 1、从OBS获取文件流
    const fileStream = await this.fileService.download(fileName, reqUser);

    // 2、解析Excel
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.read(Readable.from(fileStream));
    // 获取Excel工作表名
    const worksheet = workbook.getWorksheet(config.sheetName);

    if (!worksheet) {
      throw new BadRequestException(`未找到工作表: ${config.sheetName}`);
    }

    // 3、数据收集
    this.allErrors = [];
    this.validData = [];

    // 4:逐行处理
    let currentRow = config.startRow; // 数据起始行
    const batchSize = config.batchSize; // 批量处理行数

    // 创建唯一校验上下文
    // const validationContext: ValidationContext = {
    //   // uniqueDataStore: new Map(),
    //   // compositeUniqueStore: new Map()
    //   UniqueDataList: []
    // };

    while (currentRow <= worksheet.rowCount) {
      // 解析数据逻辑
      await this.analysisExcelData(
        currentRow,
        importType,
        reqUser,
        worksheet,
        config,
        processor,
        params
        // validationContext
      );
      currentRow += batchSize; // 跳转到下一批数据行
    }

    return {
      total: worksheet.rowCount - config.startRow + 1,
      successCount: this.validData.length,
      errors: this.allErrors
    };
  }

  /**
   *
   * @param currentRow 当前行
   * @param reqUser 请求用户
   * @param worksheet 工作表
   * @param config 导入配置
   * @param processor 处理器
   * @param params 参数
   */
  async analysisExcelData(
    currentRow: number,
    importType: string,
    reqUser: IReqUser,
    worksheet: ExcelJS.Worksheet,
    config: ImportExcelConfig,
    processor: BaseProcessor,
    params: Record<string, any> | null
    // validationContext: ValidationContext
  ) {
    const batchStartRow = currentRow; // 锁定当前批次起始行
    // 获取批次数据
    const rows =
      worksheet.getRows(
        currentRow,
        Math.min(config.batchSize, worksheet.rowCount - batchStartRow + 1)
      ) || [];

    // 并行处理每行
    // const batchArr: any[] = [];
    // rows.map((row, index) => {
    //   batchArr.push(
    //     (async () => {
    //       const rowNumber = batchStartRow + index;
    //       const rowData = this.extractRowData(row, config); // 提取行数据

    //       // 使用独立处理器实例（需要实现clone方法）
    //       const rowProcessor = processor.clone();

    //       // 执行校验
    //       const errors = await rowProcessor.validateRow(
    //         rowNumber,
    //         rowData,
    //         config,
    //         rowProcessor,
    //         validationContext
    //       );
    //       return {
    //         ...errors
    //       };
    //     })()
    //   );
    // });
    // 获取到返回值
    // const batchResults = await Promise.all(batchArr);
    const batchResults: RowError[] = [];
    for (let index = 0; index < rows.length; index++) {
      const row = rows[index];
      const rowNumber = batchStartRow + index;
      const rowData = this.extractRowData(row, config); // 提取行数据

      // 使用独立处理器实例（需要实现clone方法）
      const rowProcessor = processor.clone();

      // 执行校验
      const errors = await rowProcessor.validateRow(
        rowNumber,
        importType,
        params,
        rowData,
        config,
        rowProcessor
        // validationContext
      );
      batchResults.push(errors);
    }

    // 拿到全量数据做唯一校核，取到违反唯一约束的数据，记录错误信息
    const res = await processor.checkUnique(batchResults, importType);

    // processor.checkRepeatData();

    // 收集有效数据和错误
    res.forEach((result) => {
      if (result.errors.length > 0) {
        // 错误数据回收
        this.allErrors.push({
          rowNum: result.row,
          oldRowData: result.oldRowData,
          errors: result.errors
        });
      } else {
        // 正确数据回收
        this.validData.push(processor.transformData(result.rowData, config));
      }
    });

    // 批量入库
    if (this.validData.length > 0) {
      await processor.saveData(this.validData, reqUser, params);
    }
  }

  // 辅助方法：提取行数据
  extractRowData(row: ExcelJS.Row, config: ImportExcelConfig) {
    return Object.keys(config.fieldMapping).reduce(
      (acc, col) => {
        const cell = row.getCell(col);
        acc[col] = cell.value;
        return acc;
      },
      {} as Record<string, any>
    );
  }
}
