import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import { FileManageService } from '../file-manage/file-manage.service';
import { ImportManager } from './common/manager';
import { ImportExcelDto } from './import.dto';

@Injectable()
export class ExcelService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly fileService: FileManageService
  ) {}

  async import(data: ImportExcelDto, reqUser: IReqUser) {
    const importManager = new ImportManager(
      this.fileService,
      this.prismaService
    );
    return importManager.import(data, reqUser);
  }
}
