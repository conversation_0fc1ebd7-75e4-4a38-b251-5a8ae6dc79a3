import { Body, Controller, Param, Post, Res } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { FastifyReply } from 'fastify';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import { FileManageService } from '../file-manage/file-manage.service';
import { ExcelService } from './excel.service';
import { ImportExcelDto } from './import.dto';

@ApiTags('excel导入')
@Controller('excel')
export class ExcelController {
  constructor(
    private readonly service: ExcelService,
    private readonly fileService: FileManageService
  ) {}

  @ApiOperation({
    summary: 'excel导入',
    description: 'excel导入'
  })
  @ApiResponse({ status: 200, description: 'excel导入成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/import')
  async import(@Body() data: ImportExcelDto, @ReqUser() reqUser: IReqUser) {
    return this.service.import(data, reqUser);
  }

  @ApiOperation({
    summary: 'excel模版下载',
    description: 'excel模版下载'
  })
  @ApiResponse({ status: 200, description: 'excel模版下载成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/down/:moduleName')
  async down(
    @Param('moduleName') moduleName: string,
    @Res() res: FastifyReply,
    @ReqUser() reqUser: IReqUser
  ) {
    let fileName = '';
    // 文件下载
    switch (moduleName) {
      case 'supplierDirectory':
        fileName = moduleName + '.xlsx';
        break;

      default:
        break;
    }
    const bis = await this.fileService.download(fileName, reqUser, 'template');
    res
      .header('Content-Disposition', `attachment; filename=${moduleName}.xlsx`)
      .header('Content-Type', 'application/octet-stream')
      .send(bis); // 直接发送 流 数据
  }
}
