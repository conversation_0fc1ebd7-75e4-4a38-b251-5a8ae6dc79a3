import './import-config/index';

import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';

import { FileManageService } from '../file-manage/file-manage.service';
import { ExcelController } from './excel.controller';
import { ExcelService } from './excel.service';

@Module({
  imports: [PrismaModule],
  controllers: [ExcelController],
  providers: [ExcelService, FileManageService]
})
export class ExcelModule {}
