import { HttpException, HttpStatus } from '@nestjs/common';

import { BaseProcessor } from '../common/processors';
import { ImportExcelConfig } from '../interface';

const MODULE_REGISTRY = new Map<
  string,
  {
    config: ImportExcelConfig;
    processor: new (...args: any[]) => BaseProcessor;
  }
>();

export function ExcelModule(config: ImportExcelConfig) {
  return function (target: new (...args: any[]) => BaseProcessor) {
    MODULE_REGISTRY.set(config.module, { config, processor: target });
  };
}

export function getModuleConfig(moduleName: string): ImportExcelConfig {
  const module = MODULE_REGISTRY.get(moduleName);
  if (!module)
    throw new HttpException(
      `未找到模块配置: ${moduleName}`,
      HttpStatus.BAD_REQUEST
    );
  return module.config;
}

export function getModuleProcessor(
  moduleName: string,
  ...deps: any[]
): BaseProcessor {
  const module = MODULE_REGISTRY.get(moduleName);

  if (!module)
    throw new HttpException(
      `未找到模块处理器: ${moduleName}`,
      HttpStatus.BAD_REQUEST
    );
  return new module.processor(...deps);
}
