import { forEach } from 'lodash';

import { IReqUser } from '@/common/interfaces/req-user.interface';

import { BaseProcessor } from '../../common/processors';
import { ExcelModule } from '../../decorator/module.decorator';
import { ImportType } from '../../import.dto';
import { ImportExcelConfig, RowError } from '../../interface';
import * as reginData from './regio.json';
import {
  SupplierDirectoryClassifyArr,
  TaxpayerQualificationArr
} from './supplier-directory.dto';

const SupplierDirectoryConfig: ImportExcelConfig = {
  module: 'SupplierDirectory',
  sheetName: 'Sheet1',
  headerRow: 1, // 表头行
  startRow: 2,
  batchSize: 200,
  fieldMapping: {
    A: {
      targetField: 'fullName',
      targetName: '供应商全称',
      rules: {
        type: 'string',
        required: true,
        minLength: 1,
        maxLength: 20
      }
    },
    B: {
      targetField: 'simpleName',
      targetName: '供应商简称',
      rules: {
        type: 'string',
        required: true,
        minLength: 1,
        maxLength: 20
      }
    },
    C: {
      targetField: 'creditCode',
      targetName: '统一社会信用代码',
      rules: {
        type: 'string',
        required: true,
        minLength: 18,
        maxLength: 18
      }
    },
    D: {
      targetField: 'registeredProvince',
      targetName: '注册所在省',
      rules: {
        type: 'string',
        required: false,
        minLength: 2,
        maxLength: 20,
        custom(value: any): boolean | string {
          const obj = reginData.filter((item) => item.label === value);
          if (!obj.length) {
            return '注册所在省不存在';
          } else {
            return true;
          }
        }
      }
    },
    E: {
      targetField: 'registeredCity',
      targetName: '注册所在市',
      rules: {
        dependentRequired: ['D'], // 当E列存在时D必填
        type: 'string',
        required: false,
        minLength: 2,
        maxLength: 20,
        custom(value: any) {
          const data = value.split(',');
          if (data.length !== 2) {
            return true;
          }
          // 查询所在省
          const province = reginData.filter((item) => item.label === data[0]);
          if (province.length) {
            // 获取所在市
            const city = province[0].list.filter(
              (item) => item.label === data[1]
            );
            return city.length ? true : '注册所在市不存在';
          } else {
            return '注册所在省不存在';
          }
        }
      }
    },
    F: {
      targetField: 'registeredCounty',
      targetName: '注册所在区县',
      rules: {
        dependentRequired: ['D', 'E'], // 当F列存在时D/E必填
        type: 'string',
        required: false,
        minLength: 2,
        maxLength: 20,
        custom(value: any) {
          const data = value.split(',');
          if (data.length !== 3) {
            return true;
          }
          // 查询所在省
          const province = reginData.filter((item) => item.label === data[0]);
          if (province.length) {
            // 获取所在市
            const city = province[0].list.filter(
              (item) => item.label === data[1]
            );
            if (city.length) {
              // 获取所在县
              const county = city[0].list.filter(
                (item) => item.label === data[2]
              );
              return county.length ? true : '注册所在县不存在';
            }
            return city.length ? true : '注册所在市不存在';
          } else {
            return '注册所在省不存在';
          }
        }
      }
    },
    G: {
      targetField: 'registeredAddress',
      targetName: '注册地址',
      rules: {
        type: 'string',
        required: false,
        minLength: 2,
        maxLength: 100
      }
    },
    H: {
      targetField: 'mainBusiness',
      targetName: '主营业务',
      rules: {
        type: 'string',
        required: false,
        minLength: 2,
        maxLength: 20
      }
    },
    I: {
      targetField: 'classify',
      targetName: '供应商分类',
      rules: {
        type: 'arrayEnum',
        required: true,
        enum: [
          'SERVICE_OUTSOURCING',
          'MECHANICAL_LEASING',
          'LABOR_SUBCONTRACTING',
          'MATERIAL_PURCHASING',
          'PROFESSIONAL_SUBCONTRACTING'
        ],
        custom: (val: any) => {
          forEach(val, (item) => {
            const element = SupplierDirectoryClassifyArr.find(
              (item1) => item1.value === item
            );
            if (!element) {
              return '存在未知分类，请仔细校验';
            }
          });
          return true;
        }
      },
      transform: (val: any) => {
        const array: string[] = [];
        const valueList = val.split(',') || val.split('，');
        forEach(valueList, (item) => {
          const element = SupplierDirectoryClassifyArr.find(
            (item1) => item1.label === item
          );
          if (element) {
            array.push(element.value);
          }
        });
        return array;
      }
    },
    J: {
      targetField: 'jobContent',
      targetName: '供应工作内容',
      rules: {
        type: 'string',
        required: false,
        minLength: 2,
        maxLength: 20
      }
    },
    K: {
      targetField: 'region',
      targetName: '供应区域',
      rules: {
        type: 'string',
        required: false,
        minLength: 2,
        maxLength: 20
      }
    },
    L: {
      targetField: 'unitType',
      targetName: '单位类型',
      rules: {
        type: 'string',
        required: false,
        minLength: 2,
        maxLength: 20
      }
    },
    M: {
      targetField: 'taxpayerQualification',
      targetName: '纳税人资质',
      rules: {
        type: 'enum',
        required: true,
        enum: ['GENERAL', 'SMALL_SCALE'],
        custom: (val: any) => {
          const element = TaxpayerQualificationArr.find(
            (item1) => item1.value === val
          );
          if (!element) {
            return '存在未知纳税人资质，请仔细校验';
          } else {
            return true;
          }
        }
      },
      transform: (val: any) => {
        switch (val) {
          case '一般纳税人':
            return 'GENERAL';

          case '小规模纳税人':
            return 'SMALL_SCALE';

          default:
            break;
        }
      }
    },
    N: {
      targetField: 'registeredCapital',
      targetName: '注册资金（万元）',
      rules: {
        type: 'float',
        required: false,
        maxLength: 10
      }
    },
    O: {
      targetField: 'establishAt',
      targetName: '成立时间',
      rules: {
        type: 'date',
        required: false
      }
    },
    P: {
      targetField: 'contactBy',
      targetName: '联系人',
      rules: {
        type: 'string',
        required: false,
        minLength: 2,
        maxLength: 20
      }
    },
    Q: {
      targetField: 'contactPhone',
      targetName: '联系电话',
      rules: {
        type: 'string',
        required: false,
        pattern: /^1[3-9]\d{9}$/
      }
    },
    R: {
      targetField: 'contactEmail',
      targetName: '联系人邮箱',
      rules: {
        type: 'string',
        required: false,
        minLength: 4,
        maxLength: 100,
        pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
      }
    },
    S: {
      targetField: 'LegalBy',
      targetName: '法定代表人/单位负责人',
      rules: {
        type: 'string',
        required: false,
        minLength: 2,
        maxLength: 100
      }
    },
    T: {
      targetField: 'relationEnterprise',
      targetName: '关联企业',
      rules: {
        type: 'string',
        required: false,
        minLength: 2,
        maxLength: 100
      }
    },
    U: {
      targetField: 'introductionAt',
      targetName: '引进时间',
      rules: {
        type: 'date',
        required: false
      }
    },
    V: {
      targetField: 'remark',
      targetName: '备注',
      rules: {
        type: 'string',
        required: false,
        minLength: 4,
        maxLength: 200
      }
    }
  },
  defaultValue: {
    targetField: 'regionCode',
    targetName: '省市区三级code',
    relationField: ['registeredProvince', 'registeredCity', 'registeredCounty'],
    rules: {
      type: 'string',
      required: false
    },
    transform: (val: any) => {
      const array: string[] = [];
      let obj: any[] = [];
      for (let index = 0; index < val.split(',').length; index++) {
        const element = val.split(',')[index];
        if (element.trim() === '') {
          break;
        }
        if (index === 0) {
          obj = reginData.filter((item) => item.label === element);
          array.push(obj.length ? obj[0].code : '');
        } else {
          obj = obj[0].list.filter((item: any) => item.label === element);
          array.push(obj.length ? obj[0].code : '');
        }
      }
      return array.join(',');
    }
  }
};

@ExcelModule(SupplierDirectoryConfig)
export class SupplierDirectoryProcessor extends BaseProcessor {
  // 克隆
  clone() {
    const cloned = new SupplierDirectoryProcessor(this.prisma);
    // 复制必要状态
    return cloned;
  }

  async checkUnique(
    allData: RowError[],
    importType: string
  ): Promise<RowError[]> {
    if (importType === (ImportType.REPEAT_THROW as string)) {
      // 重复抛出
      allData = await this.checkUniqueDataRepeatThrow(allData);
      // 校验数据库是否存在
      allData = await this.checkUniqueDataByDB(allData, importType);
    }
    if (importType === (ImportType.REPEAT_UPDATE as string)) {
      // 重复更新
      allData = await this.checkUniqueDataRepeatThrow(allData);
      // 校验数据库是否存在
      allData = await this.checkUniqueDataByDB(allData, importType);
    }
    if (importType === (ImportType.COVER as string)) {
      // 校验数据库是否存在
      allData = await this.checkUniqueDataByDB(allData, importType);
    }
    return allData;
  }

  /**
   * 重复抛出
   * @param mapping
   * @param excelCol
   * @returns
   */
  async checkUniqueDataRepeatThrow(allData: RowError[]): Promise<RowError[]> {
    const creditCodeSet = new Set<string>();
    // 取到数据
    return allData.map((item) => {
      if (item.errors.length) {
        return item;
      }
      if (creditCodeSet.has(item.rowData.creditCode)) {
        item.errors.push({
          key: 'creditCode',
          message: '数据重复'
        });
      } else {
        creditCodeSet.add(item.rowData.creditCode);
      }
      return item;
    });
  }

  /**
   * 数据库校验重复数据
   * @param allData
   * @param importType
   * @returns
   */
  async checkUniqueDataByDB(
    allData: RowError[],
    importType: string
  ): Promise<RowError[]> {
    const creditCodes = allData.map((item) => item.rowData.creditCode);
    const data = await this.prisma.supplierDirectory.findMany({
      select: {
        creditCode: true
      },
      where: {
        creditCode: {
          in: creditCodes
        },
        isDeleted: false
      }
    });
    allData = allData.map((item) => {
      if (data.some((item2) => item2.creditCode === item.rowData.creditCode)) {
        if (importType === (ImportType.REPEAT_UPDATE as string)) {
          item.rowData.update = true;
        }
        if (importType === (ImportType.REPEAT_THROW as string)) {
          item.errors.push({
            key: 'creditCode',
            message: '数据已存在'
          });
        }
      }
      return item;
    });
    return allData;
  }

  // 旧数据转换
  transformOldData(rowData: any, config: ImportExcelConfig): any {
    return Object.entries(config.fieldMapping).reduce(
      (acc: any, [excelCol, mapping]: [string, any]) => {
        const { targetField, rules } = mapping;
        let value = rowData[excelCol];
        if ((rules.type === 'string' || rules.type === 'float') && value) {
          value = rowData[excelCol].toString();
        }
        acc[targetField] = value;
        return acc;
      },
      {}
    );
  }

  // 保存数据转换
  transformData(rowData: any, config: ImportExcelConfig): any {
    return Object.entries(config.fieldMapping).reduce(
      (acc: any, [excelCol, mapping]: [string, any]) => {
        const { targetField, rules } = mapping;
        let value = rowData[targetField];
        if (rules.type === 'string' && value) {
          value = rowData[targetField].toString();
        }
        if (rules.type === 'array' && value) {
          value = new Array(value.split(','));
        }
        acc[targetField] = value;
        return acc;
      },
      {}
    );
  }

  async saveData(
    validData: any[],
    reqUser: IReqUser,
    params: Record<string, any>
  ) {
    // 合并数组
    await this.prisma.supplierDirectory.createMany({
      data: validData.map((item) => ({
        ...item,
        ...params,
        creator: reqUser.nickname,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }))
    });
  }
}
