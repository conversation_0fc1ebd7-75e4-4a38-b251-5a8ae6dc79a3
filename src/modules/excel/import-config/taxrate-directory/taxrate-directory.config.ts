import { IReqUser } from '@/common/interfaces/req-user.interface';

import { BaseProcessor } from '../../common/processors';
import { ExcelModule } from '../../decorator/module.decorator';
import { ImportType } from '../../import.dto';
import { ImportExcelConfig, RowError } from '../../interface';
import { TaxrateTypeArr } from './taxrate-directory.dto';

const TaxrateDirectoryConfig: ImportExcelConfig = {
  module: 'TaxrateDirectory',
  sheetName: 'Sheet1',
  headerRow: 1, // 表头行
  startRow: 2,
  batchSize: 200,
  fieldMapping: {
    A: {
      targetField: 'levelCode',
      targetName: '层次码',
      rules: {
        type: 'string',
        required: true,
        minLength: 1,
        maxLength: 100
      }
    },
    B: {
      targetField: 'code',
      targetName: '编码',
      rules: {
        type: 'string',
        required: true,
        minLength: 1,
        maxLength: 100
      }
    },
    C: {
      targetField: 'name',
      targetName: '名称',
      rules: {
        type: 'string',
        required: true,
        minLength: 1,
        maxLength: 100
      }
    },
    D: {
      targetField: 'type',
      targetName: '发票类型',
      rules: {
        type: 'string',
        required: false,
        minLength: 2,
        maxLength: 20,
        custom: (val: any) => {
          const element = TaxrateTypeArr.find((item1) => item1.value === val);
          if (!element) {
            return '存在未知发票类型，请仔细校验';
          } else {
            return true;
          }
        }
      },
      transform: (val: any) => {
        switch (val) {
          case '增值税专用发票':
            return 'EXCLUSIVE_USE';

          case '增值税普通发票':
            return 'GENERAL_USE';

          default:
            break;
        }
      }
    },
    E: {
      targetField: 'taxRate',
      targetName: '税率',
      rules: {
        type: 'string',
        required: false,
        minLength: 1,
        maxLength: 100
      }
    },
    F: {
      targetField: 'remark',
      targetName: '备注',
      rules: {
        type: 'string',
        required: false,
        minLength: 1,
        maxLength: 200
      }
    }
  }
};

@ExcelModule(TaxrateDirectoryConfig)
export class TaxrateDirectoryProcessor extends BaseProcessor {
  async checkUnique(
    allData: RowError[],
    importType: string
  ): Promise<RowError[]> {
    if (importType === (ImportType.REPEAT_THROW as string)) {
      // 重复抛出
      allData = await this.checkUniqueDataRepeatThrow(allData);
      // 校验数据库是否存在
      allData = await this.checkUniqueDataByDB(allData, importType);
    }
    if (importType === (ImportType.REPEAT_UPDATE as string)) {
      // 重复更新
      allData = await this.checkUniqueDataRepeatThrow(allData);
      // 校验数据库是否存在
      allData = await this.checkUniqueDataByDB(allData, importType);
    }
    if (importType === (ImportType.COVER as string)) {
      // 校验数据库是否存在
      allData = await this.checkUniqueDataByDB(allData, importType);
    }
    return allData;
  }

  /**
   * 重复抛出
   * @param mapping
   * @param excelCol
   * @returns
   */
  async checkUniqueDataRepeatThrow(allData: RowError[]): Promise<RowError[]> {
    const creditCodeSet = new Set<string>();
    // 取到数据
    return allData.map((item) => {
      if (item.errors.length) {
        return item;
      }
      if (creditCodeSet.has(item.rowData.creditCode)) {
        item.errors.push({
          key: 'creditCode',
          message: '数据重复'
        });
      } else {
        creditCodeSet.add(item.rowData.creditCode);
      }
      return item;
    });
  }

  /**
   * 数据库校验重复数据
   * @param allData
   * @param importType
   * @returns
   */
  async checkUniqueDataByDB(
    allData: RowError[],
    importType: string
  ): Promise<RowError[]> {
    const creditCodes = allData.map((item) => item.rowData.creditCode);
    const data = await this.prisma.supplierDirectory.findMany({
      select: {
        creditCode: true
      },
      where: {
        creditCode: {
          in: creditCodes
        },
        isDeleted: false
      }
    });
    allData = allData.map((item) => {
      if (data.some((item2) => item2.creditCode === item.rowData.creditCode)) {
        if (importType === (ImportType.REPEAT_UPDATE as string)) {
          item.rowData.update = true;
        }
        if (importType === (ImportType.REPEAT_THROW as string)) {
          item.errors.push({
            key: 'creditCode',
            message: '数据已存在'
          });
        }
      }
      return item;
    });
    return allData;
  }

  // 克隆
  clone() {
    const cloned = new TaxrateDirectoryProcessor(this.prisma);
    // 复制必要状态
    return cloned;
  }

  // 旧数据转换
  transformOldData(rowData: any, config: ImportExcelConfig): any {
    return Object.entries(config.fieldMapping).reduce(
      (acc: any, [excelCol, mapping]: [string, any]) => {
        const { targetField, rules } = mapping;
        let value = rowData[excelCol];
        if ((rules.type === 'string' || rules.type === 'float') && value) {
          value = rowData[excelCol].toString();
        }
        acc[targetField] = value;
        return acc;
      },
      {}
    );
  }

  // 保存数据转换
  transformData(rowData: any, config: ImportExcelConfig): any {
    return Object.entries(config.fieldMapping).reduce(
      (acc: any, [excelCol, mapping]: [string, any]) => {
        const { targetField, rules } = mapping;
        let value = rowData[excelCol];
        if (rules.type === 'string' && value) {
          value = rowData[excelCol].toString();
        }
        if (rules.type === 'array' && value) {
          value = new Array(value.split(','));
        }
        acc[targetField] = value;
        return acc;
      },
      {}
    );
  }

  async saveData(
    validData: any[],
    reqUser: IReqUser,
    params: Record<string, any>
  ) {
    // 合并数组
    await this.prisma.supplierDirectory.createMany({
      data: validData.map((item) => ({
        ...item,
        ...params,
        creator: reqUser.nickname,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }))
    });
    // await this.prisma.$transaction(async (tx) => {
    //   await tx(validData);
    // });
  }
}
