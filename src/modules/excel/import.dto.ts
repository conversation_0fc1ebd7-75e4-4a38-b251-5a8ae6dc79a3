import { Optional } from '@nestjs/common';
import { ApiProperty } from '@nestjs/swagger';
import { IsIn, IsNotEmpty, IsString } from 'class-validator';

export enum ImportType {
  'REPEAT_THROW' = 'REPEAT_THROW', // 重复抛出
  'REPEAT_UPDATE' = 'REPEAT_UPDATE', // 重复更新
  'COVER' = 'COVER' // 覆盖
}

export class ImportExcelDto {
  @ApiProperty({ description: '文件名', example: 'xxxxxxx.xlsx' })
  @IsNotEmpty({ message: '文件名不能为空' })
  @IsString({ message: '文件名必须为字符串' })
  fileName: string;

  @ApiProperty({ description: '模块名', example: 'SupplierDirectory' })
  @IsNotEmpty({ message: '模块名不能为空' })
  @IsString({ message: '模块名必须为字符串' })
  moduleName: string;

  @ApiProperty({ description: '导入类型', example: 'REPEAT_THROW' })
  @IsNotEmpty({ message: '导入类型不能为空' })
  @IsIn(Object.values(ImportType), {
    message: '导入类型必须是有效枚举值'
  })
  @IsString({ message: '导入类型必须为字符串' })
  importType: ImportType;

  @ApiProperty({ description: '导入参数', example: 'xxxxxxx' })
  @Optional()
  params: Record<string, any> | null;
}
