import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';
import { PlatformModule } from '@/modules/platform/platform.module';

import { MaterialAllocationFromController } from './material-allocation-from/material-allocation-from.controller';
import { MaterialAllocationFormRepository } from './material-allocation-from/material-allocation-from.repositories';
import { MaterialAllocationFromService } from './material-allocation-from/material-allocation-from.service';
import { MaterialAllocationFromAttachmentController } from './material-allocation-from-attachment/material-allocation-from-attachment.controller';
import { MaterialAllocationFromAttachmentService } from './material-allocation-from-attachment/material-allocation-from-attachment.service';
import { MaterialAllocationFromDetailController } from './material-allocation-from-detail/material-allocation-from-detail.controller';
import { MaterialAllocationFormDetailRepository } from './material-allocation-from-detail/material-allocation-from-detail.repositories';
import { MaterialAllocationFromDetailService } from './material-allocation-from-detail/material-allocation-from-detail.service';

@Module({
  imports: [PrismaModule, PlatformModule],
  controllers: [
    MaterialAllocationFromController,
    MaterialAllocationFromDetailController,
    MaterialAllocationFromAttachmentController
  ],
  providers: [
    MaterialAllocationFormDetailRepository,
    MaterialAllocationFormRepository,
    MaterialAllocationFromService,
    MaterialAllocationFromDetailService,
    MaterialAllocationFromAttachmentService
  ]
})
export class MaterialAllocationFromModule {}
