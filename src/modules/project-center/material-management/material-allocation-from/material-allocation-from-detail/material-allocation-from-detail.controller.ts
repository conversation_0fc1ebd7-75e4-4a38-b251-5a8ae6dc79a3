import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { EditMoveDto } from '@/common/dtos/common.dto';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  MaterialAllocationFormChooseCategoryTreeResDto,
  MaterialAllocationFormChooseDetailsQueryDto,
  MaterialAllocationFormChooseDetailsResDto,
  MaterialAllocationFormDetailCreateDto,
  MaterialAllocationFormDetailResDto,
  MaterialAllocationFormDetailUpdateDto
} from './material-allocation-from-detail.dto';
import { MaterialAllocationFromDetailService } from './material-allocation-from-detail.service';

@ApiTags('调拨单/明细')
@Controller('material-allocation-from-detail')
export class MaterialAllocationFromDetailController {
  constructor(private readonly service: MaterialAllocationFromDetailService) {}

  @ApiOperation({ summary: '查询可选的材料分类' })
  @ApiResponse({
    status: 200,
    description: '获取查询可选的材料分类成功',
    type: MaterialAllocationFormChooseCategoryTreeResDto,
    isArray: true
  })
  @Get('/choose/materialCategory')
  async getChooseMaterialCategory(
    @ReqUser() reqUser: IReqUser
  ): Promise<MaterialAllocationFormChooseCategoryTreeResDto[]> {
    return await this.service.getChooseMaterialCategory(reqUser);
  }

  @ApiOperation({ summary: '查询可选的材料明细' })
  @ApiResponse({
    status: 200,
    description: '获取查询可选的材料明细成功',
    type: MaterialAllocationFormChooseDetailsResDto,
    isArray: true
  })
  @Get('/choose/materialDetails')
  async getChooseMaterialDetails(
    @Query() query: MaterialAllocationFormChooseDetailsQueryDto,
    @ReqUser() reqUser: IReqUser
  ): Promise<MaterialAllocationFormChooseDetailsResDto[]> {
    return await this.service.getChooseMaterialDetails(query, reqUser);
  }

  @ApiOperation({ summary: '材料明细保存' })
  @ApiResponse({
    status: 200,
    description: '材料明细保存成功'
  })
  @Post('')
  async add(
    @Body() body: MaterialAllocationFormDetailCreateDto,
    @ReqUser() reqUser: IReqUser
  ): Promise<boolean> {
    return await this.service.add(body, reqUser);
  }

  @ApiOperation({ summary: '修改调拨数量' })
  @ApiResponse({
    status: 200,
    description: '修改调拨数量成功'
  })
  @Patch('/:id')
  async update(
    @Param('id') id: string,
    @Body() body: MaterialAllocationFormDetailUpdateDto,
    @ReqUser() reqUser: IReqUser
  ): Promise<boolean> {
    return await this.service.update(id, body, reqUser);
  }

  @ApiOperation({ summary: '删除父级' })
  @ApiResponse({
    status: 200,
    description: '删除父级成功'
  })
  @Delete('/:id')
  async delete(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser
  ): Promise<boolean> {
    return await this.service.delete(id, reqUser);
  }

  @ApiOperation({ summary: '查询调拨单明细' })
  @ApiResponse({
    status: 200,
    description: '查询调拨单明细成功',
    type: MaterialAllocationFormDetailResDto,
    isArray: true
  })
  @Get('/:id')
  async getList(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    return await this.service.getDetailList(id, reqUser);
  }

  @ApiOperation({
    summary: '调拨单材料上下移',
    description: '调拨单材料上下移'
  })
  @ApiResponse({
    status: 200,
    description: '调拨单材料上下移成功'
  })
  @Post('/move')
  async move(
    @ReqUser() reqUser: IReqUser,
    @Query('fromId') fromId: string,
    @Query('toId') toId: string
  ) {
    await this.service.move(reqUser, fromId, toId);
    return true;
  }
}
