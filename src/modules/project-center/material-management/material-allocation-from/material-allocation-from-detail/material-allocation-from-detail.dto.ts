import { ApiProperty, PickType } from '@nestjs/swagger';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString
} from 'class-validator';

import { DetailType, MaterialType } from '@/prisma/generated';

export class BaseMaterialAllocationFormDetailDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '父级id' })
  parentId?: DetailType;

  @ApiProperty({ description: '调拨单ID' })
  @IsNotEmpty({ message: '调拨单ID不能为空' })
  @IsString({ message: '调拨单ID必须是字符串' })
  materialAllocationFromId: string;

  @ApiProperty({ description: '明细ID (收料单id/退库单id)' })
  detailId?: string;

  @ApiProperty({ description: '明细类型 (收料单/退库单)' })
  detailType?: DetailType;

  @ApiProperty({ description: '明细的单据编码' })
  detailCode?: string;

  @ApiProperty({ description: '明细的单据时间' })
  detailDate?: string;

  @ApiProperty({ description: '材料ID' })
  @IsNotEmpty({ message: '材料ID不能为空' })
  @IsString({ message: '材料ID必须是字符串' })
  materialId: string;

  @ApiProperty({ description: '材料名称' })
  @IsNotEmpty({ message: '材料名称不能为空' })
  @IsString({ message: '材料名称必须是字符串' })
  materialName: string;

  @ApiProperty({ description: '材料规格' })
  @IsNotEmpty({ message: '材料规格不能为空' })
  @IsString({ message: '材料规格必须是字符串' })
  materialSpec: string;

  @ApiProperty({ description: '计量单位' })
  @IsOptional({ message: '计量单位可以为空' })
  @IsString({ message: '计量单位必须是字符串' })
  unit?: string;

  @ApiProperty({ description: '在库数量' })
  @IsOptional({ message: '在库数量可以为空' })
  @IsNumber({}, { message: '在库数量必须是数字' })
  inStockQuantity: number;

  @ApiProperty({ description: '在库单价' })
  @IsOptional({ message: '在库单价可以为空' })
  @IsString({ message: '在库单价必须是字符串' })
  inStockPrice: number;

  @ApiProperty({ description: '调拨数量' })
  @IsOptional({ message: '调拨数量可以为空' })
  @IsNumber({}, { message: '调拨数量必须是数字' })
  allocationQuantity: number;

  @ApiProperty({ description: '调拨单价' })
  @IsOptional({ message: '调拨单价可以为空' })
  @IsNumber({}, { message: '调拨单价必须是数字' })
  allocationPrice: number;

  @ApiProperty({ description: '调拨金额' })
  @IsOptional({ message: '调拨金额可以为空' })
  @IsNumber({}, { message: '调拨金额必须是数字' })
  allocationAmount: number;

  @ApiProperty({ description: '排序号' })
  orderNo: number;

  @ApiProperty({ description: '备注' })
  @IsOptional({ message: '备注可以为空' })
  @IsString({ message: '备注必须是字符串' })
  remark?: string | null;
}

export class MaterialAllocationFormDetailResDto extends PickType(
  BaseMaterialAllocationFormDetailDto,
  [
    'id',
    'parentId',
    'materialAllocationFromId',
    'detailId',
    'detailType',
    'materialId',
    'materialName',
    'materialSpec',
    'unit',
    'orderNo',
    'inStockQuantity',
    'inStockPrice',
    'allocationQuantity',
    'allocationPrice',
    'allocationAmount',
    'remark'
  ] as const
) {}

export class MaterialAllocationFormDetailCreateDto {
  @ApiProperty({
    description: '数据数组'
  })
  @IsNotEmpty({ message: '数据数组不能为空' })
  @IsArray({ message: '数据数组必须为数组' })
  list: MaterialAllocationFormDetailCreateListDto[];

  @ApiProperty({
    description: '调拨单id'
  })
  @IsNotEmpty({ message: '调拨单id不能为空' })
  @IsString({ message: '调拨单id格式不正确' })
  materialAllocationFromId: string;
}

export class MaterialAllocationFormDetailCreateListDto extends PickType(
  BaseMaterialAllocationFormDetailDto,
  [
    'materialId',
    'materialName',
    'materialSpec',
    'unit',
    'inStockQuantity',
    'inStockPrice',
    'allocationQuantity',
    'remark',
    'orderNo'
  ] as const
) {}

export class MaterialAllocationFormDetailUpdateDto extends PickType(
  BaseMaterialAllocationFormDetailDto,
  ['allocationQuantity', 'remark'] as const
) {}

export class MaterialAllocationFormChooseCategoryTreeResDto {
  @ApiProperty({
    description: 'id'
  })
  id: string;

  @ApiProperty({
    description: '编码'
  })
  code: string;

  @ApiProperty({
    description: '类别名称'
  })
  name: string;

  @ApiProperty({
    description: '核算类型'
  })
  materialType: MaterialType;

  @ApiProperty({
    description: '备注'
  })
  remark: string;
}

export class MaterialAllocationFormChooseDetailsResDto {
  @ApiProperty({
    description: '材料id'
  })
  materialId: string;

  @ApiProperty({
    description: '材料名称'
  })
  materialName: string;

  @ApiProperty({
    description: '材料规格型号'
  })
  materialSpec: string;

  @ApiProperty({
    description: '材料计量单位'
  })
  unit: string;

  @ApiProperty({
    description: '单价'
  })
  price: number;

  @ApiProperty({
    description: '库存数量'
  })
  inventoryQuantity: number;
}

export class MaterialAllocationFormChooseDetailsDto extends MaterialAllocationFormChooseDetailsResDto {
  @ApiProperty({
    description: '收料单id'
  })
  receivingId?: string;
}

export class MaterialAllocationFormChooseDetailsQueryDto {
  @ApiProperty({
    description: '调拨单id'
  })
  @IsNotEmpty({ message: '调拨单id不能为空' })
  @IsString({ message: '调拨单id格式不正确' })
  materialAllocationFromId: string;

  @ApiProperty({
    description: '分类id'
  })
  @IsOptional()
  @IsString({ message: '分类id格式不正确' })
  categoryId?: string;
}
