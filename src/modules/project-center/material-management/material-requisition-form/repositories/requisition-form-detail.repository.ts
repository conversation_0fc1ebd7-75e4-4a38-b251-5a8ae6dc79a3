import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

@Injectable()
export class RequisitionFormDetailRepository {
  constructor(private readonly prisma: PrismaService) {}

  // 获取在库材料的分类
  async selectMaterialCategoryList(reqUser: IReqUser) {
    const result = await this.prisma.$queryRaw<any[]>`
      with temp_material_detail as (
        select distinct material_dictionary_category_id
        from material_dictionary_detail mdd
        where mdd.is_deleted = false
          and mdd.tenant_id = ${reqUser.tenantId}
          and mdd.id in (
            select material_id
            from material_receiving_inventory
            where is_deleted = false
              and tenant_id = ${reqUser.tenantId}
              and org_id = ${reqUser.orgId}
              and inventory_quantity > 0
          )
      )
      ,leaf_category as (
        select distinct mdc.full_id
        from material_dictionary_category mdc
        where mdc.is_deleted = false
          and mdc.tenant_id = ${reqUser.tenantId}
          and mdc.parent_id is null
          and mdc.status != 'NOT_ENABLED'
          and mdc.id in (select material_dictionary_category_id from temp_material_detail)
      )
      select distinct
        parent.id
        ,parent.code
        ,parent.name
        ,parent.type
        ,parent.remark
      from material_dictionary_category parent
      join leaf_category leaf
        on position(parent.id in leaf.full_id) > 0
      where parent.is_deleted = false
        and parent.tenant_id = ${reqUser.tenantId}
    `;

    return result;
  }

  async selectMaterialDetailList(reqUser: IReqUser, categoryId: string) {
    const result = await this.prisma.$queryRaw<any[]>`
      with temp_category as (
        select distinct id
        from material_dictionary_category
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and position(${categoryId} in full_id) > 0
      )
      select
        mdd.id
        ,mdd.name
        ,mdd.code
        ,mdd.unit
        ,mdd.material_type
        ,mrr.inventory_quantity
      from material_dictionary_detail mdd
      join material_receiving_inventory mri
        on mri.is_deleted = false
        and mri.tenant_id = ${reqUser.tenantId}
        and mri.org_id = ${reqUser.orgId}
        and mri.material_id = mdd.id
      where mdd.is_deleted = false
        and mdd.tenant_id = ${reqUser.tenantId}
        and mdd.material_dictionary_category_id in (select id from temp_category)
        and mdd.id in (
          select material_id
          from material_receiving_repertory
          where is_deleted = false
            and tenant_id = ${reqUser.tenantId}
            and org_id = ${reqUser.orgId}
            and inventory_quantity > 0
        )
    `;

    return result;
  }
}
