import { BadRequestException, Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';

import { TimeListResponseDto } from '@/common/dtos/common.dto';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { SubmitStatus } from '@/prisma/generated';

import {
  QueryRequisitionBillListDto,
  RequisitionBillListResponseDto,
  RequisitionDepartmentListResponseDto,
  UpdateRequisitionBillDto
} from '../material-requisition-form.dto';
import { RequisitionFormRepository } from '../repositories/requisition-form.repository';

@Injectable()
export class MaterialRequisitionFormService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly repository: RequisitionFormRepository
  ) {}

  // 获取时间筛选列表
  async getTimeList(reqUser: IReqUser): Promise<TimeListResponseDto[]> {
    const dates = await this.prisma.materialRequisitionForm.findMany({
      select: {
        year: true,
        month: true,
        day: true
      },
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      },
      orderBy: [{ year: 'desc' }, { month: 'desc' }, { day: 'desc' }]
    });

    const resultMap: Record<string, TimeListResponseDto> = {};
    for (const time of dates) {
      // 添加父级，年_月
      if (!resultMap[`${time.year}_${time.month}`]) {
        resultMap[`${time.year}_${time.month}`] = {
          id: `${time.year}_${time.month}`,
          parentId: null,
          year: time.year,
          month: time.month,
          count: 0
        };
      }
      resultMap[`${time.year}_${time.month}`].count += 1;

      // 添加子级 年_月_日
      if (!resultMap[`${time.year}_${time.month}_${time.day}`]) {
        resultMap[`${time.year}_${time.month}_${time.day}`] = {
          id: `${time.year}_${time.month}_${time.day}`,
          parentId: `${time.year}_${time.month}`,
          year: time.year,
          month: time.month,
          day: time.day,
          count: 0
        };
      }
      resultMap[`${time.year}_${time.month}_${time.day}`].count += 1;
    }

    return Object.values(resultMap);
  }

  async getBillList(
    reqUser: IReqUser,
    query: QueryRequisitionBillListDto
  ): Promise<RequisitionBillListResponseDto[]> {
    const bills = await this.repository.selectBillList(reqUser, query);

    const suppliers = (await this.repository.selectSupplierList(
      reqUser
    )) as Array<{
      id: string;
      name: string;
    }>;
    const supplierMap = suppliers.reduce(
      (pre: Record<string, { id: string; name: string }>, cur) => {
        pre[cur.id] = cur;
        return pre;
      },
      {}
    );

    for (const item of bills) {
      item.departmentName = supplierMap[item.departmentId]?.name;
    }

    return bills;
  }

  // 新增领料单
  async addRequisitionBill(
    reqUser: IReqUser
  ): Promise<RequisitionBillListResponseDto> {
    // 获取进场时间
    const dateNow = dayjs();
    const year = dateNow.year();
    const month = dateNow.month() + 1;
    const day = dateNow.date();

    const code = await this.generateBillCode(reqUser, year, month);

    const data = await this.prisma.materialRequisitionForm.create({
      data: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        creator: reqUser.nickname,
        createBy: reqUser.id,
        updateBy: reqUser.id,
        code,
        year,
        month,
        day
      },
      select: {
        id: true,
        settlementStatus: true,
        code: true,
        creator: true,
        year: true,
        month: true,
        day: true,
        auditStatus: true,
        submitStatus: true,
        createAt: true,
        updateAt: true
      }
    });

    return data as RequisitionBillListResponseDto;
  }

  // 生成单据编码
  private async generateBillCode(
    reqUser: IReqUser,
    year: number,
    month: number
  ) {
    const code = ['领', `${year}${String(month).padStart(2, '0')}`, '001'];
    const lastInspectionBill =
      await this.prisma.materialRequisitionForm.findFirst({
        where: {
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        },
        orderBy: {
          code: 'desc'
        }
      });

    if (lastInspectionBill) {
      const lastCode = lastInspectionBill.code;
      const lastCodeNumber = parseInt(lastCode.split('-')[2], 10);
      code[2] = String(lastCodeNumber + 1).padStart(3, '0');
    }

    return code.join('-');
  }

  // 获取可选的领料单位
  async getRequisitionDepartmentList(
    reqUser: IReqUser
  ): Promise<RequisitionDepartmentListResponseDto[]> {
    const suppliers = await this.repository.selectSupplierList(reqUser);

    return suppliers as RequisitionDepartmentListResponseDto[];
  }

  // 编辑领料单
  async editRequisitionBill(reqUser: IReqUser, data: UpdateRequisitionBillDto) {
    // 获取原始数据
    const oldData = await this.prisma.materialRequisitionForm.findFirst({
      where: {
        id: data.id,
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId
      },
      select: {
        id: true,
        supplierId: true,
        auditStatus: true,
        submitStatus: true,
        partName: true
      }
    });

    if (!oldData) throw new BadRequestException('领料单据不存在!');

    const sqlList = [];
    // 如果是提交单据，需要校验库存是否足够
    if (
      oldData.submitStatus !== data.submitStatus &&
      data.submitStatus === SubmitStatus.SUBMITTED
    ) {
      // @TODO:
    }

    await this.prisma.$transaction(async (tx) => {
      // 更新领料单
      await this.prisma.materialRequisitionForm.update({
        where: {
          id: data.id,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        data: {
          ...data,
          updateBy: reqUser.id
        }
      });

      // 如果是提交数据
      if (oldData && oldData.submitStatus !== data.submitStatus) {
        if (data.submitStatus === SubmitStatus.SUBMITTED) {
          // 提交数据
        } else if (data.auditStatus === SubmitStatus.PENDING) {
          // 取消提交数据
        }
      }
    });

    return true;
  }

  // 提交数据处理
  private async handleSubmitData() {}

  // 取消提交数据处理
  private async handleCancelSubmit() {}

  // 删除领料单
  async deleteRequisitionBill(reqUser: IReqUser, id: string) {
    const requisitionBill = await this.prisma.materialRequisitionForm.findFirst(
      {
        where: {
          id,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        select: {
          submitStatus: true,
          auditStatus: true
        }
      }
    );

    // 校验领料单提交状态
    if (
      requisitionBill &&
      requisitionBill.submitStatus === SubmitStatus.SUBMITTED
    ) {
      throw new BadRequestException('已提交的领料单不允许删除!');
    }

    await this.prisma.$transaction(async (tx) => {
      // 删除单据
      await tx.materialRequisitionForm.updateMany({
        where: {
          id,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });

      // 删除明细
      await tx.materialRequisitionFormDetail.updateMany({
        where: {
          requisitionFormId: id,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });

      // 删除附件
      await tx.materialRequisitionFormAttachment.updateMany({
        where: {
          requisitionFormId: id,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
    });

    return true;
  }
}
