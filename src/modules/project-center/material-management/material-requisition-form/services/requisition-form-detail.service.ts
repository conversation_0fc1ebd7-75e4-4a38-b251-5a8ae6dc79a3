import { Injectable } from '@nestjs/common';

import { CommonRepositories } from '@/common/common-repositories';
import { MaterialTypeText } from '@/common/constants/common.constant';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { MaterialType } from '@/prisma/generated';

import {
  CreateRequisitionDetailDto,
  MaterialCategoryListResponseDto,
  MaterialDetailListResponseDto,
  UpdateRequisitionDetailDto
} from '../material-requisition-form.dto';
import { RequisitionFormDetailRepository } from '../repositories/requisition-form-detail.repository';

@Injectable()
export class MaterialRequisitionFormDetailService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly repository: RequisitionFormDetailRepository
  ) {}

  // 获取领料单明细
  async getRequisitionDetailList(reqUser: IReqUser, requisitionBillId: string) {
    const result = await this.prisma.materialRequisitionFormDetail.findMany({
      where: {
        requisitionFormId: requisitionBillId,
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId
      },
      orderBy: {
        orderNo: 'asc'
      }
    });

    return result;
  }

  // 获取可选择的材料字典分类
  async getMaterialCategoryList(
    reqUser: IReqUser
  ): Promise<MaterialCategoryListResponseDto[]> {
    const result = await this.repository.selectMaterialCategoryList(reqUser);

    for (const item of result) {
      item.type = MaterialTypeText[item.type as MaterialType];
    }

    return result;
  }

  // 获取可选择的材料明细
  async getMaterialDetailList(
    reqUser: IReqUser,
    categoryId: string
  ): Promise<MaterialDetailListResponseDto[]> {
    const result = await this.repository.selectMaterialDetailList(
      reqUser,
      categoryId
    );

    for (const item of result) {
      item.type = MaterialTypeText[item.type as MaterialType];
    }

    return result;
  }

  async addRequisitionDetails(
    reqUser: IReqUser,
    requisitionBillId: string,
    body: CreateRequisitionDetailDto
  ) {}

  // 删除领料单明细
  async deleteRequisitionDetail(reqUser: IReqUser, id: string) {
    await this.prisma.$transaction(async (tx) => {
      // 删除父级（材料）
      await this.prisma.materialRequisitionFormDetail.update({
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        },
        where: {
          id,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        }
      });
      // 删除自己明细（收料单引用）
      await tx.materialRequisitionFormDetail.updateMany({
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        },
        where: {
          parentId: id,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        }
      });
    });

    return true;
  }

  // 编辑领料单明细
  async editRequisitionDetail(
    reqUser: IReqUser,
    data: UpdateRequisitionDetailDto
  ) {}

  // 领料单明细上移下移
  async moveRequisitionDetail(reqUser: IReqUser, fromId: string, toId: string) {
    await CommonRepositories.changeDataOrderNo(this.prisma, {
      tenantId: reqUser.tenantId,
      orgId: reqUser.orgId,
      fromId,
      toId,
      tableName: 'material_requisition_form_detail'
    });
    return true;
  }
}
