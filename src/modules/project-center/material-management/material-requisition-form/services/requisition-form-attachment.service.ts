import { Injectable } from '@nestjs/common';
import { update } from 'lodash';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import {
  CreateRequisitionAttachmentDto,
  RequisitionAttachmentResponseDto
} from '../material-requisition-form.dto';

@Injectable()
export class MaterialRequisitionFormAttachmentService {
  constructor(private readonly prisma: PrismaService) {}

  // 新增附件
  async addRequisitionAttachment(
    reqUser: IReqUser,
    data: CreateRequisitionAttachmentDto
  ) {
    await this.prisma.materialRequisitionFormAttachment.create({
      data: {
        ...data,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }
    });
    return true;
  }

  // 获取附件列表
  async getRequisitionAttachmentList(
    reqUser: IReqUser,
    requisitionBillId: string
  ): Promise<RequisitionAttachmentResponseDto[]> {
    const result = await this.prisma.materialRequisitionFormAttachment.findMany(
      {
        where: {
          requisitionFormId: requisitionBillId
        },
        select: {
          id: true,
          fileName: true,
          fileKey: true,
          fileSize: true,
          fileExt: true,
          fileContentType: true
        }
      }
    );

    return result;
  }

  // 删除附件
  async deleteRequisitionAttachment(reqUser: IReqUser, id: string) {
    await this.prisma.materialRequisitionFormAttachment.updateMany({
      where: {
        id,
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
    return true;
  }
}
