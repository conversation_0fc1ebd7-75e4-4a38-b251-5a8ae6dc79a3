import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { TimeListResponseDto } from '@/common/dtos/common.dto';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  CreateRequisitionAttachmentDto,
  CreateRequisitionDetailDto,
  MaterialCategoryListResponseDto,
  MaterialDetailListResponseDto,
  QueryRequisitionBillListDto,
  RequisitionAttachmentResponseDto,
  RequisitionBillListResponseDto,
  RequisitionDepartmentListResponseDto,
  UpdateRequisitionBillDto,
  UpdateRequisitionDetailDto
} from './material-requisition-form.dto';
import { MaterialRequisitionFormService } from './services/requisition-form.service';
import { MaterialRequisitionFormAttachmentService } from './services/requisition-form-attachment.service';
import { MaterialRequisitionFormDetailService } from './services/requisition-form-detail.service';

@ApiTags('领料单')
@Controller('material-requisition-form')
export class MaterialRequisitionFormController {
  constructor(
    private readonly requisitionFormService: MaterialRequisitionFormService,
    private readonly requisitionFormDetailService: MaterialRequisitionFormDetailService,
    private readonly requisitionFormAttachmentService: MaterialRequisitionFormAttachmentService
  ) {}

  @ApiOperation({
    summary: '获取时间筛选列表',
    description: '获取时间筛选列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取时间筛选列表',
    type: TimeListResponseDto,
    isArray: true
  })
  @Get('time-list')
  async getTimeList(@ReqUser() reqUser: IReqUser) {
    return await this.requisitionFormService.getTimeList(reqUser);
  }

  @ApiOperation({
    summary: '获取单据列表',
    description: '获取单据列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取单据列表',
    type: RequisitionBillListResponseDto,
    isArray: true
  })
  @Get('requisition-bill/list')
  async getBillList(
    @ReqUser() reqUser: IReqUser,
    @Query() query: QueryRequisitionBillListDto
  ) {
    return await this.requisitionFormService.getBillList(reqUser, query);
  }

  @ApiOperation({
    summary: '新增领料单',
    description: '新增领料单'
  })
  @ApiResponse({
    status: 200,
    description: '新增成功',
    type: RequisitionBillListResponseDto
  })
  @Post('requisition-bill/_add')
  async addRequisitionBill(@ReqUser() reqUser: IReqUser) {
    return await this.requisitionFormService.addRequisitionBill(reqUser);
  }

  @ApiOperation({
    summary: '获取可选的领料单位',
    description: '获取可选的领料单位'
  })
  @ApiResponse({
    status: 200,
    type: RequisitionDepartmentListResponseDto,
    isArray: true
  })
  @Get('requisition-bill/requisition-department/list')
  async getRequisitionDepartmentList(@ReqUser() reqUser: IReqUser) {
    return await this.requisitionFormService.getRequisitionDepartmentList(
      reqUser
    );
  }

  @ApiOperation({
    summary: '修改领料单',
    description: '修改领料单'
  })
  @ApiResponse({
    status: 200,
    description: '修改成功'
  })
  @Post('requisition-bill/_edit')
  async editRequisitionBill(
    @ReqUser() reqUser: IReqUser,
    @Body() data: UpdateRequisitionBillDto
  ) {
    return await this.requisitionFormService.editRequisitionBill(reqUser, data);
  }

  @ApiOperation({
    summary: '删除领料单',
    description: '删除领料单'
  })
  @ApiResponse({ status: 200, description: '删除成功' })
  @Delete('requisition-bill/:id')
  async deleteRequisitionBill(
    @ReqUser() reqUser: IReqUser,
    @Param('id') id: string
  ) {
    return await this.requisitionFormService.deleteRequisitionBill(reqUser, id);
  }

  @ApiOperation({
    summary: '获取领料单明细',
    description: '获取领料单明细'
  })
  @ApiResponse({ status: 200, description: '获取成功' })
  @Get(':requisitionBillId/detail/list')
  async getRequisitionDetailList(
    @ReqUser() reqUser: IReqUser,
    @Param('requisitionBillId') requisitionBillId: string
  ) {
    return await this.requisitionFormDetailService.getRequisitionDetailList(
      reqUser,
      requisitionBillId
    );
  }

  @ApiOperation({
    summary: '获取可选择的材料字典分类',
    description: '获取可选择的材料字典分类'
  })
  @ApiResponse({
    status: 200,
    description: '获取可选择的材料字典分类',
    type: MaterialCategoryListResponseDto,
    isArray: true
  })
  @Get(':requisitionBillId/detail/material-category/list')
  async getMaterialCategoryList(@ReqUser() reqUser: IReqUser) {
    return await this.requisitionFormDetailService.getMaterialCategoryList(
      reqUser
    );
  }

  @ApiOperation({
    summary: '获取可选择的材料明细',
    description: '获取可选择的材料明细'
  })
  @ApiResponse({
    status: 200,
    description: '获取可选择的材料明细',
    type: MaterialDetailListResponseDto,
    isArray: true
  })
  @Get(':requisitionBillId/detail/material-detail/list')
  async getMaterialDetailList(
    @ReqUser() reqUser: IReqUser,
    @Query('categoryId') categoryId: string
  ) {
    return await this.requisitionFormDetailService.getMaterialDetailList(
      reqUser,
      categoryId
    );
  }

  @ApiOperation({
    summary: '新增领料单明细',
    description: '新增领料单明细'
  })
  @ApiResponse({ status: 200, description: '新增成功' })
  @Post(':requisitionBillId/detail/_add')
  async addRequisitionDetails(
    @ReqUser() reqUser: IReqUser,
    @Param('requisitionBillId') requisitionBillId: string,
    @Body() body: CreateRequisitionDetailDto
  ) {
    return await this.requisitionFormDetailService.addRequisitionDetails(
      reqUser,
      requisitionBillId,
      body
    );
  }

  @ApiOperation({
    summary: '删除领料单明细',
    description: '删除领料单明细'
  })
  @ApiResponse({ status: 200, description: '删除成功' })
  @Delete('requisition-detail/:id')
  async deleteRequisitionDetail(
    @ReqUser() reqUser: IReqUser,
    @Param('id') id: string
  ) {
    return await this.requisitionFormDetailService.deleteRequisitionDetail(
      reqUser,
      id
    );
  }

  @ApiOperation({
    summary: '编辑领料单明细',
    description: '编辑领料单明细'
  })
  @ApiResponse({ status: 200, description: '编辑成功' })
  @Post('requisition-detail/_edit')
  async editRequisitionDetail(
    @ReqUser() reqUser: IReqUser,
    @Body() data: UpdateRequisitionDetailDto
  ) {
    return await this.requisitionFormDetailService.editRequisitionDetail(
      reqUser,
      data
    );
  }

  @ApiOperation({
    summary: '领料单明细上移下移',
    description: '领料单明细上移下移'
  })
  @ApiResponse({ status: 200, description: '编辑成功' })
  @Post('requisition-detail/_move')
  async moveRequisitionDetail(
    @ReqUser() reqUser: IReqUser,
    @Query('fromId') fromId: string,
    @Query('toId') toId: string
  ) {
    return await this.requisitionFormDetailService.moveRequisitionDetail(
      reqUser,
      fromId,
      toId
    );
  }

  @ApiOperation({
    summary: '领料单附件上传',
    description: '领料单附件上传'
  })
  @ApiResponse({ status: 200, description: '上传成功' })
  @Post('requisition-bill/attachment')
  async addRequisitionAttachment(
    @ReqUser() reqUser: IReqUser,
    @Body() data: CreateRequisitionAttachmentDto
  ) {
    return await this.requisitionFormAttachmentService.addRequisitionAttachment(
      reqUser,
      data
    );
  }

  @ApiOperation({
    summary: '获取领料单附件列表',
    description: '获取领料单附件列表'
  })
  @ApiResponse({
    status: 200,
    type: RequisitionAttachmentResponseDto,
    isArray: true
  })
  @Get(':requisitionBillId/attachment/list')
  async getRequisitionAttachmentList(
    @ReqUser() reqUser: IReqUser,
    @Param('requisitionBillId') requisitionBillId: string
  ) {
    return await this.requisitionFormAttachmentService.getRequisitionAttachmentList(
      reqUser,
      requisitionBillId
    );
  }

  @ApiOperation({
    summary: '领料单附件删除',
    description: '领料单附件删除'
  })
  @ApiResponse({ status: 200, description: '删除成功' })
  @Delete('requisition-bill/attachment/:id')
  async deleteRequisitionAttachment(
    @ReqUser() reqUser: IReqUser,
    @Param('id') id: string
  ) {
    return await this.requisitionFormAttachmentService.deleteRequisitionAttachment(
      reqUser,
      id
    );
  }
}
