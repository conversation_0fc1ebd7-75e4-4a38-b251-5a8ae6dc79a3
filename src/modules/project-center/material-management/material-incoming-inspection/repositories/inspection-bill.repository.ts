import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { Prisma } from '@/prisma/generated';

import {
  InspectionBillListResponseDto,
  QueryInspectionBillListDto
} from '../material-incoming-inspection.dto';

@Injectable()
export class MaterialIncomingInspectionRepository {
  constructor(private readonly prisma: PrismaService) {}

  async selectBillList(
    reqUser: IReqUser,
    query: QueryInspectionBillListDto
  ): Promise<InspectionBillListResponseDto[]> {
    const { onlyViewSelf = false } = query;

    const inspectionBills: InspectionBillListResponseDto[] = await this.prisma
      .$queryRaw`
      with temp_material_categories as (
        select
          miid.incoming_inspection_id,
          STRING_AGG(distinct
            case
              when position('|' in mdc.full_name) > 0
              then split_part(mdc.full_name, '|', 2)
              else split_part(mdc.full_name, '|', 1)
            end,
            ','
          ) filter (where mdc.full_name is not null) material_categories
        from material_incoming_inspection_detail miid
        join material_dictionary_detail mdd
          on mdd.id = miid.material_id
          and mdd.is_deleted = false
          and mdd.tenant_id = ${reqUser.tenantId}
        join material_dictionary_category mdc
          on mdc.id = mdd.material_dictionary_category_id
          and mdc.is_deleted = false
          and mdc.tenant_id = ${reqUser.tenantId}
          and mdc.org_id = mdd.org_id
        where miid.is_deleted = false
          and miid.tenant_id = ${reqUser.tenantId}
          and miid.org_id = ${reqUser.orgId}
        group by miid.incoming_inspection_id
      )
      , temp_incoming_inspection_detail as (
        select
          miid.incoming_inspection_id
          ,CASE
            WHEN mrid.incoming_inspection_detail_id IS NOT NULL THEN 1
            ELSE 0
          END AS is_received
        from material_incoming_inspection_detail miid
        left join (
          select distinct incoming_inspection_detail_id
          from material_receiving_incoming_detail
          where is_deleted = false
            and tenant_id = ${reqUser.tenantId}
            and org_id = ${reqUser.orgId}
        ) mrid
          on mrid.incoming_inspection_detail_id = miid.id
        where miid.is_deleted = false
          and miid.tenant_id = ${reqUser.tenantId}
          and miid.org_id = ${reqUser.orgId}
      )
      , temp_received_status as (
        select
          incoming_inspection_id
          ,CASE
            WHEN received_count = 0 THEN 'UN_RECEIVED'
            WHEN received_count = total_count THEN 'PARTIAL_RECEIVED'
            ELSE 'RECEIVED'
          END AS material_receipt_status
        from (
          select
             incoming_inspection_id
             ,SUM(is_received) AS received_count
             ,COUNT(*) AS total_count
          from temp_incoming_inspection_detail
          group by incoming_inspection_id
        ) tt
      )
      select
        COALESCE(temp_received_status.material_receipt_status, 'UN_RECEIVED') as material_receipt_status
        ,mii.id
        ,mii.code
        ,mii.purchase_type
        ,mii.supplier_id
        ,mii.supplier_name
        ,mii.contract_id
        ,mii.contract_name
        ,tmc.material_categories
        ,mii.creator
        ,mii.year
        ,mii.month
        ,mii.day
        ,mii.submit_status
        ,mii.audit_status
        ,mii.excel_file_key
        ,mii.qr_code_url
        ,o.name as org_name
      from material_incoming_inspection mii
      join platform_meta.org o
        on o.tenant_id = mii.tenant_id
        and o.id = mii.org_id
      left join temp_received_status
        on temp_received_status.incoming_inspection_id = mii.id
      left join temp_material_categories tmc
        on tmc.incoming_inspection_id = mii.id
      where mii.tenant_id = ${reqUser.tenantId}
        and mii.org_id = ${reqUser.orgId}
        and mii.is_deleted = false
        ${onlyViewSelf ? Prisma.sql`and mii.create_by = ${reqUser.id}` : Prisma.empty}
        ${query.year ? Prisma.sql`and mii.year = ${query.year}` : Prisma.empty}
        ${query.month ? Prisma.sql`and mii.month = ${query.month}` : Prisma.empty}
        ${query.day ? Prisma.sql`and mii.day = ${query.day}` : Prisma.empty}
      order by mii.code desc, mii.id desc
    `;

    return inspectionBills;
  }

  async getBillInfoById(tenantId: string, orgId: string, id: string) {
    const result = await this.prisma.$queryRaw<any[]>`
      select
        mii.material_receipt_status
        ,mii.id
        ,mii.code
        ,mii.purchase_type
        ,mii.supplier_id
        ,mii.supplier_name
        ,mii.contract_id
        ,mii.contract_name
        ,mii.creator
        ,mii.year
        ,mii.month
        ,mii.day
        ,mii.submit_status
        ,mii.audit_status
        ,o.name as org_name
      from material_incoming_inspection mii
      join platform_meta.org o
        on o.tenant_id = mii.tenant_id
        and o.id = mii.org_id
      where mii.tenant_id = ${tenantId}
        and mii.org_id = ${orgId}
        and mii.id = ${id}
        and mii.is_deleted = false
    `;

    return result[0];
  }
}
