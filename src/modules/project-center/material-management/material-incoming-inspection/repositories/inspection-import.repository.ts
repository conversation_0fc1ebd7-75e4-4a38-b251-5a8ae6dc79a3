import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { ContractTemplateClassifyType } from '@/prisma/generated';

@Injectable()
export class MaterialIncomingInspectionImportRepository {
  constructor(private readonly prisma: PrismaService) {}

  // 查询当前及上级组织的所有供应商名录
  async selectAllSuppliers(tenantId: string, orgId: string) {
    const result = await this.prisma.$queryRaw<any[]>`
      with temp_org_ids as (
        select distinct parent.id
        from platform_meta.org parent
        join platform_meta.org child
          on child.is_deleted = false
          and child.tenant_id = parent.tenant_id
          and child.id = ${orgId}
          and position(parent.full_id in child.full_id)>0
        where parent.is_deleted = false
          and parent.tenant_id = ${tenantId}
      )
      select
        id,
        full_name
      from ecost.supplier_directory
      where is_deleted = false
        and tenant_id = ${tenantId}
        and org_id in (select id from temp_org_ids)
        and id in (
          select
            mc.party_b
          from material_contract as mc
          join contract_template as ct
            on ct.is_deleted = false
            and ct.tenant_id = mc.tenant_id
            and ct.id = mc.contract_template_id
            and ct.classify = ${ContractTemplateClassifyType.MATERIALS_PURCHASING}::"ContractTemplateClassifyType"
          where mc.is_deleted = false
            and mc.tenant_id = ${tenantId}
            and mc.org_id = ${orgId}
            and mc.parent_id is null
        )
    `;
    return result;
  }

  // 查询当前组织的物资采购合同
  async selectMaterialsPurchasingContracts(tenantId: string, orgId: string) {
    const result: any[] = await this.prisma.$queryRaw`
      with temp_material as (
        select
          tt.material_contract_id
          ,COALESCE(STRING_AGG(tt.material_dictionary_detail_id, '-'), '') as material_ids
        from (
          select material_contract_id, material_dictionary_detail_id from contract_consume_material_details where is_deleted = false and tenant_id = ${tenantId} and org_id = ${orgId}
          union all
          select material_contract_id, material_dictionary_detail_id from contract_concrete_details where is_deleted = false and tenant_id = ${tenantId} and org_id = ${orgId}
          union all
          select material_contract_id, material_dictionary_detail_id from contract_turnover_material_details where is_deleted = false and tenant_id = ${tenantId} and org_id = ${orgId}
        ) tt
        group by tt.material_contract_id
      )
      select
        mc.id
        ,mc.name
        ,mc.party_b
        ,mc.party_b_type
        ,temp_material.material_ids
      from material_contract as mc
      join contract_template as ct
        on ct.is_deleted = false
        and ct.tenant_id = mc.tenant_id
        and ct.id = mc.contract_template_id
        and ct.classify = ${ContractTemplateClassifyType.MATERIALS_PURCHASING}::"ContractTemplateClassifyType"
      left join temp_material
        on temp_material.material_contract_id = mc.id
      where mc.is_deleted = false
        and mc.tenant_id = ${tenantId}
        and mc.org_id = ${orgId}
        and mc.parent_id is null
    `;

    return result;
  }

  // 查询当前及以上组织的材料信息
  async selectAllMaterials(tenantId: string, orgId: string) {
    const result: any[] = await this.prisma.$queryRaw`
      with temp_org_ids as (
        select distinct parent.id
        from platform_meta.org parent
        join platform_meta.org child
          on child.is_deleted = false
          and child.tenant_id = parent.tenant_id
          and child.id = ${orgId}
          and position(parent.full_id in child.full_id)>0
        where parent.is_deleted = false
          and parent.tenant_id = ${tenantId}
      )
      select
        id
        ,code
        ,name
        ,specification_model as spec
        ,metering_unit as unit
        ,type
      from material_dictionary_detail
      where is_deleted = false
        and tenant_id = ${tenantId}
        and org_id in (select id from temp_org_ids)
        and material_dictionary_version_id in (
          select version_id from account_material_dictionary_version
          where is_deleted = false
            and tenant_id = ${tenantId}
            and org_id = ${orgId}
        )
    `;

    return result;
  }

  // 根据年月分组，取进场验收单的最大的最后三位编码
  async selectInspectionCodeNo(tenantId: string, orgId: string) {
    const result: any[] = await this.prisma.$queryRaw`
      select
        year
        ,month
        ,max(right(code, 3)) as code_no
      from material_incoming_inspection
      where is_deleted = false
        and tenant_id = ${tenantId}
        and org_id = ${orgId}
      group by year, month
    `;

    return result;
  }

  async selectMaterialUnit(tenantId: string, orgId: string) {
    const result: any[] = await this.prisma.$queryRaw`
      with material_unit as (
        select
          id as material_id
          ,metering_unit as unit
        from material_dictionary_detail
        where is_deleted = false
          and material_dictionary_version_id in (
            select version_id from account_material_dictionary_version
            where tenant_id = ${tenantId}
              and org_id = ${orgId}
          )
      )
      , material_dictionary_unit as (
        select
          material_dictionary_detail_id as material_id
          ,unit
        from material_dictionary_unit_calculation
        where is_deleted = false
          and tenant_id = ${tenantId}
          and material_dictionary_detail_id in (select material_id from material_unit)
      )
      select
        tt.material_id
        ,string_agg(distinct tt.unit, ',') optional_units
      from (
        select * from material_unit
        union all
        select * from material_dictionary_unit
      ) tt
      group by tt.material_id
    `;

    return result;
  }

  async selectContractMaterialUnit(tenantId: string, orgId: string) {
    const result: any[] = await this.prisma.$queryRaw`
      select
        material_contract_id
        ,material_detail_id as material_id
        ,string_agg(distinct unit, ',') optional_units
      from material_contract_unit_calculation
      where is_deleted = false
        and tenant_id = ${tenantId}
        and org_id = ${orgId}
      group by material_contract_id, material_id
    `;

    return result;
  }
}
