import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';
import { PlatformModule } from '@/modules/platform/platform.module';

import { MaterialContractModule } from '../material-contract/material-contract.module';
import { MaterialIncomingInspectionController } from './material-incoming-inspection.controller';
import { MaterialIncomingInspectionRepository } from './repositories/inspection-bill.repository';
import { MaterialIncomingInspectionDetailRepository } from './repositories/inspection-detail.repository';
import { MaterialIncomingInspectionImportRepository } from './repositories/inspection-import.repository';
import { MaterialIncomingInspectionAttachmentService } from './services/inspection-attachment.service';
import { MaterialIncomingInspectionListService } from './services/inspection-bill.service';
import { MaterialIncomingInspectionDetailService } from './services/inspection-detail.service';
import { MaterialIncomingInspectionImportService } from './services/inspection-import.service';

@Module({
  imports: [PrismaModule, PlatformModule, MaterialContractModule],
  controllers: [MaterialIncomingInspectionController],
  providers: [
    MaterialIncomingInspectionListService,
    MaterialIncomingInspectionDetailService,
    MaterialIncomingInspectionAttachmentService,
    MaterialIncomingInspectionImportService,
    MaterialIncomingInspectionRepository,
    MaterialIncomingInspectionDetailRepository,
    MaterialIncomingInspectionImportRepository
  ]
})
export class materialIncomingInspectionModule {}
