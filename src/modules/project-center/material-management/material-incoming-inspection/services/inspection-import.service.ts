import { Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';
import * as uuid from 'uuid';

import { PurchaseTypeEn } from '@/common/constants/common.constant';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { ExcelService } from '@/modules/platform/excel.service';
import { MaterialType, PurchaseType } from '@/prisma/generated';

import {
  BulkSaveInspectionBillDataDto,
  ImportExcelDataBodyDto,
  ImportExcelDataResponseDto,
  ImportExcelDataResponseItemDto,
  MaterialSearchType,
  QueryUnitSelectionDto,
  SupplierAndContractResponseDto,
  UnitSelectionResponseDto
} from '../material-incoming-inspection.dto';
import { MaterialIncomingInspectionDetailRepository } from '../repositories/inspection-detail.repository';
import { MaterialIncomingInspectionImportRepository } from '../repositories/inspection-import.repository';
import { MaterialIncomingInspectionDetailService } from './inspection-detail.service';

@Injectable()
export class MaterialIncomingInspectionImportService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly excelService: ExcelService,
    private readonly repository: MaterialIncomingInspectionImportRepository,
    private readonly detailRepository: MaterialIncomingInspectionDetailRepository,
    private readonly detailService: MaterialIncomingInspectionDetailService
  ) {}

  // 导入进场验收单数据
  async importExcelData(
    req: Request,
    reqUser: IReqUser,
    data: ImportExcelDataBodyDto
  ): Promise<ImportExcelDataResponseDto> {
    const { tenantId, orgId } = reqUser;
    // 1.获取解析excel数据
    let importData = await this.excelService.getExcelImportData(req, {
      fileKey: data.fileKey,
      type: data.type
    });
    importData = importData['物资进场验收单'];

    const supplierAndContractsMap = {};
    const contractMaterialMap = {};
    const allMaterialMap = {};
    const transferInMaterialMap = {};

    const [
      inspectionBillMap,
      materialCodeMap,
      inspectionCodeNoMap,
      { contractMaterialUnitMap, materialUnitMap }
    ] = await Promise.all([
      this.getExistsInspectionBillMap(tenantId, orgId),
      this.getAllMaterialsMap(tenantId, orgId),
      this.getExistsInspectionCodeNoMap(tenantId, orgId),
      this.getExistsMaterialUnitMap(tenantId, orgId)
    ]);

    // 排序
    importData.sort((a: Record<string, any>, b: Record<string, any>) => {
      const dateA = dayjs(a.date);
      const dateB = dayjs(b.date);

      // 非法日期处理：无效日期排最后
      if (!dateA.isValid() && !dateB.isValid()) return 0;
      if (!dateA.isValid()) return 1;
      if (!dateB.isValid()) return -1;

      return dateB.valueOf() - dateA.valueOf(); // 降序
    });

    // 2.数据合法性校验
    let errorCount = 0;
    const totalCount = importData.length;
    const successData: ImportExcelDataResponseItemDto[] = [];
    const errorData: ImportExcelDataResponseItemDto[] = [];
    for (const item of importData) {
      let errorFlag = false;

      // 校验进场日期
      if (!dayjs(item.siteEntryDate, 'YYYY-MM-DD', true).isValid()) {
        item.siteEntryDateMsg = '进场日期输入错误，请修改！';
        errorFlag = true;
      } else {
        const year = dayjs(item.siteEntryDate).year();
        const month = dayjs(item.siteEntryDate).month() + 1;
        const day = dayjs(item.siteEntryDate).date();
        item.year = year;
        item.month = month;
        item.day = day;

        let codeNo = 1;
        if (inspectionCodeNoMap[`${year}-${month}`]) {
          inspectionCodeNoMap[`${year}-${month}`]++;
          codeNo = inspectionCodeNoMap[`${year}-${month}`];
        } else {
          inspectionCodeNoMap[`${year}-${month}`] = codeNo;
        }
        item.code = `验-${year}${String(month).padStart(2, '0')}-${String(codeNo).padStart(3, '0')}`;
      }

      // 采购类型校验
      item.purchaseType = PurchaseTypeEn[item.purchaseType as string];
      if (!Object.values(PurchaseType).includes(item.purchaseType)) {
        item.purchaseTypeMsg =
          '采购类型必须是[自采、集采、甲指、甲供、调入]中的一个，请修改！';
        errorFlag = true;
      } else {
        const supplierAndContracts = await this.getSupplierAndContracts(
          reqUser,
          supplierAndContractsMap,
          item.purchaseType as PurchaseType
        );

        const supplier = supplierAndContracts.find(
          (i) => i.name === item.supplierName
        );

        // 校验供应商名称
        if (!supplier) {
          item.supplierNameMsg = '供应商名录不存在该供应商名称，请检查！';
          errorFlag = true;
        } else {
          item.supplierId = supplier.id;
        }

        // 校验合同名称
        // 如果存在供应商，且不为零星供应商，且不为调入类型，校验合同
        if (
          supplier &&
          supplier.contracts &&
          supplier.name !== '零星材料供应商' &&
          item.purchaseType !== PurchaseType.TRANSFER_IN
        ) {
          const contract = supplier.contracts.find(
            (i) => i.name === item.contractName
          );

          // 合同名称校验
          if (!contract) {
            item.contractNameMsg = '合同名称系统内不存在，请检查！';
            errorFlag = true;
          } else {
            item.contractId = contract.id;
          }
        }

        // 材料编码校验
        // 如果存在供应商并且存在合同，校验合同下的材料
        if (supplier && item.contractId) {
        } else if (
          supplier &&
          (supplier.name !== '零星材料供应商' ||
            item.purchaseType !== PurchaseType.TRANSFER_IN)
        ) {
          // 校验材料字典的材料
        }

        if (!materialCodeMap[item.materialCode]) {
          // <1> 如果材料编码不存在，提示错误
          // <2> 如果材料编码存在，合同存在，校验材料是否存在于合同中
          // <3> 如果材料编码存在，采购类型为甲供，校验材料是否消耗材
          item.materialCodeMsg = '材料编码系统内不存在，请检查！';
          errorFlag = true;
        } else {
          item.materialId = materialCodeMap[item.materialCode].id;
          item.materialSpec = materialCodeMap[item.materialCode].spec;
          item.materialName = materialCodeMap[item.materialCode].name;
          item.unit = materialCodeMap[item.materialCode].unit;
          if (
            item.contractId &&
            (item.purchaseType === PurchaseType.SELF_PURCHASE ||
              item.purchaseType === PurchaseType.CENTRALIZED_PURCHASE ||
              item.purchaseType === PurchaseType.PARTY_A_DIRECTED)
          ) {
            //
            item.materialCodeMsg = '合同内不存在此材料。';
            errorFlag = true;
          } else if (
            item.purchaseType === PurchaseType.PARTY_A_SUPPLIED &&
            materialCodeMap[item.materialCode].type !==
              MaterialType.CONSUME_MATERIAL
          ) {
            item.materialCodeMsg = '材料核算类型不是消耗材料，请检查！';
            errorFlag = true;
          }
        }

        // 计量单位校验
        if (item.contractId && item.materialId) {
          if (
            !contractMaterialUnitMap[`${item.contractId}@${item.materialId}`] ||
            !contractMaterialUnitMap[
              `${item.contractId}@${item.materialId}`
            ].includes(item.unit)
          ) {
            item.unitMsg = '计量单位数据错误，请检查！';
            errorFlag = true;
          }
        } else if (item.materialId) {
          if (
            !materialUnitMap[item.materialId] ||
            !materialUnitMap[item.materialId].includes(item.unit)
          ) {
            item.unitMsg = '计量单位数据错误，请检查！';
            errorFlag = true;
          }
        }

        // 进场数量数据类型校验
        if (Number.isNaN(Number(item.siteEntryQuantity))) {
          item.siteEntryQuantityMsg = '数据类型错误，请修改！';
          errorFlag = true;
        }

        // 验收数量数据类型校验
        if (Number.isNaN(Number(item.actualQuantity))) {
          item.actualQuantityMsg = '数据类型错误，请修改！';
          errorFlag = true;
        }
      }

      if (errorFlag) {
        errorData.push(item);
        errorCount++;
      } else {
        successData.push(item);
      }
    }

    // 3.数据入库
    await this.processAndSaveData(reqUser, successData, inspectionBillMap);

    return {
      errorData,
      totalCount,
      errorCount,
      successCount: totalCount - errorCount
    };
  }

  async processAndSaveData(
    reqUser: IReqUser,
    successData: ImportExcelDataResponseItemDto[],
    inspectionBillMap: Record<string, any>
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 1.处理数据
    const idMap: Record<string, string> = {};
    const insertInspectionBills: any[] = []; // 要新增的验收单单据
    const updateInspectionBillIds: string[] = []; // 用于删除旧单据的明细，覆盖新增明细
    const insertInspectionDetails: any[] = []; // 要新增的验收单明细
    for (const item of successData) {
      if (!item.supplierId || !item.contractId) continue;

      const oldInspectionBill =
        inspectionBillMap[`${item.supplierId}@${item.contractId}`];

      // 单据数据处理， 防止重复处理
      if (!idMap[`${item.supplierId}@${item.contractId}`]) {
        let id: string;
        if (oldInspectionBill) {
          id = oldInspectionBill.id;
          updateInspectionBillIds.push(oldInspectionBill.id);

          idMap[`${item.supplierId}@${item.contractId}`] = id;
        } else {
          if (!idMap[`${item.supplierId}@${item.contractId}`]) {
            idMap[`${item.supplierId}@${item.contractId}`] = uuid.v7();
          }

          id = idMap[`${item.supplierId}@${item.contractId}`] || uuid.v7();
          insertInspectionBills.push({
            tenantId,
            orgId,
            id,
            code: item.code,
            purchaseType: item.purchaseType,
            supplierId: item.supplierId,
            supplierName: item.supplierName,
            contractId: item.contractId,
            contractName: item.contractName,
            creator: reqUser.nickname,
            year: item.year,
            month: item.month,
            day: item.day,
            createBy: userId,
            updateBy: userId,
            updateAt: new Date()
          });
        }
      }

      // 单据明细数据处理
      insertInspectionDetails.push({
        tenantId,
        orgId,
        materialId: item.materialId,
        materialName: item.materialName,
        materialSpec: item.materialSpec,
        qualityStandard: item.qualityStandard,
        unit: item.unit,
        appearanceDescription: item.appearanceDescription,
        remark: item.remark,
        createBy: reqUser.id,
        updateBy: reqUser.id,
        updateAt: new Date(),
        incomingInspectionId: idMap[`${item.supplierId}@${item.contractId}`],
        siteEntryQuantity: item.siteEntryQuantity,
        actualQuantity: item.actualQuantity
      });
    }

    //2.保存数据
    await this.prisma.$transaction(async (tx) => {
      // 新增单据
      await tx.materialIncomingInspection.createMany({
        data: insertInspectionBills
      });

      // 删除旧单据明细
      await tx.materialIncomingInspectionDetail.updateMany({
        where: {
          incomingInspectionId: {
            in: updateInspectionBillIds
          },
          isDeleted: false,
          tenantId,
          orgId
        },
        data: {
          isDeleted: true,
          updateBy: userId,
          updateAt: new Date()
        }
      });

      // 新增单据明细
      await tx.materialIncomingInspectionDetail.createMany({
        data: insertInspectionDetails
      });
    });
  }

  // 获取供应商和合同列表
  async getSupplierAndContracts(
    reqUser: IReqUser,
    supplierAndContractsMap: Record<string, SupplierAndContractResponseDto[]>,
    purchaseType: PurchaseType
  ): Promise<SupplierAndContractResponseDto[]> {
    if (!supplierAndContractsMap[purchaseType]) {
      supplierAndContractsMap[purchaseType] =
        await this.detailService.getSupplierAndContractList(
          reqUser,
          purchaseType
        );
    }

    return supplierAndContractsMap[purchaseType];
  }

  // 获取材料信息
  async getMaterialMap({
    reqUser,
    contractId,
    purchaseType,
    contractMaterialMap,
    allMaterialMap,
    transferInMaterialMap
  }: {
    reqUser: IReqUser;
    contractId: string | null;
    purchaseType: PurchaseType;
    contractMaterialMap: Record<string, any>;
    allMaterialMap: Record<string, any>;
    transferInMaterialMap: Record<string, any>;
  }) {
    if (purchaseType === PurchaseType.TRANSFER_IN) {
      if (!transferInMaterialMap) {
        // TODO:
      }
      return transferInMaterialMap;
    }

    if (contractId) {
      if (!contractMaterialMap[contractId]) {
        const result = await this.detailService.getInspectionMaterialDetailList(
          reqUser,
          {
            purchaseType,
            contractId,
            materialSearchType: MaterialSearchType.CONTRACT,
            materialCategoryId: null,
            keyword: ''
          }
        );
        contractMaterialMap[contractId] = {};
        for (const item of result) {
          contractMaterialMap[contractId][item.code] = item;
        }
      }
      return contractMaterialMap[contractId];
    } else {
      if (!allMaterialMap) {
        const result = await this.detailService.getInspectionMaterialDetailList(
          reqUser,
          {
            purchaseType,
            contractId: null,
            materialSearchType: MaterialSearchType.MATERIAL_DICT,
            materialCategoryId: null,
            keyword: ''
          }
        );
        for (const item of result) {
          allMaterialMap[item.code] = item;
        }
      }
      return allMaterialMap;
    }
  }

  // 获取所有供应商名录
  async getAllSupplierMap(tenantId: string, orgId: string) {
    const suppliers = await this.repository.selectAllSuppliers(tenantId, orgId);

    const supplierNameMap = suppliers.reduce((acc: any, cur: any) => {
      acc[cur.fullName] = cur;
      return acc;
    }, {});

    return supplierNameMap;
  }

  // 获取当前组织的物资采购合同
  async getContractMap(tenantId: string, orgId: string) {
    const contracts = await this.repository.selectMaterialsPurchasingContracts(
      tenantId,
      orgId
    );

    const contractNameMap = contracts.reduce((acc: any, cur: any) => {
      acc[cur.name] = cur;
      return acc;
    }, {});

    return contractNameMap;
  }

  // 获取 供应商+合同=>采购类型 映射map
  async getExistsInspectionBillMap(
    tenantId: string,
    orgId: string
  ): Promise<Record<string, any>> {
    const bills = await this.prisma.materialIncomingInspection.findMany({
      where: {
        isDeleted: false,
        tenantId,
        orgId,
        contractId: {
          not: null
        }
      },
      select: {
        supplierId: true,
        supplierName: true,
        contractId: true,
        contractName: true,
        purchaseType: true,
        id: true
      }
    });

    const inspectionBillMap = bills.reduce((acc: any, cur: any) => {
      const key = `${cur.supplierId}@${cur.contractId}`;
      acc[key] = cur;
      return acc;
    }, {});

    return inspectionBillMap;
  }

  async getExistsInspectionCodeNoMap(tenantId: string, orgId: string) {
    const result = await this.repository.selectInspectionCodeNo(
      tenantId,
      orgId
    );

    const codeNoMap = result.reduce((acc: any, cur: any) => {
      const key = `${cur.year}-${cur.month}`;
      acc[key] = Number(cur.codeNo);
      return acc;
    }, {});

    return codeNoMap;
  }

  async getAllMaterialsMap(tenantId: string, orgId: string) {
    const materials = await this.repository.selectAllMaterials(tenantId, orgId);

    const materialCodeMap = materials.reduce((acc: any, cur: any) => {
      acc[cur.code] = cur;
      return acc;
    }, {});

    return materialCodeMap;
  }

  async getExistsMaterialUnitMap(tenantId: string, orgId: string) {
    const materialUnits = await this.repository.selectMaterialUnit(
      tenantId,
      orgId
    );
    const contractMaterialUnits =
      await this.repository.selectContractMaterialUnit(tenantId, orgId);

    const materialUnitMap = materialUnits.reduce((acc: any, cur: any) => {
      if (cur.optionalUnits) acc[cur.materialId] = cur.optionalUnits.split(',');
      return acc;
    }, {});

    const contractMaterialUnitMap = contractMaterialUnits.reduce(
      (acc: any, cur: any) => {
        if (cur.optionalUnits)
          acc[`${cur.materialContractId}@${cur.materialId}`] =
            cur.optionalUnits.split(',');
        return acc;
      },
      {}
    );

    return { materialUnitMap, contractMaterialUnitMap };
  }

  // 获取材料的可选单位列表
  async getUnitSelectionData(
    tenantId: string,
    orgId: string,
    data: QueryUnitSelectionDto
  ): Promise<UnitSelectionResponseDto[]> {
    const result = await this.detailRepository.selectInspectionDetailUnits(
      tenantId,
      orgId,
      data.contractId,
      data.materialIds
    );

    const res: UnitSelectionResponseDto[] = [];
    for (const item of result) {
      if (item.optionalUnits) {
        res.push({
          materialId: item.materialId,
          units: item.optionalUnits.split(',')
        });
      }
    }

    return res;
  }

  // 批量保存数据
  async bulkSaveData(reqUser: IReqUser, data: BulkSaveInspectionBillDataDto[]) {
    const { tenantId, orgId } = reqUser;
    const [inspectionBillMap, inspectionCodeNoMap] = await Promise.all([
      this.getExistsInspectionBillMap(tenantId, orgId),
      this.getExistsInspectionCodeNoMap(tenantId, orgId)
    ]);

    const saveData: ImportExcelDataResponseItemDto[] = [];
    for (const item of data) {
      const tempDay = dayjs(item.siteEntryDate);
      const year = tempDay.year();
      const month = tempDay.month() + 1;
      const day = tempDay.date();

      if (!inspectionCodeNoMap[`${year}-${month}`]) {
        inspectionCodeNoMap[`${year}-${month}`] = 0;
      }
      inspectionCodeNoMap[`${year}-${month}`]++;
      const codeNo = inspectionCodeNoMap[`${year}-${month}`];
      const code = `验-${year}${String(month).padStart(2, '0')}-${String(codeNo).padStart(3, '0')}`;

      saveData.push({
        rowNo: 0,
        code,
        purchaseType: item.purchaseType,
        siteEntryDate: item.siteEntryDate,
        year,
        month,
        day,
        supplierId: item.supplierId,
        supplierName: item.supplierName,
        contractId: item.contractId,
        contractName: item.contractName,
        materialId: item.materialId,
        materialCode: item.materialCode,
        materialSpec: item.materialSpec,
        materialName: item.materialName,
        qualityStandard: item.qualityStandard,
        unit: item.unit,
        siteEntryQuantity: item.siteEntryQuantity,
        actualQuantity: item.actualQuantity,
        appearanceDescription: item.appearanceDescription,
        remark: item.remark
      });
    }
    await this.processAndSaveData(reqUser, saveData, inspectionBillMap);

    return true;
  }
}
