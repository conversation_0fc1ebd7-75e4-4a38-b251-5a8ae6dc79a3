import { Injectable } from '@nestjs/common';
import { isEmpty } from 'lodash';

import { CommonRepositories } from '@/common/common-repositories';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { PlatformService } from '@/modules/platform/platform.service';
import {
  ContractTemplateClassifyType,
  MaterialType,
  PartyBType,
  PurchaseType
} from '@/prisma/generated';

import {
  CreateInspectionDetailItem,
  InspectionBillDetailResponseDto,
  InspectionMaterialDetailListResponseDto,
  MaterialCategoryListResponseDto,
  MaterialSearchType,
  QueryMaterialCategoryListDto,
  QueryMaterialDetailListDto,
  SupplierAndContractResponseDto,
  UpdateInspectionDetailDto
} from '../material-incoming-inspection.dto';
import { MaterialIncomingInspectionDetailRepository } from '../repositories/inspection-detail.repository';

@Injectable()
export class MaterialIncomingInspectionDetailService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly repository: MaterialIncomingInspectionDetailRepository
  ) {}

  async getSupplierAndContractList(
    reqUser: IReqUser,
    purchaseType: PurchaseType
  ): Promise<SupplierAndContractResponseDto[]> {
    let result: SupplierAndContractResponseDto[] = [];
    if (
      purchaseType === PurchaseType.SELF_PURCHASE ||
      purchaseType === PurchaseType.CENTRALIZED_PURCHASE ||
      purchaseType === PurchaseType.PARTY_A_DIRECTED
    ) {
      // 当采购类型为，自采，集采，甲指时，从合同编制模块中获取
      result = await this.getHasContractSuppliers(reqUser);
    } else if (purchaseType === PurchaseType.PARTY_A_SUPPLIED) {
      // 当采购类型为“甲供”，选择组织机构树
      const partAInfo = await this.repository.selectPartAInfo(reqUser);
      if (partAInfo) {
        result.push({
          id: partAInfo.id,
          name: partAInfo.value
        });
      }
    } else if (purchaseType === PurchaseType.TRANSFER_IN) {
      // @TODO: 采购类型为“调入”，从调拨单选取
    }

    return result;
  }

  // 获取有合同的供应商信息（自采，集采，甲指）
  private async getHasContractSuppliers(
    reqUser: IReqUser
  ): Promise<SupplierAndContractResponseDto[]> {
    // 获取所有合同信息
    const contracts = await this.repository.selectContracts(
      reqUser.tenantId,
      reqUser.orgId,
      ContractTemplateClassifyType.MATERIALS_PURCHASING
    );

    const supplierIds: string[] = [];
    const companyIds: string[] = [];
    for (const item of contracts) {
      if (item.partyBType === PartyBType.SUPPLIER) {
        supplierIds.push(item.partyB);
      } else if (item.partyBType === PartyBType.COMPANY) {
        companyIds.push(item.partyB);
      }
    }

    const [suppliers, companies] = await Promise.all([
      this.prisma.supplierDirectory.findMany({
        select: {
          id: true,
          fullName: true
        },
        where: {
          tenantId: reqUser.tenantId,
          isDeleted: false,
          OR: [{ id: { in: supplierIds } }, { fullName: '零星材料供应商' }]
        }
      }),
      this.prisma.businessBaseInfo.findMany({
        select: {
          id: true,
          companyName: true
        },
        where: {
          tenantId: reqUser.tenantId,
          isDeleted: false,
          id: { in: companyIds }
        }
      })
    ]);

    const resultMap: Record<string, SupplierAndContractResponseDto> = {};
    for (const contract of contracts) {
      const { partyB, partyBType } = contract;
      let name = '';
      if (partyBType === PartyBType.SUPPLIER) {
        name = suppliers.find((item) => item.id === partyB)?.fullName || '';
      } else if (partyBType === PartyBType.COMPANY) {
        name = companies.find((item) => item.id === partyB)?.companyName || '';
      }

      if (!resultMap[partyB]) {
        resultMap[partyB] = {
          id: partyB,
          name,
          contracts: []
        };
      }
      if (resultMap[partyB]?.contracts) {
        resultMap[partyB].contracts.push({
          id: contract.id,
          name: contract.name
        });
      }
    }

    // 添加 “零星材料供应商”到最后面
    const result = Object.values(resultMap);
    const sporadicSupplier = suppliers.find(
      (item) => item.fullName === '零星材料供应商'
    );
    if (sporadicSupplier) {
      result.push({
        id: sporadicSupplier.id,
        name: sporadicSupplier.fullName,
        contracts: []
      });
    }
    return result;
  }

  // 获取进场验收单明细
  async getInspectionDetailList(
    reqUser: IReqUser,
    inspectionBillId: string
  ): Promise<InspectionBillDetailResponseDto[]> {
    const result: any[] =
      await this.prisma.materialIncomingInspectionDetail.findMany({
        select: {
          id: true,
          materialId: true,
          materialName: true,
          materialSpec: true,
          qualityStandard: true,
          unit: true,
          siteEntryQuantity: true,
          actualQuantity: true,
          appearanceDescription: true,
          remark: true
        },
        where: {
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false,
          incomingInspectionId: inspectionBillId
        },
        orderBy: { orderNo: 'asc' }
      });

    const inspectionBill =
      await this.prisma.materialIncomingInspection.findFirst({
        select: { id: true, contractId: true },
        where: {
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          id: inspectionBillId
        }
      });
    const materialIds = result.map((item) => item.materialId);
    if (inspectionBill && !isEmpty(materialIds)) {
      const contractId = inspectionBill.contractId;
      const optionalUnits: { materialId: string; optionalUnits: string }[] =
        await this.repository.selectInspectionDetailUnits(
          reqUser.tenantId,
          reqUser.orgId,
          contractId,
          materialIds
        );
      const optionalUnitMap = optionalUnits.reduce(
        (acc: { [key: string]: string }, item) => {
          acc[item.materialId] = item.optionalUnits;
          return acc;
        },
        {}
      );

      for (const item of result as InspectionBillDetailResponseDto[]) {
        item.optionalUnits = optionalUnitMap[item.materialId] || '';
      }
    }

    return result;
  }

  // 获取可选择的材料字典分类
  async getInspectionMaterialCategoryList(
    reqUser: IReqUser,
    query: QueryMaterialCategoryListDto
  ): Promise<MaterialCategoryListResponseDto[]> {
    const { materialSearchType, contractId } = query;

    let result: MaterialCategoryListResponseDto[] = [];
    if (materialSearchType === MaterialSearchType.CONTRACT && contractId) {
      const contractInfo = await this.repository.selectLastContractInfo(
        reqUser,
        contractId
      );

      if (contractInfo) {
        const contractId = contractInfo.id;
        result = await this.repository.selectContractMaterialCategories(
          reqUser,
          contractId,
          query.keyword
        );
      }
    } else if (materialSearchType === MaterialSearchType.MATERIAL_DICT) {
      let materialType: MaterialType | null = null;
      if (query.purchaseType == PurchaseType.PARTY_A_SUPPLIED) {
        materialType = MaterialType.CONSUME_MATERIAL;
      }
      result = await this.repository.selectAllMaterialCategories(
        reqUser,
        materialType,
        query.keyword
      );
    }

    return result;
  }

  async getInspectionMaterialDetailList(
    reqUser: IReqUser,
    query: QueryMaterialDetailListDto
  ): Promise<InspectionMaterialDetailListResponseDto[]> {
    const { materialSearchType, contractId } = query;
    let result: InspectionMaterialDetailListResponseDto[] = [];
    if (materialSearchType === MaterialSearchType.CONTRACT && contractId) {
      const contractInfo = await this.repository.selectLastContractInfo(
        reqUser,
        contractId
      );
      if (contractInfo) {
        const contractId = contractInfo.id;
        result = await this.repository.selectContractMaterials(
          reqUser,
          query.materialCategoryId,
          contractId,
          query.keyword
        );
      }
    } else if (materialSearchType === MaterialSearchType.MATERIAL_DICT) {
      let materialType: MaterialType | null = null;
      if (query.purchaseType == PurchaseType.PARTY_A_SUPPLIED) {
        materialType = MaterialType.CONSUME_MATERIAL;
      }
      result = await this.repository.selectAllMaterials(
        reqUser,
        query.materialCategoryId,
        materialType,
        query.keyword
      );
    }

    return result;
  }

  // 新增进场验收单材料明细数据行
  async addInspectionDetails(
    reqUser: IReqUser,
    inspectionBillId: string,
    data: CreateInspectionDetailItem[]
  ) {
    await this.prisma.materialIncomingInspectionDetail.createMany({
      data: data.map((item) => ({
        ...item,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        incomingInspectionId: inspectionBillId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }))
    });

    return true;
  }

  // 删除进场验收单明细
  async deleteInspectionDetail(reqUser: IReqUser, id: string) {
    await this.prisma.materialIncomingInspectionDetail.update({
      data: {
        isDeleted: true,
        updateBy: reqUser.id,
        updateAt: new Date()
      },
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false,
        id
      }
    });
    return true;
  }

  // 编辑进场验收单明细
  async editInspectionDetail(
    reqUser: IReqUser,
    data: UpdateInspectionDetailDto
  ) {
    await this.prisma.materialIncomingInspectionDetail.update({
      data: { ...data, updateAt: new Date(), updateBy: reqUser.id },
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false,
        id: data.id
      }
    });
    return true;
  }

  // 进场验收单明细上移下移
  async moveInspectionDetail(reqUser: IReqUser, fromId: string, toId: string) {
    await CommonRepositories.changeDataOrderNo(this.prisma, {
      tenantId: reqUser.tenantId,
      orgId: reqUser.orgId,
      fromId,
      toId,
      tableName: 'material_incoming_inspection_detail'
    });
    return true;
  }
}
