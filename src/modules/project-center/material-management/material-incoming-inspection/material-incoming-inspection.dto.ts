import { ApiProperty, ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
  ValidateNested
} from 'class-validator';

import { FileOperateDto } from '@/common/dtos/common.dto';
import {
  AuditStatus,
  MaterialReceiptStatus,
  MaterialType,
  PurchaseType,
  SubmitStatus
} from '@/prisma/generated/enums';

// #region 材料验收单据
class MaterialIncomingInspectionBaseDto {
  @ApiProperty({ description: '租户ID' })
  @IsString({ message: 'tenantId 必须是字符串' })
  tenantId: string;

  @ApiProperty({ description: '组织ID' })
  @IsString({ message: 'orgId 必须是字符串' })
  orgId: string;

  @ApiProperty({ description: '数据ID，系统自动生成，可选' })
  @IsString({ message: 'id 必须是字符串' })
  id: string;

  @ApiPropertyOptional({ description: '收料状态', enum: MaterialReceiptStatus })
  @IsEnum(MaterialReceiptStatus, {
    message: 'materialReceiptStatus 必须是 MaterialReceiptStatus 枚举值'
  })
  @IsOptional()
  materialReceiptStatus?: MaterialReceiptStatus;

  @ApiProperty({ description: '单据编码' })
  @IsString({ message: 'code 必须是字符串' })
  code: string;

  @ApiProperty({ description: '采购类型', enum: PurchaseType })
  @IsEnum(PurchaseType, { message: 'purchaseType 必须是 PurchaseType 枚举值' })
  purchaseType: PurchaseType;

  @ApiProperty({ description: '供应商ID' })
  @IsString({ message: 'supplierId 必须是字符串' })
  supplierId?: string;

  @ApiPropertyOptional({ description: '供应商名称' })
  @IsString({ message: 'supplierId 必须是字符串' })
  @IsOptional()
  supplierName?: string;

  @ApiPropertyOptional({ description: '合同ID' })
  @IsString({ message: 'contractId 必须是字符串' })
  @IsOptional()
  contractId?: string;

  @ApiPropertyOptional({ description: '合同名称' })
  @IsString({ message: 'contractId 必须是字符串' })
  @IsOptional()
  contractName?: string;

  @ApiPropertyOptional({ description: '提交状态', enum: SubmitStatus })
  @IsEnum(SubmitStatus, { message: 'submitStatus 必须是 SubmitStatus 枚举值' })
  @IsOptional()
  submitStatus?: SubmitStatus;

  @ApiPropertyOptional({ description: '审批状态', enum: AuditStatus })
  @IsEnum(AuditStatus, { message: 'auditStatus 必须是 AuditStatus 枚举值' })
  @IsOptional()
  auditStatus?: AuditStatus;

  @ApiProperty({ description: '年' })
  @IsInt({ message: 'year必须是整数' })
  year: number;

  @ApiProperty({ description: '月' })
  @IsInt({ message: 'year必须是整数' })
  month: number;

  @ApiProperty({ description: '日' })
  @IsInt({ message: 'year必须是整数' })
  day: number;

  @ApiPropertyOptional({ description: '是否删除' })
  @IsBoolean({ message: 'isDeleted 必须是布尔值' })
  @IsOptional()
  isDeleted?: boolean;

  @ApiPropertyOptional({ description: '创建人' })
  @IsString({ message: 'createBy 必须是字符串' })
  @IsOptional()
  createBy?: string;

  @ApiPropertyOptional({ description: '更新人' })
  @IsString({ message: 'updateBy 必须是字符串' })
  @IsOptional()
  updateBy?: string;

  @ApiPropertyOptional({ description: '创建时间' })
  @IsDate({ message: 'createAt 必须是有效日期' })
  @Type(() => Date)
  @IsOptional()
  createAt?: Date;

  @ApiPropertyOptional({ description: '更新时间' })
  @IsDate({ message: 'updateAt 必须是有效日期' })
  @Type(() => Date)
  @IsOptional()
  updateAt?: Date;

  @ApiPropertyOptional({ description: '编制人' })
  creator?: string;

  @ApiPropertyOptional({ description: '材料类别' })
  materialCategories?: string;
}

export class InspectionTimeListResponseDto {
  @ApiProperty({ description: 'id' })
  id: string;

  @ApiPropertyOptional({ description: 'parentId' })
  parentId?: string | null;

  @ApiPropertyOptional({ description: '年' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  year?: number;

  @ApiPropertyOptional({ description: '月' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  month?: number;

  @ApiPropertyOptional({ description: '日' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  day?: number;

  @ApiProperty({ description: '单据数量' })
  count: number;
}

export class InspectionBillListResponseDto extends PickType(
  MaterialIncomingInspectionBaseDto,
  [
    'materialReceiptStatus',
    'id',
    'purchaseType',
    'materialCategories',
    'creator',
    'year',
    'month',
    'day',
    'submitStatus',
    'auditStatus'
  ] as const
) {
  orgId: string;
  supplierId?: string | null;
  supplierName?: string | null;
  contractId?: string | null;
  contractName?: string | null;
  orgName?: string;
}

export class QueryInspectionBillListDto {
  @ApiPropertyOptional({ description: '年' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  year?: number;

  @ApiPropertyOptional({ description: '月' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  month?: number;

  @ApiPropertyOptional({ description: '日' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  day?: number;

  @ApiProperty({ description: '是否仅查看自己的数据,默认为false' })
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  onlyViewSelf: boolean;
}

export class UpdateInspectionBillListDto extends PickType(
  MaterialIncomingInspectionBaseDto,
  [
    'id',
    'purchaseType',
    'supplierId',
    'supplierName',
    'contractId',
    'contractName',
    'year',
    'month',
    'day',
    'auditStatus',
    'submitStatus'
  ] as const
) {}

class ContractItemDto {
  @ApiProperty({ description: '合同id' })
  id: string;

  @ApiProperty({ description: '合同名称' })
  name: string;
}

export class SupplierAndContractResponseDto {
  @ApiProperty({ description: '供应商id' })
  @IsString()
  id: string;

  @ApiProperty({ description: '父级id' })
  parentId?: string;

  @ApiProperty({ description: '供应商名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '合同列表' })
  contracts?: ContractItemDto[];
}

// #endregion 材料验收单据

// #region 材料验收单明细
class MaterialIncomingInspectionDetailDto {
  @ApiProperty({ description: '组织ID' })
  @IsString()
  orgId: string;

  @ApiProperty({ description: 'ID' })
  @IsString()
  id: string;

  @ApiProperty({ description: '验收单ID' })
  @IsString()
  incomingInspectionId: string;

  @ApiProperty({ description: '材料ID' })
  @IsString()
  materialId: string;

  @ApiPropertyOptional({ description: '材料名称' })
  @IsOptional()
  @IsString()
  materialName?: string;

  @ApiPropertyOptional({ description: '材料规格' })
  @IsOptional()
  @IsString()
  materialSpec?: string;

  @ApiPropertyOptional({ description: '质量标准' })
  @IsOptional()
  @IsString()
  qualityStandard?: string;

  @ApiProperty({ description: '计量单位' })
  @IsString()
  unit: string;

  @ApiProperty({ description: '进场数量' })
  @IsNumber({ maxDecimalPlaces: 8 }, { message: '最多保留8位小数' })
  @Min(0)
  @Max(99999999.99999999, { message: '不能超过99999999.99999999' })
  siteEntryQuantity?: number;

  @ApiProperty({ description: '实收数量' })
  @IsNumber({ maxDecimalPlaces: 8 }, { message: '最多保留8位小数' })
  @Min(0)
  @Max(99999999.99999999, { message: '不能超过99999999.99999999' })
  actualQuantity?: number;

  @ApiPropertyOptional({ description: '外观质量描述' })
  @IsOptional()
  @IsString()
  appearanceDescription?: string;

  @ApiProperty({ description: '排序号' })
  @IsNumber()
  orderNo: number;

  @ApiPropertyOptional({ description: '备注' })
  @IsOptional()
  @IsString()
  remark?: string;
}

export class InspectionBillDetailResponseDto extends PickType(
  MaterialIncomingInspectionDetailDto,
  [
    'id',
    'materialId',
    'materialName',
    'materialSpec',
    'qualityStandard',
    'unit',
    'siteEntryQuantity',
    'actualQuantity',
    'appearanceDescription',
    'remark'
  ] as const
) {
  @ApiProperty({ description: '可选单位下拉列表' })
  optionalUnits?: string;
}

export enum MaterialSearchType {
  CONTRACT = 'CONTRACT',
  MATERIAL_DICT = 'MATERIAL_DICT'
}

export class QueryMaterialCategoryListDto {
  @ApiPropertyOptional({ description: '搜索类型' })
  @IsEnum(MaterialSearchType, {
    message: 'materialSearchType 必须是 CONTRACT, 枚举值'
  })
  materialSearchType: MaterialSearchType;

  @ApiProperty({ description: '采购类型' })
  @IsEnum(PurchaseType, {
    message: 'purchaseType 必须是 PurchaseType 枚举值'
  })
  purchaseType: PurchaseType;

  @ApiProperty({ description: '搜索关键字' })
  @IsString()
  keyword: string;

  @ApiPropertyOptional({ description: '合同Id' })
  @IsOptional()
  @IsString()
  contractId?: string;
}

export class MaterialCategoryListResponseDto {
  @ApiProperty({ description: 'ID' })
  id: string;

  @ApiProperty({ description: '父ID' })
  parentId: string;

  @ApiProperty({ description: '类别名称' })
  name: string;

  @ApiProperty({ description: '编码' })
  code: string;

  @ApiProperty({ description: '材料类型' })
  @IsEnum(MaterialType, { message: '材料类型必须是MaterialType 枚举值' })
  type: MaterialType;

  @ApiProperty({ description: '层级' })
  level: string;

  @ApiProperty({ description: '备注' })
  remark: string;
}

export class QueryMaterialDetailListDto {
  @ApiProperty({ description: '采购类型' })
  @IsEnum(PurchaseType, {
    message: 'purchaseType 必须是 PurchaseType 枚举值'
  })
  purchaseType: PurchaseType;

  @ApiPropertyOptional({ description: '搜索类型' })
  @IsEnum(MaterialSearchType, {
    message: 'materialSearchType 必须是 MaterialSearchType 枚举值'
  })
  materialSearchType: MaterialSearchType;

  @ApiProperty({ description: '材料类别Id' })
  @IsString()
  materialCategoryId: string | null;

  @ApiProperty({ description: '搜索关键字' })
  @IsString()
  keyword: string;

  @ApiPropertyOptional({ description: '合同Id' })
  @IsOptional()
  @IsString()
  contractId?: string;
}

export class InspectionMaterialDetailListResponseDto {
  @ApiProperty({ description: 'ID' })
  id: string;

  @ApiProperty({ description: '编码' })
  code: string;

  @ApiProperty({ description: '合同材料名称' })
  name: string;

  @ApiProperty({ description: '规格型号' })
  spec: string;

  @ApiProperty({ description: '计量单位' })
  unit: string;

  @ApiProperty({ description: '核算类型' })
  type: string;

  @ApiProperty({ description: '备注' })
  remark: string;
}

export class CreateInspectionDetailItem extends PickType(
  MaterialIncomingInspectionDetailDto,
  ['materialId', 'materialName', 'materialSpec', 'unit'] as const
) {}

export class CreateInspectionDetailDto {
  @ApiProperty({ type: [CreateInspectionDetailItem] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateInspectionDetailItem)
  data: CreateInspectionDetailItem[];
}

export class UpdateInspectionDetailDto extends PickType(
  MaterialIncomingInspectionDetailDto,
  [
    'id',
    'qualityStandard',
    'unit',
    'siteEntryQuantity',
    'actualQuantity',
    'appearanceDescription',
    'remark',
    'orderNo'
  ] as const
) {}

// #endregion 材料验收单明细

// #region 验收单附件

export class InspectionAttachmentResponseDto extends PickType(FileOperateDto, [
  'fileName',
  'fileKey',
  'fileSize',
  'fileExt',
  'fileContentType'
]) {
  @ApiProperty({ description: '数据ID' })
  @IsString()
  id: string;

  @ApiProperty({ description: '验收单ID' })
  @IsString()
  incomingInspectionId: string;
}

export class CreateInspectionAttachmentDto extends PickType(FileOperateDto, [
  'fileName',
  'fileKey',
  'fileSize',
  'fileExt',
  'fileContentType'
] as const) {
  @ApiProperty({ description: '验收单ID' })
  @IsString()
  incomingInspectionId: string;
}

// #endregion 验收单附件

// #region 数据补录

export class ImportExcelDataBodyDto {
  @ApiProperty({ description: 'fileKey' })
  @IsNotEmpty({ message: 'fileKey不能为空' })
  @IsString({ message: 'fileKey必须是字符串' })
  fileKey: string;

  @ApiProperty({ description: '导入文件type' })
  @IsNotEmpty({ message: 'type不能为空' })
  @IsString({ message: 'type必须是字符串' })
  type: string;
}

export class ImportExcelDataResponseItemDto {
  rowNo: number;
  code?: string;
  purchaseType: PurchaseType;

  siteEntryDate: string;
  year?: number;
  month?: number;
  day?: number;

  supplierId?: string;
  supplierName: string;
  supplierNameMsg?: string;

  contractId?: string;
  contractName: string;
  contractNameMsg?: string;

  materialId?: string;
  materialCode: string;
  materialCodeMsg?: string;
  materialSpec: string;
  materialName: string;

  qualityStandard: string;
  unit: string;
  unitMsg?: string;

  siteEntryQuantity: string;
  siteEntryQuantityMsg?: string;

  actualQuantity: string;
  actualQuantityMsg?: string;

  appearanceDescription: string;
  remark: string;
}

export class ImportExcelDataResponseDto {
  @ApiProperty({ description: '导入失败个数' })
  errorCount: number;

  @ApiProperty({ description: '导入成功个数' })
  successCount: number;

  @ApiProperty({ description: '导入总个数' })
  totalCount: number;

  @ApiProperty({ description: '导入失败数据' })
  errorData: ImportExcelDataResponseItemDto[];
}

export class QueryUnitSelectionDto {
  @ApiProperty({ description: '材料ID' })
  @IsNotEmpty()
  @IsArray()
  materialIds: string[];

  @ApiPropertyOptional({ description: '合同ID' })
  @IsOptional()
  contractId: string | null;
}

export class UnitSelectionResponseDto {
  @ApiProperty({ description: '材料ID' })
  @IsString()
  materialId: string;

  @ApiProperty({ description: '单位' })
  @IsString()
  units: string[];
}

export class BulkSaveInspectionBillDataDto {
  @ApiProperty({ description: '采购类型' })
  @IsEnum(PurchaseType, { message: '采购类型必须是枚举值' })
  purchaseType: PurchaseType;

  @ApiProperty({ description: '进场时间' })
  siteEntryDate: string;

  year?: number;
  month?: number;
  day?: number;

  @ApiProperty({ description: '供应商ID' })
  supplierId?: string;
  @ApiProperty({ description: '供应商名称' })
  supplierName: string;

  @ApiProperty({ description: '合同ID' })
  contractId?: string;
  @ApiProperty({ description: '合同名称' })
  contractName: string;

  @ApiProperty({ description: '材料ID' })
  materialId?: string;
  @ApiProperty({ description: '材料编码' })
  materialCode: string;
  @ApiProperty({ description: '材料规格' })
  materialSpec: string;
  @ApiProperty({ description: '材料名称' })
  materialName: string;

  @ApiProperty({ description: '质量标准' })
  qualityStandard: string;
  @ApiProperty({ description: '计量单位' })
  unit: string;

  @ApiProperty({ description: '进场数量' })
  @IsNumber({ maxDecimalPlaces: 8 }, { message: '最多保留8位小数' })
  siteEntryQuantity: string;

  @ApiProperty({ description: '实收数量' })
  @IsNumber({ maxDecimalPlaces: 8 }, { message: '最多保留8位小数' })
  actualQuantity: string;

  @ApiProperty({ description: '外观描述' })
  appearanceDescription: string;
  @ApiProperty({ description: '备注' })
  remark: string;
}

// #endregion
