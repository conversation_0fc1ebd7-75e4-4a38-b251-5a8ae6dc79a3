import { Body, Controller, Get, Patch, Post, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  MaterialReceivingReturnDetailQueryDto,
  MaterialReceivingReturnDetailResponseDto,
  MaterialSettlementDetailQueryDto,
  MaterialSettlementDetailResponseDto,
  QueryReceivingReturnDto,
  ReceivingReturnResponseDto,
  SettlementSupplierAndContractResponseDto,
  SumReceivingReturnBodyDto,
  SumReceivingReturnResponseDto
} from './material-settlement-detail.dto';
import { MaterialSettlementDetailService } from './material-settlement-detail.service';

@ApiTags('物资结算/明细')
@Controller('material-settlement-detail')
export class MaterialSettlementDetailController {
  constructor(private readonly service: MaterialSettlementDetailService) {}

  @ApiOperation({ summary: '供应商及合同列表' })
  @ApiResponse({
    status: 200,
    description: '获取供应商及合同列表成功',
    type: SettlementSupplierAndContractResponseDto,
    isArray: true
  })
  @Get('/supplier-and-contract/list')
  async getSupplierAndContractList(@ReqUser() reqUser: IReqUser) {
    return await this.service.getSupplierAndContractList(reqUser);
  }

  @ApiOperation({ summary: '根据供应商及合同获取所有收料单或退货单列表' })
  @ApiResponse({
    status: 200,
    description: '获取收料单或退货单列表成功',
    type: ReceivingReturnResponseDto,
    isArray: true
  })
  @Get('/supplier-and-contract/receiving-returns')
  async getReceivingReturnList(
    @ReqUser() reqUser: IReqUser,
    @Query() query: QueryReceivingReturnDto
  ) {
    return await this.service.getReceivingReturnList(reqUser, query);
  }

  @ApiOperation({ summary: '根据收料单或退货单获取材料汇总明细列表' })
  @ApiResponse({
    status: 200,
    description: '获取材料汇总明细列表成功',
    type: SumReceivingReturnResponseDto,
    isArray: true
  })
  @Post('/supplier-and-contract/receiving-returns/summary')
  async sumReceivingReturnList(
    @ReqUser() reqUser: IReqUser,
    @Body() body: SumReceivingReturnBodyDto
  ) {
    return await this.service.sumReceivingReturnList(reqUser, body);
  }

  @ApiOperation({ summary: '根据收料单或退货单更新明细' })
  @ApiResponse({
    status: 200,
    description: '更新明细成功'
  })
  @Patch('/supplier-and-contract/receiving-returns/summary')
  async updateSettlementDetails(
    @ReqUser() reqUser: IReqUser,
    @Body() body: SumReceivingReturnBodyDto
  ) {
    return await this.service.updateSettlementDetails(reqUser, body);
  }

  @ApiOperation({ summary: '根据删除的收料单或退货单更新明细' })
  @ApiResponse({
    status: 200,
    description: '更新明细成功'
  })
  @Patch('/supplier-and-contract/receiving-returns/delete-summary')
  async removeSettlementDetails(
    @ReqUser() reqUser: IReqUser,
    @Body() body: SumReceivingReturnBodyDto
  ) {
    return await this.service.removeSettlementDetails(reqUser, body);
  }

  @ApiOperation({ summary: '根据结算单获取明细列表' })
  @ApiResponse({
    status: 200,
    description: '结算单获取明细成功',
    type: MaterialSettlementDetailResponseDto,
    isArray: true
  })
  @Get()
  async getSettlementDetails(
    @ReqUser() reqUser: IReqUser,
    @Query() query: MaterialSettlementDetailQueryDto
  ) {
    return await this.service.getSettlementDetails(reqUser, query);
  }

  @ApiOperation({ summary: '根据删除的收料单或退货单更新明细' })
  @ApiResponse({
    status: 200,
    description: '更新明细成功'
  })
  @Patch('/update')
  async updateSettlementDetail(
    @ReqUser() reqUser: IReqUser,
    @Body() body: MaterialSettlementDetailResponseDto
  ) {
    return await this.service.updateSettlementDetail(reqUser, body);
  }

  @ApiOperation({
    summary: '结算单材料上下移',
    description: '结算单材料上下移'
  })
  @ApiResponse({
    status: 200,
    description: '结算单材料上下移成功'
  })
  @Post('/move')
  async move(
    @ReqUser() reqUser: IReqUser,
    @Query('fromId') fromId: string,
    @Query('toId') toId: string
  ) {
    await this.service.move(reqUser, fromId, toId);
    return true;
  }

  @ApiOperation({
    summary: '根据结算单对应材料明细获取收料单及退货单明细列表'
  })
  @ApiResponse({
    status: 200,
    description: '结算单获取收料单及退货单明细列表成功',
    type: MaterialReceivingReturnDetailResponseDto,
    isArray: true
  })
  @Get('/material/receiving-returns')
  async getMaterialReceivingReturnDetails(
    @ReqUser() reqUser: IReqUser,
    @Query() query: MaterialReceivingReturnDetailQueryDto
  ) {
    return await this.service.getMaterialReceivingReturnDetails(reqUser, query);
  }
}
