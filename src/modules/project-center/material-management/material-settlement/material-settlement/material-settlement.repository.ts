import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import {
  AuditStatus,
  Prisma,
  SettlementBillType,
  SubmitStatus
} from '@/prisma/generated';

import { MaterialSettlementPeriodResDto } from './material-settlement.dto';

@Injectable()
export class MaterialSettlementRepository {
  constructor(private readonly prisma: PrismaService) {}

  // 获取物资结算单统计周期
  async selectSettlementPeriods(reqUser: IReqUser) {
    const result = await this.prisma.$queryRaw<any[]>`
        select distinct
        year, month
      from material_settlement
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and org_id = ${reqUser.orgId}
      order by year desc, month desc
    `;

    return result;
  }

  async getSettlementPeriodAmounts(
    reqUser: IReqUser,
    query: MaterialSettlementPeriodResDto
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
        select 
        sum(tax_included_amount) as cumulation_tax_included_amount,
        SUM(tax_included_amount) FILTER (WHERE ${query.year && query.month ? Prisma.sql` (year * 100 + month) = ${query.year * 100 + query.month}` : Prisma.sql` 1=1`}) AS tax_included_amount
      from material_settlement
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and org_id = ${reqUser.orgId}
         ${query.year && query.month ? Prisma.sql`and (year * 100 + month) <= ${query.year * 100 + query.month}` : Prisma.empty}
    `;

    return result;
  }

  async selectSettlementPeriodContracts(
    reqUser: IReqUser,
    query: MaterialSettlementPeriodResDto
  ) {
    const { year, month } = query;
    const result = await this.prisma.$queryRaw<any[]>`
      select 
        count(distinct contract_id) as count
      from material_settlement
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and org_id = ${reqUser.orgId}
        and submit_status = ${SubmitStatus.SUBMITTED}::"SubmitStatus"
        and year = ${year}
        and month = ${month}
    `;

    return result;
  }

  async selectUnSettlementPeriodContracts(
    reqUser: IReqUser,
    { year, month, day }: { year: number; month: number; day: number }
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
    with settled_contract as (
      select 
        contract_id
      from material_settlement
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and org_id = ${reqUser.orgId}
        and submit_status = ${SubmitStatus.SUBMITTED}::"SubmitStatus"
        and year = ${year}
        and month = ${month}
    ),
    settled_receiving as (
      select
        distinct a.bill_id
      from material_settlement_bill_ref_detail a
      join material_settlement b
        on a.tenant_id = b.tenant_id
        and a.org_id = b.org_id
        and a.settlement_id = b.id
      where a.is_deleted = false
        and a.tenant_id = ${reqUser.tenantId}
        and a.org_id = ${reqUser.orgId}
        and a.settlement_bill_type = ${SettlementBillType.RECEIVING}::"SettlementBillType"
        and b.is_deleted = false
        and b.submit_status = ${SubmitStatus.SUBMITTED}::"SubmitStatus"
    )
      select 
        count(distinct contract_id) as count
      from material_receiving
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and org_id = ${reqUser.orgId}
        and audit_status = ${AuditStatus.APPROVED}::"AuditStatus"
        and (year * 10000 + month * 100 + day) <= ${year * 10000 + month * 100 + day}
        and contract_id not in (select contract_id from settled_contract)
        and id not in (select bill_id from settled_receiving)
    `;

    return result;
  }

  async selectSettlementPeriodMaterials(
    reqUser: IReqUser,
    query: MaterialSettlementPeriodResDto
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
    with material_category_settlement as (
      select
        case
          when position('|' in mdc.full_name) > 0
          then split_part(mdc.full_name, '|', 2)
          else split_part(mdc.full_name, '|', 1)
        end as material_category_name,
        case
          when position('|' in mdc.full_id) > 0
          then split_part(mdc.full_id, '|', 2)
          else split_part(mdc.full_id, '|', 1)
        end as material_category_id,
        b.year,
        b.month,
        a.settlement_amount * (1 + b.tax_rate)  as tax_included_amount
      from material_settlement_detail a
      join material_settlement b
        on a.tenant_id = b.tenant_id
        and a.org_id = b.org_id
        and a.settlement_id = b.id
      join material_dictionary_detail mdd
        on mdd.id = a.material_id
        and mdd.is_deleted = false
        and mdd.tenant_id = ${reqUser.tenantId}
      join material_dictionary_category mdc
        on mdc.id = mdd.material_dictionary_category_id
        and mdc.is_deleted = false
        and mdc.tenant_id = ${reqUser.tenantId}
        and mdc.org_id = mdd.org_id
      where a.is_deleted = false
        and a.tenant_id = ${reqUser.tenantId}
        and a.org_id = ${reqUser.orgId}
        and b.is_deleted = false
        ${query.year && query.month ? Prisma.sql`and (b.year * 100 + b.month) <= ${query.year * 100 + query.month}` : Prisma.empty}
    )
    select 
      material_category_name,
      material_category_id,
      sum(tax_included_amount) as cumulation_tax_included_amount,
      sum(tax_included_amount) filter (where ${query.year && query.month ? Prisma.sql`(year * 100 + month) = ${query.year * 100 + query.month}` : Prisma.sql`1=1`}) as tax_included_amount
    from material_category_settlement
    group by material_category_name, material_category_id
    order by material_category_id
    `;

    return result;
  }

  async selectMaterialSettlementList(
    reqUser: IReqUser,
    query: MaterialSettlementPeriodResDto
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
    with material_category_settlement as (
      select
        case
          when position('|' in mdc.full_id) > 0
          then split_part(mdc.full_id, '|', 2)
          else split_part(mdc.full_id, '|', 1)
        end as material_category_id,
        a.settlement_id
      from material_settlement_detail a
      join material_settlement b
        on a.tenant_id = b.tenant_id
        and a.org_id = b.org_id
        and a.settlement_id = b.id
      join material_dictionary_detail mdd
        on mdd.id = a.material_id
        and mdd.is_deleted = false
        and mdd.tenant_id = ${reqUser.tenantId}
      join material_dictionary_category mdc
        on mdc.id = mdd.material_dictionary_category_id
        and mdc.is_deleted = false
        and mdc.tenant_id = ${reqUser.tenantId}
        and mdc.org_id = mdd.org_id
      where a.is_deleted = false
        and a.tenant_id = ${reqUser.tenantId}
        and a.org_id = ${reqUser.orgId}
        and b.is_deleted = false
        ${query.year && query.month ? Prisma.sql`and (b.year * 100 + b.month) <= ${query.year * 100 + query.month}` : Prisma.empty}
    )
    select
      COALESCE(b.full_name, c.company_name) as new_supplier_name,
      a.id,
      a.year,
      a.month,
      a.code,
      a.settlement_type,
      a.supplier_id,
      a.supplier_name,
      a.contract_id,
      a.contract_name,
      a.contract_amount,
      a.price_type,
      a.tax_rate,
      a.tax_excluded_amount,
      a.tax_included_amount,
      a.tax_amount,
      a.before_cumulation_tax_excluded_amount,
      a.before_cumulation_tax_included_amount,
      a.before_cumulation_tax_amount,
      TO_CHAR(a.settlement_date, 'YYYY-MM-DD') AS settlement_date,
      a.submit_status,
      a.audit_status,
      u.nickname as creator,
      o.name as project_name
    from material_settlement a
    left join supplier_directory b
      on a.tenant_id = b.tenant_id
      and a.org_id = b.org_id
      and a.supplier_id = b.id
      and b.is_deleted = false
    left join business_base_info c
      on c.tenant_id = a.tenant_id
      and c.id = a.supplier_id
      and c.is_deleted = false
    left join platform_meta."user" u
      on u.id = a.create_by
    left join platform_meta."org" o
      on o.id = a.org_id
      and o.tenant_id = a.tenant_id
    where a.is_deleted = false
      and a.tenant_id = ${reqUser.tenantId}
      and a.org_id = ${reqUser.orgId}
      ${query.supplierId ? Prisma.sql`and a.supplier_id = ${query.supplierId}` : Prisma.empty}
      ${query.contractId ? Prisma.sql`and a.contract_id = ${query.contractId}` : Prisma.empty}
      ${query.id ? Prisma.sql`and a.id = ${query.id}` : Prisma.empty}
      ${query.year && query.month ? Prisma.sql`and (a.year * 100 + a.month) ${query.isCumulation ? Prisma.sql`<=` : Prisma.sql`=`} ${query.year * 100 + query.month}` : Prisma.empty}
      ${query.materialCategoryId ? Prisma.sql`and a.id in (select distinct settlement_id from material_category_settlement where material_category_id = ${query.materialCategoryId}) ` : Prisma.empty}
      order by a.id desc
    `;

    return result;
  }

  async selectMaterialContract(
    reqUser: IReqUser,
    contractId: string,
    settlementDate: Date
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
    with contract as (
      select 
        sign_date,
        amount as contract_amount,
        price_type,
        tax_rate,
        id
      from material_contract
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and org_id = ${reqUser.orgId}
        and id = ${contractId}
        and sign_date <= ${settlementDate}
      union all
      select 
        sign_date,
        amount as contract_amount,
        price_type,
        tax_rate,
        id
      from material_contract
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and org_id = ${reqUser.orgId}
        and parent_id = ${contractId}
        and sign_date <= ${settlementDate}
    )
    select 
      *
    from contract
    order by sign_date desc
    `;

    return result;
  }
}
