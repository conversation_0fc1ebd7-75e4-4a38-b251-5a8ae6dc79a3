import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  BaseMaterialSettlementDto,
  MaterialSettlementAmountResDto,
  MaterialSettlementAuditBodyDto,
  MaterialSettlementContractResDto,
  MaterialSettlementCreateDto,
  MaterialSettlementMaterialResDto,
  MaterialSettlementPeriodResDto,
  MaterialSettlementSubmitBodyDto,
  MaterialSettlementUpdateDto
} from './material-settlement.dto';
import { MaterialSettlementService } from './material-settlement.service';

@ApiTags('物资结算/列表')
@Controller('material-settlement')
export class MaterialSettlementController {
  constructor(private readonly service: MaterialSettlementService) {}

  @ApiOperation({ summary: '物资结算单的统计周期列表' })
  @ApiResponse({
    status: 200,
    description: '获取统计周期列表成功',
    type: MaterialSettlementPeriodResDto,
    isArray: true
  })
  @Get('/periods')
  async getSettlementPeriods(@ReqUser() reqUser: IReqUser) {
    return await this.service.getSettlementPeriods(reqUser);
  }

  @ApiOperation({ summary: '物资结算单的结算金额' })
  @ApiResponse({
    status: 200,
    description: '获取结算金额成功',
    type: MaterialSettlementAmountResDto
  })
  @Get('/period-amounts')
  async getSettlementPeriodAmounts(
    @ReqUser() reqUser: IReqUser,
    @Query() query: MaterialSettlementPeriodResDto
  ) {
    return await this.service.getSettlementPeriodAmounts(reqUser, query);
  }

  @ApiOperation({ summary: '物资结算单的合同结算数量' })
  @ApiResponse({
    status: 200,
    description: '获取合同结算数量成功',
    type: MaterialSettlementContractResDto
  })
  @Get('/period-contracts')
  async getSettlementPeriodContracts(
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser
  ) {
    return await this.service.getSettlementPeriodContracts(req, reqUser);
  }

  @ApiOperation({ summary: '物资结算单的材料结算金额列表' })
  @ApiResponse({
    status: 200,
    description: '获取材料结算金额列表成功',
    type: MaterialSettlementMaterialResDto,
    isArray: true
  })
  @Get('/period-material-categories')
  async getSettlementPeriodMaterials(
    @ReqUser() reqUser: IReqUser,
    @Query() query: MaterialSettlementPeriodResDto
  ) {
    return await this.service.getSettlementPeriodMaterials(reqUser, query);
  }

  @ApiOperation({ summary: '物资结算单列表' })
  @ApiResponse({
    status: 200,
    description: '获取物资结算单列表成功',
    type: BaseMaterialSettlementDto,
    isArray: true
  })
  @Get()
  async getList(
    @ReqUser() reqUser: IReqUser,
    @Query() query: MaterialSettlementPeriodResDto
  ) {
    return await this.service.getMaterialSettlementList(reqUser, query);
  }

  @ApiOperation({ summary: '新增物资结算单' })
  @ApiResponse({
    status: 200,
    description: '新增物资结算单成功'
  })
  @Post()
  async add(
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser,
    @Body() body: MaterialSettlementCreateDto
  ) {
    return await this.service.addMaterialSettlement(req, reqUser, body);
  }

  @ApiOperation({ summary: '编辑物资结算单' })
  @ApiResponse({
    status: 200,
    description: '编辑物资结算单成功'
  })
  @Patch('/:id')
  async update(
    @Param('id') id: string,
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser,
    @Body() data: MaterialSettlementUpdateDto
  ) {
    return await this.service.updateMaterialSettlement(id, req, reqUser, data);
  }

  @ApiOperation({ summary: '提交状态变更' })
  @ApiResponse({
    status: 200,
    description: '提交状态变更成功'
  })
  @Patch('/submit/:id')
  async updateSubmitStatus(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() body: MaterialSettlementSubmitBodyDto
  ) {
    return await this.service.updateSubmitStatus(id, reqUser, body);
  }

  @ApiOperation({ summary: '删除结算单' })
  @ApiResponse({
    status: 200,
    description: '删除结算单成功'
  })
  @Delete('/:id')
  async delete(@ReqUser() reqUser: IReqUser, @Param('id') id: string) {
    return await this.service.deleteMaterialSettlement(id, reqUser);
  }

  @ApiOperation({ summary: '审核状态变更' })
  @ApiResponse({
    status: 200,
    description: '审核状态变更成功'
  })
  @Patch('/audit/:id')
  async updateAuditStatus(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() body: MaterialSettlementAuditBodyDto
  ) {
    const { auditStatus } = body;
    return await this.service.updateAuditStatus(id, reqUser, auditStatus);
  }
}
