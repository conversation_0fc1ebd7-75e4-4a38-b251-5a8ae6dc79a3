import { ApiProperty, PickType } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class BaseMaterialSettlementAttachmentDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '收料单id' })
  @IsNotEmpty({ message: '收料单id不能为空' })
  @IsString({ message: '收料单id必须是字符串' })
  settlementId: string;

  @ApiProperty({ description: '文件名称' })
  @IsNotEmpty({ message: '文件名称不能为空' })
  @IsString({ message: '文件名称必须是字符串' })
  fileName: string;

  @ApiProperty({ description: '文件扩展名' })
  @IsNotEmpty({ message: '文件扩展名不能为空' })
  @IsString({ message: '文件扩展名必须是字符串' })
  fileExt: string;

  @ApiProperty({ description: '文件key' })
  @IsNotEmpty({ message: '文件key不能为空' })
  @IsString({ message: '文件key必须是字符串' })
  fileKey: string;

  @ApiProperty({ description: '文件大小' })
  @IsNotEmpty({ message: '文件大小不能为空' })
  @IsNumber({}, { message: '文件大小必须是数字' })
  fileSize: number;

  @ApiProperty({ description: '文件类型' })
  @IsNotEmpty({ message: '文件类型不能为空' })
  @IsString({ message: '文件类型必须是字符串' })
  fileContentType: string;
}

export class MaterialSettlementAttachmentResDto extends PickType(
  BaseMaterialSettlementAttachmentDto,
  [
    'id',
    'settlementId',
    'fileName',
    'fileSize',
    'fileExt',
    'fileContentType',
    'fileKey'
  ] as const
) {}

export class MaterialSettlementAttachmentCreateDto extends PickType(
  BaseMaterialSettlementAttachmentDto,
  [
    'settlementId',
    'fileName',
    'fileSize',
    'fileExt',
    'fileContentType',
    'fileKey'
  ] as const
) {}
