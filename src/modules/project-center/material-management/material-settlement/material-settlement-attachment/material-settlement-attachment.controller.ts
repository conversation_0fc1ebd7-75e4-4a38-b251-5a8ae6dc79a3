import { Body, Controller, Delete, Get, Param, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  MaterialSettlementAttachmentCreateDto,
  MaterialSettlementAttachmentResDto
} from './material-settlement-attachment.dto';
import { MaterialSettlementAttachmentService } from './material-settlement-attachment.service';

@ApiTags('物资结算单/附件')
@Controller('material-settlement-attachment')
export class MaterialSettlementAttachmentController {
  constructor(private readonly service: MaterialSettlementAttachmentService) {}

  @ApiOperation({
    summary: '获取附件列表',
    description: '获取附件列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取附件列表成功',
    type: MaterialSettlementAttachmentResDto,
    isArray: true
  })
  @Get('/:settlementId')
  async getList(
    @Param('settlementId') settlementId: string,
    @ReqUser() reqUser: IReqUser
  ) {
    return await this.service.getList(settlementId, reqUser);
  }

  @ApiOperation({
    summary: '新增附件',
    description: '新增附件'
  })
  @ApiResponse({
    status: 200,
    description: '新增附件成功'
  })
  @Post()
  async add(
    @ReqUser() reqUser: IReqUser,
    @Body() data: MaterialSettlementAttachmentCreateDto
  ) {
    return await this.service.add(data, reqUser);
  }

  @ApiOperation({
    summary: '删除附件',
    description: '删除附件'
  })
  @ApiResponse({
    status: 200,
    description: '删除附件成功'
  })
  @Delete('/:id')
  async delete(@ReqUser() reqUser: IReqUser, @Param('id') id: string) {
    return await this.service.delete(id, reqUser);
  }
}
