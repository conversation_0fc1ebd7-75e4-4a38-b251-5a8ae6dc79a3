import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import { MaterialSettlementAttachmentCreateDto } from './material-settlement-attachment.dto';

@Injectable()
export class MaterialSettlementAttachmentService {
  constructor(private readonly prisma: PrismaService) {}

  async getList(settlementId: string, reqUser: IReqUser) {
    return await this.prisma.materialSettlementAttachment.findMany({
      where: {
        settlementId,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      }
    });
  }

  async add(data: MaterialSettlementAttachmentCreateDto, reqUser: IReqUser) {
    return await this.prisma.materialSettlementAttachment.create({
      data: {
        ...data,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }
    });
  }

  async delete(id: string, reqUser: IReqUser) {
    return await this.prisma.materialSettlementAttachment.update({
      where: {
        id,
        isDeleted: false
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }
}
