import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { TimeListResponseDto } from '@/common/dtos/common.dto';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  MaterialRealizedPaymentRecordCreateDto,
  MaterialRealizedPaymentRecordResDto,
  MaterialRealizedPaymentRecordUpdateDto,
  QueryMaterialRealizedPaymentRecordDto
} from './material-realized-payment-record.dto';
import { MaterialRealizedPaymentRecordService } from './material-realized-payment-record.service';

@ApiTags('/实付记录')
@Controller('material-realized-payment-record')
export class MaterialRealizedPaymentRecordController {
  constructor(private readonly service: MaterialRealizedPaymentRecordService) {}

  @ApiOperation({
    summary: '获取时间筛选列表',
    description: '获取时间筛选列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取时间筛选列表',
    type: TimeListResponseDto,
    isArray: true
  })
  @Get('time-list')
  async getTimeList(@ReqUser() reqUser: IReqUser) {
    return await this.service.getTimeList(reqUser);
  }

  @ApiOperation({ summary: '查询列表' })
  @ApiResponse({
    status: 200,
    description: '查询列表成功',
    type: MaterialRealizedPaymentRecordResDto,
    isArray: true
  })
  @Get('')
  async getList(
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser,
    @Query() query: QueryMaterialRealizedPaymentRecordDto
  ): Promise<MaterialRealizedPaymentRecordResDto[]> {
    return await this.service.getList(reqUser, query, req);
  }

  @ApiOperation({ summary: '新增' })
  @ApiResponse({
    status: 200,
    description: '新增成功'
  })
  @Post('')
  async add(
    @ReqUser() reqUser: IReqUser,
    @Body() body: MaterialRealizedPaymentRecordCreateDto
  ): Promise<boolean> {
    return await this.service.add(reqUser, body);
  }

  @ApiOperation({ summary: '编辑' })
  @ApiResponse({
    status: 200,
    description: '编辑成功'
  })
  @Patch('/:id')
  async update(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() body: MaterialRealizedPaymentRecordUpdateDto
  ): Promise<boolean> {
    return await this.service.update(id, reqUser, body);
  }
}
