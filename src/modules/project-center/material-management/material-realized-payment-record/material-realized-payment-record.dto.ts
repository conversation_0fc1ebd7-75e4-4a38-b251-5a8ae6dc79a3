import { ApiProperty, PickType } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString
} from 'class-validator';

import {
  ContractTemplateClassifyType,
  PartyBType,
  SubmitStatus
} from '@/prisma/generated';

export class BaseMaterialRealizedPaymentRecordDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '合同id' })
  @IsNotEmpty({ message: '合同id不能为空' })
  @IsString({ message: '合同id必须是字符串' })
  contractId: string;

  @ApiProperty({ description: '本期付款金额' })
  @IsOptional({ message: '本期付款金额能为空' })
  @IsNumber({}, { message: '本期付款金额必须是数字' })
  amount?: number;

  @ApiProperty({ description: '年 付款日期年' })
  @IsOptional({ message: '年-付款日期年能为空' })
  @IsNumber({}, { message: '年-付款日期年必须是数字' })
  year?: number;

  @ApiProperty({ description: '月 付款日期月' })
  @IsOptional({ message: '月-付款日期月能为空' })
  @IsNumber({}, { message: '月-付款日期月必须是数字' })
  month?: number;

  @ApiProperty({ description: '日 付款日期日' })
  @IsOptional({ message: '日-付款日期日能为空' })
  @IsNumber({}, { message: '日-付款日期日必须是数字' })
  day?: number;

  @ApiProperty({ description: '提交状态' })
  @IsOptional({ message: '提交状态可以为空' })
  @IsIn(Object.values(SubmitStatus), {
    message: '提交状态必须是有效枚举值'
  })
  @IsString({ message: '提交状态必须是字符串' })
  submitStatus?: SubmitStatus;

  @ApiProperty({ description: '备注' })
  @IsOptional({ message: '备注能为空' })
  @IsString({ message: '备注必须字符串' })
  remark?: string;

  @ApiProperty({ description: '合同名称' })
  contractName: string;

  @ApiProperty({ description: '合同编号' })
  contractCode: string;

  @ApiProperty({ description: '供应商名称' })
  supplierName?: string;

  @ApiProperty({ description: '供应商Id' })
  supplierId: string;

  @ApiProperty({ description: '合同类型' })
  classify: ContractTemplateClassifyType;

  @ApiProperty({ description: '编制日期' })
  createAt: string;

  @ApiProperty({ description: '编制人' })
  creator: string;

  @ApiProperty({ description: '合同乙方类型' })
  supplierType: PartyBType;
}

export class QueryMaterialRealizedPaymentRecordDto extends PickType(
  BaseMaterialRealizedPaymentRecordDto,
  ['year', 'month', 'day']
) {
  @ApiProperty({ description: '是否仅查看自己的数据,默认为false' })
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  onlyViewSelf: boolean;
}

export class MaterialRealizedPaymentRecordResDto extends PickType(
  BaseMaterialRealizedPaymentRecordDto,
  [
    'id',
    'contractId',
    'contractName',
    'contractCode',
    'supplierName',
    'classify',
    'amount',
    'year',
    'month',
    'day',
    'remark',
    'submitStatus',
    'supplierId'
  ]
) {}

export class MaterialRealizedPaymentRecordCreateDto {
  @ApiProperty({ description: '合同id数组' })
  @IsNotEmpty({ message: '合同id数组不能为空' })
  @IsArray({ message: '合同id数组类型错误' })
  contractIdList: string[];
}

export class MaterialRealizedPaymentRecordUpdateDto extends PickType(
  BaseMaterialRealizedPaymentRecordDto,
  ['day', 'month', 'year', 'remark', 'submitStatus', 'amount']
) {}
