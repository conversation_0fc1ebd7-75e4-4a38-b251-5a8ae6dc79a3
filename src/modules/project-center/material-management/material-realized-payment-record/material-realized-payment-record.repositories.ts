import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { Prisma } from '@/prisma/generated';

import {
  MaterialRealizedPaymentRecordResDto,
  QueryMaterialRealizedPaymentRecordDto
} from './material-realized-payment-record.dto';

@Injectable()
export class MaterialRealizedPaymentRecordRepository {
  constructor(private readonly prisma: PrismaService) {}

  async getList(
    reqUser: IReqUser,
    query: QueryMaterialRealizedPaymentRecordDto
  ): Promise<MaterialRealizedPaymentRecordResDto[]> {
    const { orgId, tenantId, id: userId } = reqUser;
    const { onlyViewSelf = false } = query;
    return await this.prisma.$queryRaw<MaterialRealizedPaymentRecordResDto[]>`
      select 
        mrpr.id
        ,mrpr.contract_id
        ,mrpr.amount
        ,mrpr.year
        ,mrpr.month
        ,mrpr.day
        ,mrpr.submit_status
        ,mrpr.create_at
        ,mc.name as contract_name
        ,mc.code as contract_code
        ,mc.party_b as supplier_id
        ,mc.party_b_type as supplier_type
        ,ct.classify
        ,u.nickname as creator
      from material_realized_payment_record mrpr
      left join material_contract mc
        on mc.id = mrpr.contract_id
        and mc.is_deleted = false
        and mc.tenant_id = mrpr.tenant_id
        and mc.org_id = mrpr.org_id
        and mc.parent_id is null
      left join contract_template ct
        on ct.id = mc.contract_template_id
        and ct.is_deleted = false
        and ct.tenant_id = mc.tenant_id
        and ct.org_id = mc.org_id
      left join platform_meta."user" u
        on u.id = mrpr.create_By
      where mrpr.is_deleted = false
        and mrpr.tenant_id = ${tenantId}
        and mrpr.org_id = ${orgId}
        ${onlyViewSelf ? Prisma.sql`and mrpr.create_By = ${userId}` : Prisma.empty}
    `;
  }
}
