import { Injectable, NotFoundException } from '@nestjs/common';
import * as dayjs from 'dayjs';

import { TimeListResponseDto } from '@/common/dtos/common.dto';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import { MaterialContractService } from '../material-contract/material-contract/material-contract.service';
import {
  MaterialRealizedPaymentRecordCreateDto,
  MaterialRealizedPaymentRecordResDto,
  MaterialRealizedPaymentRecordUpdateDto,
  QueryMaterialRealizedPaymentRecordDto
} from './material-realized-payment-record.dto';
import { MaterialRealizedPaymentRecordRepository } from './material-realized-payment-record.repositories';

@Injectable()
export class MaterialRealizedPaymentRecordService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly repository: MaterialRealizedPaymentRecordRepository,
    private readonly contractService: MaterialContractService
  ) {}

  async getTimeList(reqUser: IReqUser): Promise<TimeListResponseDto[]> {
    // 实现获取时间列表的逻辑
    const dates = await this.prisma.materialRealizedPaymentRecord.findMany({
      distinct: ['year', 'month', 'day'],
      select: {
        year: true,
        month: true,
        day: true
      },
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      },
      orderBy: [{ year: 'desc' }, { month: 'desc' }, { day: 'desc' }]
    });

    const resultMap: Record<string, TimeListResponseDto> = {};
    for (const time of dates) {
      // 添加父级，年_月
      if (!resultMap[`${time.year}_${time.month}`]) {
        resultMap[`${time.year}_${time.month}`] = {
          id: `${time.year}_${time.month}`,
          parentId: null,
          year: time.year,
          month: time.month,
          count: 0
        };
      }
      resultMap[`${time.year}_${time.month}`].count += 1;
    }

    return Object.values(resultMap);
  }

  async getList(
    reqUser: IReqUser,
    query: QueryMaterialRealizedPaymentRecordDto,
    req: Request
  ): Promise<MaterialRealizedPaymentRecordResDto[]> {
    const data = await this.repository.getList(reqUser, query);
    // 查询乙方
    const partyB = await this.contractService.getPartBCompanyList(
      req,
      reqUser,
      false
    );
    const res = data.map((item) => {
      return {
        ...item,
        supplierName: partyB.find((item2) => item2.id === item.supplierId)?.name
      };
    });
    return res;
  }

  async add(
    reqUser: IReqUser,
    data: MaterialRealizedPaymentRecordCreateDto
  ): Promise<boolean> {
    const { contractIdList } = data;
    const { tenantId, orgId, id: userId } = reqUser;
    // 获取付款时间
    const dateNow = dayjs();
    const year = dateNow.year();
    const month = dateNow.month() + 1;
    const day = dateNow.date();
    const addData = contractIdList.map((item) => {
      return {
        tenantId,
        orgId,
        contractId: item,
        year,
        month,
        day,
        createBy: userId
      };
    });
    await this.prisma.materialRealizedPaymentRecord.createMany({
      data: addData
    });
    return true;
  }

  async update(
    id: string,
    reqUser: IReqUser,
    data: MaterialRealizedPaymentRecordUpdateDto
  ): Promise<boolean> {
    const { id: userId, tenantId, orgId } = reqUser;
    await this.checkData(id, reqUser);
    await this.prisma.materialRealizedPaymentRecord.update({
      where: {
        id,
        tenantId,
        orgId,
        isDeleted: false
      },
      data: {
        ...data,
        updateBy: userId
      }
    });
    return true;
  }

  async del(id: string, reqUser: IReqUser): Promise<boolean> {
    const { id: userId, tenantId, orgId } = reqUser;
    await this.checkData(id, reqUser);
    await this.prisma.materialRealizedPaymentRecord.update({
      where: {
        id,
        tenantId,
        orgId,
        isDeleted: false
      },
      data: {
        isDeleted: true,
        updateBy: userId
      }
    });
    return true;
  }

  async checkData(id: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    const resData = await this.prisma.materialRealizedPaymentRecord.findUnique({
      where: {
        id,
        tenantId,
        orgId,
        isDeleted: false
      }
    });
    if (!resData) {
      throw new NotFoundException('数据不存在');
    }
  }
}
