import { Modu<PERSON> } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';

import { MaterialContractModule } from '../material-contract/material-contract.module';
import { MaterialRealizedPaymentRecordController } from './material-realized-payment-record.controller';
import { MaterialRealizedPaymentRecordRepository } from './material-realized-payment-record.repositories';
import { MaterialRealizedPaymentRecordService } from './material-realized-payment-record.service';

@Module({
  imports: [PrismaModule, MaterialContractModule],
  controllers: [MaterialRealizedPaymentRecordController],
  providers: [
    MaterialRealizedPaymentRecordService,
    MaterialRealizedPaymentRecordRepository
  ]
})
export class MaterialRealizedPaymentRecordModule {}
