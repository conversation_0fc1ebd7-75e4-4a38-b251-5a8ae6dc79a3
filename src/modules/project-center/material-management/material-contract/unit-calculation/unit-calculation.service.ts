import {
  BadRequestException,
  Injectable,
  NotFoundException
} from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { ContractTemplateClassifyType } from '@/prisma/generated';

import {
  UnitCalculationCreateDto,
  UnitCalculationQueryDto,
  UnitCalculationUpdateDto
} from './unit-calculation.dto';

@Injectable()
export class UnitCalculationService {
  constructor(private readonly prisma: PrismaService) {}

  async getList(query: UnitCalculationQueryDto, reqUser: IReqUser) {
    const { materialDetailId, materialContractId } = query;
    const { tenantId, orgId } = reqUser;
    const materialDictionaryDetail =
      await this.prisma.materialDictionaryDetail.findUnique({
        where: {
          id: materialDetailId,
          tenantId: tenantId,
          isDeleted: false
        }
      });
    // is_show为false代表为字典本身数据，不显示，可下拉选到
    // is_operation为false代表为字典的单位，不可操作，可下拉选到
    // is_original为false代表为父级的计量单位，不可操作，可下拉选到，为true代表为子级计量单位，可操作，可下拉选到
    const res = await this.prisma.$queryRaw<any[]>`
      select 
      '1' as id,
      metering_unit as unit,
      1 as factor,
      null as remark,
      false as is_show,
      false as is_original,
      false as is_operation,
      null as material_detail_id
      from material_dictionary_detail 
      where id = ${materialDetailId} 
      and tenant_id = ${tenantId} 
      and is_deleted = false
      and metering_unit is not null
      and metering_unit != ''

      UNION all

      select id, unit, factor, remark, true as is_show, false as is_original, false as is_operation, null as material_detail_id from material_dictionary_unit_calculation mduc
      where material_dictionary_detail_id = ${materialDetailId}
      and is_deleted = false

      UNION all

      select id, unit, factor, remark, true as is_show, is_original, true as is_operation, material_detail_id from material_contract_unit_calculation
      where material_detail_id = ${materialDetailId}
      and material_contract_id = ${materialContractId}
      and is_deleted = false
      and tenant_id = ${tenantId}
      and org_id = ${orgId}
      order by is_operation asc, is_original desc
    `;
    return {
      dictionaryUnit: materialDictionaryDetail?.meteringUnit ?? '',
      list: res
    };
  }

  /**
   * 初始化单位换算（补充协议）
   * @param txPrisma
   * @param materialContractId
   * @param data
   * @param reqUser
   */
  async init(
    txPrisma: PrismaService,
    materialContractId: string,
    reqUser: IReqUser,
    parentId: string
  ) {
    const { orgId, tenantId, id: userId } = reqUser;
    let data;
    if (parentId) {
      // 查询下是否有补充协议
      const child = await this.prisma.materialContract.findFirst({
        where: {
          parentId,
          isDeleted: false,
          tenantId,
          orgId
        },
        orderBy: {
          createAt: 'desc'
        }
      });
      if (child) {
        parentId = child.id;
      }
      // 有父级查询父级
      data = await this.prisma.materialContractUnitCalculation.findMany({
        select: {
          materialDetailId: true,
          factor: true,
          remark: true,
          unit: true
        },
        where: {
          materialContractId: parentId,
          isDeleted: false,
          tenantId,
          orgId
        },
        orderBy: {
          createAt: 'asc'
        }
      });
      await txPrisma.materialContractUnitCalculation.createMany({
        data: data.map((item) => ({
          ...item,
          isOriginal: true,
          materialContractId,
          tenantId,
          orgId,
          createBy: userId,
          updateBy: userId
        }))
      });
    }
  }

  async add(reqUser: IReqUser, data: UnitCalculationCreateDto) {
    const { tenantId, orgId, id: userId } = reqUser;
    // 新增前校验
    await this.checkAdd(reqUser, data);
    return await this.prisma.materialContractUnitCalculation.create({
      data: {
        ...data,
        tenantId,
        orgId,
        createBy: userId,
        updateBy: userId
      }
    });
  }

  async checkAdd(reqUser: IReqUser, data: UnitCalculationCreateDto) {
    const res = await this.getList(
      {
        materialDetailId: data.materialDetailId,
        materialContractId: data.materialContractId
      },
      reqUser
    );
    if (res.list.find((item) => item.unit === data.unit)) {
      throw new BadRequestException('单位换算已存在');
    }
  }

  async update(id: string, reqUser: IReqUser, data: UnitCalculationUpdateDto) {
    const { tenantId, orgId, id: userId } = reqUser;
    return await this.prisma.materialContractUnitCalculation.update({
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      },
      data: {
        ...data,
        updateBy: userId
      }
    });
  }

  async getOne(id: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    const data = await this.prisma.materialContractUnitCalculation.findUnique({
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      }
    });
    if (!data) {
      throw new BadRequestException('该计量单位不存在');
    }
    return data;
  }

  async delete(id: string, reqUser: IReqUser) {
    // 删除前校验
    await this.checkDelete(id, reqUser);
    return await this.prisma.materialContractUnitCalculation.update({
      where: {
        id,
        isDeleted: false
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }

  async checkDelete(id: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    // 查询该单位换算
    const unitCalculation = await this.getOne(id, reqUser);
    // 查询该单位换算所属的材料详情
    // 查询合同范本类型
    const contract = await this.getContractTemplate(
      unitCalculation.materialContractId,
      reqUser
    );
    const templateClassifyType = contract?.contractTemplate.classify;
    if (
      templateClassifyType === ContractTemplateClassifyType.MATERIALS_PURCHASING
    ) {
      // 物资采购合同
      // 查询计量单位
      const details =
        await this.prisma.contractConsumeMaterialDetails.findFirst({
          where: {
            materialDictionaryDetailId: unitCalculation.materialDetailId,
            materialContractId: unitCalculation.materialContractId,
            isDeleted: false,
            tenantId,
            orgId,
            unit: unitCalculation.unit
          }
        });
      if (details) {
        throw new BadRequestException('该计量单位已经被使用，无法删除');
      }
    }
    if (
      templateClassifyType ===
      ContractTemplateClassifyType.MATERIALS_COMMERCIAL_CONCRETE
    ) {
      // 商品混凝土
      // 查询明细
      const details = await this.prisma.contractConcreteDetails.findFirst({
        where: {
          materialDictionaryDetailId: unitCalculation.materialDetailId,
          materialContractId: unitCalculation.materialContractId,
          isDeleted: false,
          tenantId,
          orgId,
          unit: unitCalculation.unit
        }
      });
      if (details) {
        throw new BadRequestException('该计量单位已经被使用，无法删除');
      }
    }
    if (
      templateClassifyType ===
      ContractTemplateClassifyType.MATERIALS_LEASING_TURNOVER
    ) {
      // 租赁
      // 查询明细
      const details =
        await this.prisma.contractTurnoverMaterialDetails.findFirst({
          where: {
            materialDictionaryDetailId: unitCalculation.materialDetailId,
            materialContractId: unitCalculation.materialContractId,
            isDeleted: false,
            tenantId,
            orgId,
            unit: unitCalculation.unit
          }
        });
      if (details) {
        throw new BadRequestException('该计量单位已经被使用，无法删除');
      }
    }
  }

  // 查询合同范本的类型
  async getContractTemplate(contractId: string, reqUser: IReqUser) {
    const contractTemplate = await this.prisma.materialContract.findUnique({
      where: {
        id: contractId,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId
      },
      select: {
        contractTemplateId: true,
        contractTemplate: {
          select: {
            classify: true
          }
        }
      }
    });
    return contractTemplate;
  }
}
