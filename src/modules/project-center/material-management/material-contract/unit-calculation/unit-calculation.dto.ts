import { ApiProperty, PickType } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsPositive, IsString } from 'class-validator';

export class BaseUnitCalculationDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '单位' })
  @IsNotEmpty({ message: '单位不能为空' })
  @IsString({ message: '单位必须是字符串' })
  unit: string;

  @ApiProperty({ description: '换算系数 ' })
  @IsNotEmpty({ message: '换算系数不能为空' })
  @IsPositive({ message: '换算系数必须大于0' })
  factor: number;

  @ApiProperty({ description: '备注' })
  @IsOptional({ message: '备注可以为空' })
  @IsString({ message: '备注必须是字符串' })
  remark?: string;

  @ApiProperty({ description: '是否可操作' })
  isOperation: boolean;

  @ApiProperty({ description: '是否属于原合同' })
  isOriginal: boolean;

  @ApiProperty({ description: '是否在材料下拉框可选' })
  isShow: boolean;

  @ApiProperty({ description: '已选明细id ' })
  @IsNotEmpty({ message: '已选明细id不能为空' })
  @IsString({ message: '已选明细id必须是字符串' })
  materialDetailId: string;

  @ApiProperty({ description: '合同id ' })
  @IsNotEmpty({ message: '已选明细id不能为空' })
  @IsString({ message: '已选明细id必须是字符串' })
  materialContractId: string;
}

export class UnitCalculationResDto {
  @ApiProperty({ description: '材料字典的单位' })
  dictionaryUnit: string;

  @ApiProperty({ description: '数据数组' })
  list: UnitCalculationRes[];
}

export class UnitCalculationRes extends PickType(BaseUnitCalculationDto, [
  'id',
  'factor',
  'isOperation',
  'isOriginal',
  'isShow',
  'remark',
  'unit',
  'materialDetailId'
] as const) {}

export class UnitCalculationCreateDto extends PickType(BaseUnitCalculationDto, [
  'materialContractId',
  'materialDetailId',
  'factor',
  'remark',
  'unit'
] as const) {}

export class UnitCalculationUpdateDto extends PickType(BaseUnitCalculationDto, [
  'factor',
  'remark',
  'unit'
] as const) {}

export class UnitCalculationQueryDto {
  @ApiProperty({
    description: '已选明细id',
    required: true
  })
  @IsNotEmpty({ message: '已选明细id不能为空' })
  @IsString({ message: '已选明细id必须为字符串' })
  materialDetailId: string;

  @ApiProperty({
    description: '合同id'
  })
  @IsNotEmpty({ message: '合同id不能为空' })
  @IsString({ message: '合同id必须为字符串' })
  materialContractId: string;
}
