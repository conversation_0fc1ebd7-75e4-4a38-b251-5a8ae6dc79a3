import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  UnitCalculationCreateDto,
  UnitCalculationQueryDto,
  UnitCalculationResDto,
  UnitCalculationUpdateDto
} from './unit-calculation.dto';
import { UnitCalculationService } from './unit-calculation.service';

@ApiTags('合同编制/单位换算')
@Controller('unit-calculation')
export class UnitCalculationController {
  constructor(private readonly service: UnitCalculationService) {}

  @ApiOperation({
    summary: '查询某明细的单位换算信息',
    description: '查询某明细的单位换算信息'
  })
  @ApiResponse({
    status: 200,
    description: '某明细的单位换算信息成功',
    type: UnitCalculationResDto,
    isArray: true
  })
  @Get('')
  async getList(
    @ReqUser() reqUser: IReqUser,
    @Query() query: UnitCalculationQueryDto
  ) {
    return await this.service.getList(query, reqUser);
  }

  @ApiOperation({
    summary: '新增某明细的单位换算信息',
    description: '新增某明细的单位换算信息'
  })
  @ApiResponse({
    status: 200,
    description: '新增某明细的单位换算信息成功'
  })
  @Post('')
  async add(
    @ReqUser() reqUser: IReqUser,
    @Body() data: UnitCalculationCreateDto
  ) {
    return await this.service.add(reqUser, data);
  }

  @ApiOperation({
    summary: '编辑某明细的单位换算信息',
    description: '编辑某明细的单位换算信息'
  })
  @ApiResponse({
    status: 200,
    description: '编辑某明细的单位换算信息成功',
    type: UnitCalculationResDto,
    isArray: true
  })
  @Patch('/:id')
  async update(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() data: UnitCalculationUpdateDto
  ) {
    return await this.service.update(id, reqUser, data);
  }

  @ApiOperation({
    summary: '删除某明细的单位换算信息',
    description: '删除某明细的单位换算信息'
  })
  @ApiResponse({
    status: 200,
    description: '删除某明细的单位换算信息成功',
    type: UnitCalculationResDto,
    isArray: true
  })
  @Delete('/:id')
  async delete(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    return await this.service.delete(id, reqUser);
  }
}
