import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  ChooseMaterialCategoryResDto,
  ChooseMaterialDetailResDto,
  ContractConsumeMaterialDetailsResDto,
  ContractDetailsListCreateDto,
  ContractDetailUpdateDto,
  EditMaterialDetailMoveDto,
  QueryChooseMateriaCategoryDto,
  QueryChooseMaterialDetailDto,
  QueryMaterialDetailDto
} from './contract-material-detail.dto';
import { ContractMaterialDetailService } from './contract-material-detail.service';

@ApiTags('合同编制/合同明细')
@Controller('contract-material-detail')
export class ContractMaterialDetailController {
  constructor(private readonly service: ContractMaterialDetailService) {}

  @ApiOperation({
    summary: '前置查询（查询可以选择的材料分类）',
    description: '前置查询（查询可以选择的材料分类）'
  })
  @ApiResponse({
    status: 200,
    description: '查询可以选择的材料分类成功',
    type: ChooseMaterialCategoryResDto,
    isArray: true
  })
  @Get('/material-category/:materialContractId')
  async getMaterialCategory(
    @ReqUser() reqUser: IReqUser,
    @Param('materialContractId') materialContractId: string,
    @Query() query: QueryChooseMateriaCategoryDto
  ) {
    return await this.service.getMaterialCategory(
      reqUser,
      materialContractId,
      query
    );
  }

  @ApiOperation({
    summary: '前置查询（查询可以选择的材料明细）',
    description: '前置查询（查询可以选择的材料明细）'
  })
  @ApiResponse({
    status: 200,
    description: '查询可以选择的材料明细成功',
    type: ChooseMaterialDetailResDto,
    isArray: true
  })
  @Get('/material-detail')
  async getMaterialDetail(
    @ReqUser() reqUser: IReqUser,
    @Query() query: QueryMaterialDetailDto
  ) {
    return await this.service.getMaterialDetail(reqUser, query);
  }

  @ApiOperation({
    summary: '新增合同材料',
    description: '新增合同材料'
  })
  @ApiResponse({
    status: 200,
    description: '新增合同材料成功'
  })
  @Post('')
  async add(
    @ReqUser() reqUser: IReqUser,
    @Body() data: ContractDetailsListCreateDto
  ) {
    return await this.service.add(reqUser, data);
  }

  @ApiOperation({
    summary: '获取合同下选择的合同材料',
    description: '获取合同下选择的合同材料'
  })
  @ApiResponse({
    status: 200,
    description: '获取合同下选择的合同材料成功',
    type: ContractConsumeMaterialDetailsResDto
  })
  @Get('/:contractId')
  async getList(
    @ReqUser() reqUser: IReqUser,
    @Query() query: QueryChooseMaterialDetailDto,
    @Param('contractId') contractId: string
  ) {
    return await this.service.getChooseMaterialDetail(
      reqUser,
      query,
      contractId
    );
  }

  @ApiOperation({
    summary: '编辑合同材料',
    description: '编辑合同材料'
  })
  @ApiResponse({
    status: 200,
    description: '编辑合同材料成功'
  })
  @Patch('/:id')
  async addConsumeMaterial(
    @ReqUser() reqUser: IReqUser,
    @Param('id') id: string,
    @Body() data: ContractDetailUpdateDto
  ) {
    return await this.service.update(reqUser, id, data);
  }

  @ApiOperation({
    summary: '合同材料上下移',
    description: '合同材料上下移'
  })
  @ApiResponse({
    status: 200,
    description: '合同材料上下移成功'
  })
  @Post('/move')
  async move(
    @ReqUser() reqUser: IReqUser,
    @Body() data: EditMaterialDetailMoveDto
  ) {
    const { moveType } = data;
    if (moveType === 'up') {
      // 上移
      await this.service.up(data, reqUser);
    } else {
      // 下移
      await this.service.down(data, reqUser);
    }
    return true;
  }

  @ApiOperation({
    summary: '删除已选择材料',
    description: '删除已选择材料'
  })
  @ApiResponse({
    status: 200,
    description: '删除已选择材料成功'
  })
  @Delete('/detail/:id/:contractId')
  async delete(
    @ReqUser() reqUser: IReqUser,
    @Param('contractId') contractId: string,
    @Param('id') id: string
  ) {
    return await this.service.del(reqUser, id, contractId);
  }
}
