import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsIn,
  IsNotEmpty,
  IsOptional,
  IsString
} from 'class-validator';

import { FieldType } from '@/prisma/generated';

export class PartyACompanyResDto {
  @ApiProperty({ description: '公司id' })
  id: string;

  @ApiProperty({ description: '公司名称' })
  name: string;
}

export class PartyBCompanyResDto {
  @ApiProperty({ description: '公司id' })
  id: string;

  @ApiProperty({ description: '公司名称' })
  name: string;

  @ApiProperty({ description: '数据来源：（company:公司，supplier:供应商）' })
  type: string;
}

export class PartyQueryDto {
  @ApiProperty({ description: '公司名称' })
  @IsOptional({ message: '公司名称可以为空' })
  @IsString({ message: '公司名称必须是字符串' })
  name?: string;
}

export class SaveRuleFieldListDto {
  @ApiProperty({ description: '合同模板字段规则' })
  @IsNotEmpty({ message: '合同模板字段规则不能为空' })
  @IsArray({ message: '合同模板字段规则必须是数组' })
  ruleList: SaveRuleFieldDto[];
}

export class SaveRuleFieldDto {
  @ApiProperty({ description: '合同模板字段规则id' })
  @IsNotEmpty({ message: '合同模板字段规则id不能为空' })
  @IsString({ message: '合同模板字段规则id必须是字符串' })
  contractTemplateFieldRuleId: string;

  @ApiProperty({ description: '字段值(增值税税率需要)' })
  @IsOptional({ message: '字段值可以为空' })
  @IsString({ message: '字段值必须是字符串' })
  defaultValue?: string;

  @ApiProperty({ description: '字段值' })
  @IsOptional({ message: '字段值可以为空' })
  @IsString({ message: '字段值必须是字符串' })
  value?: string;

  @ApiProperty({ description: '字段编码' })
  @IsNotEmpty({ message: '字段编码不可以为空' })
  @IsString({ message: '字段编码必须是字符串' })
  code: string;

  @ApiProperty({ description: '字段类型' })
  @IsNotEmpty({ message: '字段类型不可以为空' })
  @IsIn(Object.values(FieldType), {
    message: '字段类型必须是有效枚举值'
  })
  @IsString({ message: '字段类型必须是字符串' })
  fieldType: FieldType;
}

export class ContractTemplateResDto extends PartyACompanyResDto {}
