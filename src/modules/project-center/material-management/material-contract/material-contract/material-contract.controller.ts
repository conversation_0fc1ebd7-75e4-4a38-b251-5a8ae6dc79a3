import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  Res
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { FastifyReply } from 'fastify';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  CheckRequiredFieldDto,
  MaterialContractCreateDto,
  MaterialContractResDto,
  MaterialContractUpdateAuditStatusDto,
  MaterialContractUpdateDto,
  MaterialContractUpdateSubmitStatusDto,
  QueryMaterialContractDto
} from './material-contract.dto';
import { MaterialContractService } from './material-contract.service';
import {
  ContractTemplateResDto,
  PartyACompanyResDto,
  PartyBCompanyResDto,
  PartyQueryDto,
  SaveRuleFieldListDto
} from './other.dto';

@ApiTags('合同编制/合同编制')
@Controller('contract-compilation')
export class MaterialContractController {
  constructor(private readonly service: MaterialContractService) {}

  @ApiOperation({
    summary: '前置查询（甲方公司）',
    description: '前置查询（甲方公司）'
  })
  @ApiResponse({
    status: 200,
    description: '甲方公司查询成功',
    type: PartyACompanyResDto,
    isArray: true
  })
  @Get('partyA-company')
  async getPartyACompanyList(
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser,
    @Query() query: PartyQueryDto
  ) {
    return await this.service.getPartyACompanyList(
      req,
      reqUser,
      true,
      query?.name
    );
  }

  @ApiOperation({
    summary: '前置查询（乙方（公司+供应商））',
    description: '前置查询（乙方（公司+供应商））'
  })
  @ApiResponse({
    status: 200,
    description: '乙方（公司+供应商）查询成功',
    type: PartyBCompanyResDto,
    isArray: true
  })
  @Get('partyB-company')
  async getPartBCompanyList(
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser,
    @Query() query: PartyQueryDto
  ) {
    return await this.service.getPartBCompanyList(
      req,
      reqUser,
      true,
      query?.name
    );
  }

  @ApiOperation({
    summary: '前置查询（合同范本查询）',
    description: '前置查询（合同范本）查询）'
  })
  @ApiResponse({
    status: 200,
    description: '合同范本查询成功',
    type: ContractTemplateResDto,
    isArray: true
  })
  @Get('contract-template')
  async getContractTemplateList(
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser,
    @Query() query: PartyQueryDto
  ) {
    return await this.service.getContractTemplateList(
      req,
      reqUser,
      query?.name
    );
  }

  @ApiOperation({
    summary: '获取合同列表',
    description: '获取合同列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取合同列表成功',
    type: MaterialContractResDto,
    isArray: true
  })
  @Get()
  async getList(
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser,
    @Query() query: QueryMaterialContractDto
  ) {
    return await this.service.getList(req, reqUser, query);
  }

  @ApiOperation({
    summary: '新增合同',
    description: '新增合同'
  })
  @ApiResponse({
    status: 200,
    description: '新增合同成功'
  })
  @Post()
  async add(
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser,
    @Body() data: MaterialContractCreateDto
  ) {
    return await this.service.add(req, reqUser, data);
  }

  @ApiOperation({
    summary: '判断是否可以进入货物清单',
    description: '判断是否可以进入货物清单'
  })
  @ApiResponse({
    status: 200,
    description: '判断是否可以进入货物清单'
  })
  @Get('/check-field')
  async checkField(
    @ReqUser() reqUser: IReqUser,
    @Query() data: CheckRequiredFieldDto
  ) {
    return await this.service.checkRequiredField(
      data.id,
      reqUser,
      data.contractTemplateId,
      'boolean'
    );
  }

  @ApiOperation({
    summary: '编辑合同',
    description: '编辑合同'
  })
  @ApiResponse({
    status: 200,
    description: '编辑合同成功'
  })
  @Patch('/:id')
  async update(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() data: MaterialContractUpdateDto
  ) {
    return await this.service.updateOne(reqUser, id, data);
  }

  @ApiOperation({
    summary: '删除合同',
    description: '删除合同'
  })
  @ApiResponse({
    status: 200,
    description: '删除合同成功'
  })
  @Delete('/:id')
  async delete(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    return await this.service.deleteOne(reqUser, id);
  }

  @ApiOperation({
    summary: '获取字段规则列表',
    description: '获取字段规则列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取字段规则列表成功',
    isArray: true
  })
  @Get('/rule-field/:id')
  async getRuleFieldList(
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser,
    @Param('id') id: string
  ) {
    return await this.service.getRuleFieldList(req, id, reqUser);
  }

  @ApiOperation({
    summary: '新增编辑字段规则列表',
    description: '新增编辑字段规则列表'
  })
  @ApiResponse({
    status: 200,
    description: '新增编辑字段规则列表成功',
    isArray: true
  })
  @Patch('/rule-field/:id')
  async saveRuleField(
    @ReqUser() reqUser: IReqUser,
    @Param('id') id: string,
    @Body() data: SaveRuleFieldListDto
  ) {
    return await this.service.saveRuleField(id, reqUser, data.ruleList);
  }

  @ApiOperation({
    summary: '每次跳转业务清单详情页时统计价格',
    description: '每次跳转业务清单详情页时统计价格'
  })
  @ApiResponse({
    status: 200,
    description: '每次跳转业务清单详情页时统计价格成功'
  })
  @Patch('/calculatePrice/:id')
  async calculatePrice(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    await this.service.checkDetailRequiredField(id, reqUser);
    return await this.service.calculatePrice(id, reqUser);
  }

  @ApiOperation({
    summary: '合同提交提交状态更改',
    description: '合同提交提交状态更改'
  })
  @ApiResponse({
    status: 200,
    description: '合同提交提交状态更改成功'
  })
  @Patch('/submitStatus/:id')
  async editSubmitStatus(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() data: MaterialContractUpdateSubmitStatusDto
  ) {
    return await this.service.editSubmitStatus(id, reqUser, data);
  }

  @ApiOperation({
    summary: '合同审核状态更改',
    description: '合同审核状态更改'
  })
  @ApiResponse({
    status: 200,
    description: '合同审核状态更改成功'
  })
  @Patch('/auditStatus/:id')
  async editAuditStatus(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() data: MaterialContractUpdateAuditStatusDto
  ) {
    return await this.service.editAuditStatus(id, reqUser, data);
  }

  @ApiOperation({
    summary: '导出',
    description: '导出'
  })
  @ApiResponse({
    status: 200,
    description: '导出成功'
  })
  @Get('/export')
  async export(
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser,
    @Res() res: FastifyReply
  ) {
    const bis = await this.service.export(req, reqUser);
    const filename = `合同数据`;
    const encodedFilename = encodeURIComponent(filename);
    res
      .header(
        'Content-Disposition',
        `attachment; filename="${encodedFilename}.xlsx";`
      )
      .header(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      )
      .header('Content-Transfer-Encoding', 'binary')
      .header('Cache-Control', 'no-cache, no-store, must-revalidate')
      .send(bis); // 直接发送 Buffer 数据
  }
}
