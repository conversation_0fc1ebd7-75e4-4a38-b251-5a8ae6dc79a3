import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';
import { PlatformModule } from '@/modules/platform/platform.module';

import { ContractConcreteSurchargeController } from './contract-concrete-surcharge/contract-concrete-surcharge.controller';
import { ContractConcreteSurchargeService } from './contract-concrete-surcharge/contract-concrete-surcharge.service';
import { ContractMaterialDetailController } from './contract-material-detail/contract-material-detail.controller';
import { ContractMaterialDetailService } from './contract-material-detail/contract-material-detail.service';
import { MaterialContractController } from './material-contract/material-contract.controller';
import { MaterialContractService } from './material-contract/material-contract.service';
import { MaterialContractAccessoryController } from './material-contract-accessory/material-contract-accessory.controller';
import { MaterialContractAccessoryService } from './material-contract-accessory/material-contract-accessory.service';
import { UnitCalculationController } from './unit-calculation/unit-calculation.controller';
import { UnitCalculationService } from './unit-calculation/unit-calculation.service';

@Module({
  imports: [PrismaModule, PlatformModule],
  controllers: [
    MaterialContractController,
    ContractMaterialDetailController,
    UnitCalculationController,
    ContractConcreteSurchargeController,
    MaterialContractAccessoryController
  ],
  providers: [
    MaterialContractService,
    ContractMaterialDetailService,
    UnitCalculationService,
    ContractConcreteSurchargeService,
    MaterialContractAccessoryService
  ],
  exports: [MaterialContractService]
})
export class MaterialContractModule {}
