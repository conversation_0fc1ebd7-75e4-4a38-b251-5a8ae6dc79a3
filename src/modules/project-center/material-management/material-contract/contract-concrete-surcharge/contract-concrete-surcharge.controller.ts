import { Body, Controller, Get, Param, Patch } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  ContractConcreteSurchargeResDto,
  ContractConcreteSurchargeUpdateDto
} from './contract-concrete-surcharge.dto';
import { ContractConcreteSurchargeService } from './contract-concrete-surcharge.service';

@ApiTags('合同编制/混凝土附加费')
@Controller('contract-concrete-surcharge')
export class ContractConcreteSurchargeController {
  constructor(private readonly service: ContractConcreteSurchargeService) {}

  @ApiOperation({
    summary: '获取合同附加费列表',
    description: '获取合同附加费列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取合同附加费列表成功',
    type: ContractConcreteSurchargeResDto,
    isArray: true
  })
  @Get('/:contractId')
  async getList(
    @Param('contractId') contractId: string,
    @ReqUser() reqUser: IReqUser
  ) {
    return await this.service.getList(contractId, reqUser);
  }

  @Patch('/:id')
  async update(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() data: ContractConcreteSurchargeUpdateDto
  ) {
    return await this.service.update(id, reqUser, data);
  }
}
