import { ApiProperty, PickType } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class BaseContractConcreteSurchargeDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '合同id' })
  materialContractId: string;

  @ApiProperty({ description: '名称' })
  name: string;

  @ApiProperty({ description: '单位' })
  unit: string;

  @ApiProperty({ description: '变更前单价' })
  price: number;

  @ApiProperty({ description: '变更后单价' })
  @IsOptional({ message: '变更后单价可以为空' })
  @IsNumber({}, { message: '变更后单价必须是数字' })
  changePrice?: number;

  @ApiProperty({ description: '变更计算单价' })
  @IsOptional({ message: '变更计算单价可以为空' })
  @IsNumber({}, { message: '变更计算单价必须是数字' })
  changeCalculatePrice?: number;

  @ApiProperty({ description: '备注' })
  @IsOptional({ message: '备注可以为空' })
  @IsString({ message: '备注必须是字符串' })
  remark?: string;
}

export class ContractConcreteSurchargeResDto extends PickType(
  BaseContractConcreteSurchargeDto,
  [
    'id',
    'name',
    'price',
    'changePrice',
    'unit',
    'changeCalculatePrice',
    'materialContractId',
    'remark'
  ] as const
) {}

export class ContractConcreteSurchargeUpdateDto extends PickType(
  BaseContractConcreteSurchargeDto,
  ['changePrice', 'changeCalculatePrice', 'remark'] as const
) {}
