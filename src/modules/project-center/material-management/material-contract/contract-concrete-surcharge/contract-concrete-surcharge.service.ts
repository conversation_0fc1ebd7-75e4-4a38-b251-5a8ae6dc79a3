import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { ConcreteSurchargeSeedService } from '@/common/modules/prisma/seed/services';

import { ContractConcreteSurchargeUpdateDto } from './contract-concrete-surcharge.dto';

@Injectable()
export class ContractConcreteSurchargeService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly concreteSurchargeSeedService: ConcreteSurchargeSeedService
  ) {}

  async init(
    txPrisma: PrismaService,
    reqUser: IReqUser,
    contractId: string,
    parentData: any[] = []
  ) {
    await this.concreteSurchargeSeedService.init(
      txPrisma,
      reqUser,
      contractId,
      parentData
    );
  }

  async getList(contractId: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    return await this.prisma.contractConcreteSurcharge.findMany({
      where: {
        materialContractId: contractId,
        tenantId,
        orgId
      },
      orderBy: {
        createAt: 'asc'
      }
    });
  }

  async update(
    id: string,
    reqUser: IReqUser,
    data: ContractConcreteSurchargeUpdateDto
  ) {
    return await this.prisma.contractConcreteSurcharge.update({
      where: {
        id,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      },
      data: {
        ...data,
        updateBy: reqUser.id
      }
    });
  }
}
