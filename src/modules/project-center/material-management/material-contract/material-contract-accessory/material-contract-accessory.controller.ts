import { Body, Controller, Delete, Get, Param, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  materialContractAccessoryCreateDto,
  materialContractAccessoryResDto
} from './material-contract-accessory.dto';
import { MaterialContractAccessoryService } from './material-contract-accessory.service';

@ApiTags('合同编制/合同附件')
@Controller('material-contract-accessory')
export class MaterialContractAccessoryController {
  constructor(private readonly service: MaterialContractAccessoryService) {}

  @ApiOperation({
    summary: '获取合同附件列表',
    description: '获取合同附件列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取合同附件列表成功',
    type: materialContractAccessoryResDto,
    isArray: true
  })
  @Get('/:contractId')
  async getList(
    @Param('contractId') contractId: string,
    @ReqUser() reqUser: IReqUser
  ) {
    return await this.service.getList(contractId, reqUser);
  }

  @ApiOperation({
    summary: '新增合同附件列表',
    description: '新增合同附件列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取合同附件列表成功',
    type: materialContractAccessoryResDto,
    isArray: true
  })
  @Post()
  async add(
    @ReqUser() reqUser: IReqUser,
    @Body() data: materialContractAccessoryCreateDto
  ) {
    return await this.service.add(data, reqUser);
  }

  @ApiOperation({
    summary: '删除合同附件列表',
    description: '删除合同附件列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取合同附件列表成功',
    type: materialContractAccessoryResDto,
    isArray: true
  })
  @Delete('/:id')
  async delete(@ReqUser() reqUser: IReqUser, @Param('id') id: string) {
    return await this.service.delete(id, reqUser);
  }
}
