import { ApiProperty, PickType } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class BaseMaterialContractAccessoryDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '合同id' })
  @IsNotEmpty({ message: '合同id不能为空' })
  @IsString({ message: '合同id必须是字符串' })
  materialContractId: string;

  @ApiProperty({ description: '文件名称' })
  @IsNotEmpty({ message: '文件名称不能为空' })
  @IsString({ message: '文件名称必须是字符串' })
  fileName: string;

  @ApiProperty({ description: '文件扩展名' })
  @IsNotEmpty({ message: '文件扩展名不能为空' })
  @IsString({ message: '文件扩展名必须是字符串' })
  fileExt: string;

  @ApiProperty({ description: '文件key' })
  @IsNotEmpty({ message: '文件key不能为空' })
  @IsString({ message: '文件key必须是字符串' })
  fileKey: string;

  @ApiProperty({ description: '文件大小' })
  @IsNotEmpty({ message: '文件大小不能为空' })
  @IsString({ message: '文件大小必须是字符串' })
  fileSize: string;

  @ApiProperty({ description: '文件类型' })
  @IsNotEmpty({ message: '文件类型不能为空' })
  @IsString({ message: '文件类型必须是字符串' })
  fileContentType: string;

  @ApiProperty({ description: '备注' })
  @IsOptional({ message: '备注可以为空' })
  @IsString({ message: '备注必须是字符串' })
  remark?: string;
}

export class materialContractAccessoryResDto extends PickType(
  BaseMaterialContractAccessoryDto,
  [
    'id',
    'materialContractId',
    'fileName',
    'fileSize',
    'fileExt',
    'fileContentType',
    'fileKey',
    'remark'
  ] as const
) {}

export class materialContractAccessoryCreateDto extends PickType(
  BaseMaterialContractAccessoryDto,
  [
    'materialContractId',
    'fileName',
    'fileSize',
    'fileExt',
    'fileContentType',
    'fileKey',
    'remark'
  ] as const
) {}
