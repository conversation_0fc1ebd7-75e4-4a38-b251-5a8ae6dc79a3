import { Body, Controller, Delete, Get, Param, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  MaterialReturnSalesAttachmentCreateDto,
  MaterialReturnSalesAttachmentResDto
} from './material-return-sales-form-attachment.dto';
import { MaterialReturnSalesFormAttachmentService } from './material-return-sales-form-attachment.service';

@ApiTags('退货单/附件')
@Controller('material-return-sales-form-attachment')
export class MaterialReturnSalesFormAttachmentController {
  constructor(
    private readonly service: MaterialReturnSalesFormAttachmentService
  ) {}

  @ApiOperation({
    summary: '获取附件列表',
    description: '获取附件列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取附件列表成功',
    type: MaterialReturnSalesAttachmentResDto,
    isArray: true
  })
  @Get('/:returnSalesFormId')
  async getList(
    @Param('returnSalesFormId') returnSalesFormId: string,
    @ReqUser() reqUser: IReqUser
  ) {
    return await this.service.getList(returnSalesFormId, reqUser);
  }

  @ApiOperation({
    summary: '新增附件列表',
    description: '新增附件列表'
  })
  @ApiResponse({
    status: 200,
    description: '新增附件列表成功'
  })
  @Post()
  async add(
    @ReqUser() reqUser: IReqUser,
    @Body() data: MaterialReturnSalesAttachmentCreateDto
  ) {
    return await this.service.add(data, reqUser);
  }

  @ApiOperation({
    summary: '删除附件列表',
    description: '删除附件列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取附件列表成功'
  })
  @Delete('/:id')
  async delete(@ReqUser() reqUser: IReqUser, @Param('id') id: string) {
    return await this.service.delete(id, reqUser);
  }
}
