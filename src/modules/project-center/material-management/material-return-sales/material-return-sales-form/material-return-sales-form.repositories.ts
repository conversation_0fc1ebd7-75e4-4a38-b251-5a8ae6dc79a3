import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { Prisma } from '@/prisma/generated';

import {
  MaterialReturnSalesFormResDto,
  QueryMaterialReturnSalesFormDto
} from './material-return-sales-form.dto';

@Injectable()
export class MaterialReturnSalesFormRepository {
  constructor(private readonly prisma: PrismaService) {}

  async getList(
    reqUser: IReqUser,
    query: QueryMaterialReturnSalesFormDto
  ): Promise<MaterialReturnSalesFormResDto[]> {
    const { onlyViewSelf = false } = query;
    const res: MaterialReturnSalesFormResDto[] = await this.prisma.$queryRaw`
       with temp_material_categories as (
        select
          mrd.return_sales_form_id,
          STRING_AGG(distinct
            case
              when position('|' in mdc.full_name) > 0
              then split_part(mdc.full_name, '|', 2)
              else split_part(mdc.full_name, '|', 1)
            end,
            ','
          ) filter (where mdc.full_name is not null) material_categories
        from material_return_sales_form_detail mrd
        join material_dictionary_detail mdd
          on mdd.id = mrd.material_id
          and mdd.is_deleted = false
          and mdd.tenant_id = ${reqUser.tenantId}
        join material_dictionary_category mdc
          on mdc.id = mdd.material_dictionary_category_id
          and mdc.is_deleted = false
          and mdc.tenant_id = ${reqUser.tenantId}
          and mdc.org_id = mdd.org_id
        where mrd.is_deleted = false
          and mrd.tenant_id = ${reqUser.tenantId}
          and mrd.org_id = ${reqUser.orgId}
        group by mrd.return_sales_form_id
      )
      select
        mr.material_settlement_status
        ,mr.id
        ,mr.code
        ,mr.purchase_type
        ,o.seal_name as project_name
        ,mr.supplier_name
        ,mr.supplier_id
				,mr.contract_id
        ,mr.contract_name
        ,tmc.material_categories
				,mr.amount
        ,u.nickname as creator
        ,mr.year
        ,mr.month
        ,mr.day
        ,mr.submit_status
        ,mr.audit_status
      from material_return_sales_form mr
      left join temp_material_categories tmc
        on tmc.return_sales_form_id = mr.id
      left join platform_meta.org o
        on o.tenant_id = mr.tenant_id
        and o.id = mr.org_id
      left join platform_meta."user" u
        on u.id = mr.create_By
      where mr.tenant_id = ${reqUser.tenantId}
        and mr.org_id = ${reqUser.orgId}
        and mr.is_deleted = false
        ${onlyViewSelf ? Prisma.sql`and mr.create_By = ${reqUser.id}` : Prisma.empty}
        ${query.year ? Prisma.sql`and mr.year = ${query.year}` : Prisma.empty}
        ${query.month ? Prisma.sql`and mr.month = ${query.month}` : Prisma.empty}
        ${query.day ? Prisma.sql`and mr.day = ${query.day}` : Prisma.empty}
      order by mr.code desc, mr.id desc
    `;
    return res;
  }

  async getOne(id: string, reqUser: IReqUser) {
    const res = await this.prisma.$queryRaw<any[]>`
      select mr.*, o.seal_name as project_name, u.nickname as creator from material_return_sales_form mr
      left join platform_meta.org o
        on o.tenant_id = mr.tenant_id
        and o.id = mr.org_id
      left join platform_meta."user" u
        on u.id = mr.create_By
      where mr.id = ${id}
        and mr.tenant_id = ${reqUser.tenantId}
        and mr.org_id = ${reqUser.orgId}
        and mr.is_deleted = false
    `;
    return res[0];
  }
}
