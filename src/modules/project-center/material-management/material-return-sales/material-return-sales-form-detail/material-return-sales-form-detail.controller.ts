import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  MaterialReturnSalesFormChooseCategoryTreeResDto,
  MaterialReturnSalesFormChooseDetailsQueryDto,
  MaterialReturnSalesFormChooseDetailsResDto,
  MaterialReturnSalesFormDetailCreateDto,
  MaterialReturnSalesFormDetailResDto,
  MaterialReturnSalesFormDetailUpdateDto
} from './material-return-sales-form-detail.dto';
import { MaterialReturnSalesFormDetailService } from './material-return-sales-form-detail.service';

@ApiTags('/退货单/明细')
@Controller('material-return-sales-form-detail')
export class MaterialReturnSalesFormDetailController {
  constructor(private readonly service: MaterialReturnSalesFormDetailService) {}

  @ApiOperation({ summary: '查询可选的材料分类' })
  @ApiResponse({
    status: 200,
    description: '获取查询可选的材料分类成功',
    type: MaterialReturnSalesFormChooseCategoryTreeResDto,
    isArray: true
  })
  @Get('/choose/materialCategory/:returnSalesFormId')
  async getChooseMaterialCategory(
    @Param('returnSalesFormId') returnSalesFormId: string,
    @ReqUser() reqUser: IReqUser
  ): Promise<MaterialReturnSalesFormChooseCategoryTreeResDto[]> {
    return await this.service.getChooseMaterialCategory(
      returnSalesFormId,
      reqUser
    );
  }

  @ApiOperation({ summary: '查询可选的材料明细' })
  @ApiResponse({
    status: 200,
    description: '获取查询可选的材料明细成功',
    type: MaterialReturnSalesFormChooseDetailsResDto,
    isArray: true
  })
  @Get('/choose/materialDetails')
  async getChooseMaterialDetails(
    @Query() query: MaterialReturnSalesFormChooseDetailsQueryDto,
    @ReqUser() reqUser: IReqUser
  ): Promise<MaterialReturnSalesFormChooseDetailsResDto[]> {
    return await this.service.getChooseMaterialDetails(query, reqUser);
  }

  @ApiOperation({ summary: '材料明细保存' })
  @ApiResponse({
    status: 200,
    description: '材料明细保存成功'
  })
  @Post('')
  async add(
    @Body() body: MaterialReturnSalesFormDetailCreateDto,
    @ReqUser() reqUser: IReqUser
  ): Promise<boolean> {
    return await this.service.add(body, reqUser);
  }

  @ApiOperation({ summary: '修改退货数量' })
  @ApiResponse({
    status: 200,
    description: '修改退货数量成功'
  })
  @Patch('/:id')
  async update(
    @Param('id') id: string,
    @Body() body: MaterialReturnSalesFormDetailUpdateDto,
    @ReqUser() reqUser: IReqUser
  ): Promise<boolean> {
    return await this.service.update(id, body, reqUser);
  }

  @ApiOperation({ summary: '删除父级' })
  @ApiResponse({
    status: 200,
    description: '删除父级成功'
  })
  @Delete('/:id')
  async delete(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser
  ): Promise<boolean> {
    return await this.service.delete(id, reqUser);
  }

  @ApiOperation({ summary: '查询退货单明细' })
  @ApiResponse({
    status: 200,
    description: '查询退货单明细成功',
    type: MaterialReturnSalesFormDetailResDto,
    isArray: true
  })
  @Get('/:id')
  async getList(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    return await this.service.getDetailList(id, reqUser);
  }

  @ApiOperation({ summary: '查询退货单下收料明细的最早时间' })
  @ApiResponse({
    status: 200,
    description: '查询退货单下收料明细的最早时间成功'
  })
  @Get('/receivingMinDate')
  async getEarliestTime(
    @Query('supplierId') supplierId: string,
    @Query('contractId') contractId: string,
    @ReqUser() reqUser: IReqUser
  ): Promise<string | null> {
    return await this.service.getEarliestTime(supplierId, contractId, reqUser);
  }

  @ApiOperation({
    summary: '退货单材料上下移',
    description: '退货单材料上下移'
  })
  @ApiResponse({
    status: 200,
    description: '退货单材料上下移成功'
  })
  @Post('/move')
  async move(
    @ReqUser() reqUser: IReqUser,
    @Body('fromId') fromId: string,
    @Body('toId') toId: string
  ) {
    await this.service.move(reqUser, fromId, toId);
    return true;
  }
}
