import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';
import { PlatformModule } from '@/modules/platform/platform.module';

import { MaterialReturnSalesFormController } from './material-return-sales-form/material-return-sales-form.controller';
import { MaterialReturnSalesFormRepository } from './material-return-sales-form/material-return-sales-form.repositories';
import { MaterialReturnSalesFormService } from './material-return-sales-form/material-return-sales-form.service';
import { MaterialReturnSalesFormAttachmentController } from './material-return-sales-form-attachment/material-return-sales-form-attachment.controller';
import { MaterialReturnSalesFormAttachmentService } from './material-return-sales-form-attachment/material-return-sales-form-attachment.service';
import { MaterialReturnSalesFormDetailController } from './material-return-sales-form-detail/material-return-sales-form-detail.controller';
import { MaterialReturnSalesFormDetailRepository } from './material-return-sales-form-detail/material-return-sales-form-detail.repositories';
import { MaterialReturnSalesFormDetailService } from './material-return-sales-form-detail/material-return-sales-form-detail.service';

@Module({
  imports: [PrismaModule, PlatformModule],
  controllers: [
    MaterialReturnSalesFormController,
    MaterialReturnSalesFormDetailController,
    MaterialReturnSalesFormAttachmentController
  ],
  providers: [
    MaterialReturnSalesFormDetailRepository,
    MaterialReturnSalesFormRepository,
    MaterialReturnSalesFormService,
    MaterialReturnSalesFormDetailService,
    MaterialReturnSalesFormAttachmentService
  ]
})
export class MaterialReturnSalesModule {}
