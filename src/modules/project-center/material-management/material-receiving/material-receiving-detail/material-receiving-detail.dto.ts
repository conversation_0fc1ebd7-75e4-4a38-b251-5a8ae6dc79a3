import { ApiProperty, PickType } from '@nestjs/swagger';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString
} from 'class-validator';

export class BaseMaterialReceivingDetailDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '验收单明细ID' })
  @IsNotEmpty({ message: '验收单明细ID不能为空' })
  @IsString({ message: '验收单明细ID必须是字符串' })
  materialIncomingInspectionDetailId: string;

  @ApiProperty({ description: '收料单ID' })
  @IsNotEmpty({ message: '收料单ID不能为空' })
  @IsString({ message: '收料单ID必须是字符串' })
  receivingId: string;

  @ApiProperty({ description: '收料单明细ID(不传)' })
  materialReceivingDetailId?: string;

  @ApiProperty({ description: '材料ID' })
  @IsNotEmpty({ message: '材料ID不能为空' })
  @IsString({ message: '材料ID必须是字符串' })
  materialId: string;

  @ApiProperty({ description: '材料名称' })
  @IsNotEmpty({ message: '材料名称不能为空' })
  @IsString({ message: '材料名称必须是字符串' })
  materialName: string;

  @ApiProperty({ description: '材料规格' })
  @IsNotEmpty({ message: '材料规格不能为空' })
  @IsString({ message: '材料规格必须是字符串' })
  materialSpec: string;

  @ApiProperty({ description: '质量标准' })
  @IsOptional({ message: '质量标准可以为空' })
  @IsString({ message: '质量标准必须是字符串' })
  qualityStandard?: string;

  @ApiProperty({ description: '计量单位' })
  @IsOptional({ message: '计量单位可以为空' })
  @IsString({ message: '计量单位必须是字符串' })
  unit?: string;

  @ApiProperty({ description: '收料单计量单位' })
  @IsOptional({ message: '收料单计量单位可以为空' })
  @IsString({ message: '收料单计量单位必须是字符串' })
  incomingUnit?: string;

  @ApiProperty({ description: '合同计量单位' })
  @IsOptional({ message: '合同计量单位可以为空' })
  @IsString({ message: '合同计量单位必须是字符串' })
  contractUnit?: string;

  @ApiProperty({ description: '实收数量' })
  @IsOptional({ message: '实收数量可以为空' })
  @IsNumber({}, { message: '实收数量必须是数字' })
  actualQuantity?: number | null;

  @ApiProperty({ description: '单价类型' })
  @IsOptional({ message: '单价类型可以为空' })
  @IsString({ message: '单价类型必须是字符串' })
  priceType?: string | null;

  @ApiProperty({ description: '不含税单价' })
  @IsOptional({ message: '合同单价可以为空' })
  @IsNumber({}, { message: '合同单价必须是数字' })
  priceExcludingTax?: number | null;

  @ApiProperty({ description: '含税单价' })
  @IsOptional({ message: '收料单价可以为空' })
  @IsNumber({}, { message: '收料单价必须是数字' })
  priceIncludingTax?: number | null;

  @ApiProperty({ description: '不含税金额' })
  @IsOptional({ message: '不含税金额可以为空' })
  @IsNumber({}, { message: '不含税金额必须是数字' })
  taxExcludedAmount?: number | null;

  @ApiProperty({ description: '含税金额' })
  @IsOptional({ message: '含税金额可以为空' })
  @IsNumber({}, { message: '含税金额必须是数字' })
  taxIncludedAmount?: number | null;

  @ApiProperty({ description: '排序号' })
  orderNo: number;

  @ApiProperty({ description: '备注' })
  @IsOptional({ message: '备注可以为空' })
  @IsString({ message: '备注必须是字符串' })
  remark?: string | null;
}

export class MaterialReceivingDetailResDto {
  @ApiProperty({ description: '税率' })
  taxRate: number;

  @ApiProperty({ description: '收料单明细' })
  list: MaterialReceivingDetailResListDto[];
}

export class MaterialReceivingDetailResListDto extends PickType(
  BaseMaterialReceivingDetailDto,
  [
    'id',
    'receivingId',
    'materialId',
    'materialName',
    'materialSpec',
    'priceType',
    'unit',
    'orderNo',
    'qualityStandard',
    'priceExcludingTax',
    'priceIncludingTax',
    'actualQuantity',
    'taxExcludedAmount',
    'taxIncludedAmount',
    'remark'
  ] as const
) {
  @ApiProperty({ description: '材料类别' })
  materialCategories: string;
}

export class MaterialReceivingDetailCreateDto {
  @ApiProperty({
    description: '数据数组'
  })
  @IsNotEmpty({ message: '数据数组不能为空' })
  @IsArray({ message: '数据数组必须为数组' })
  list: MaterialReceivingDetailCreateListDto[];
}

export class MaterialReceivingDetailCreateListDto extends PickType(
  BaseMaterialReceivingDetailDto,
  [
    'materialIncomingInspectionDetailId',
    'materialReceivingDetailId',
    'receivingId',
    'materialId',
    'materialName',
    'materialSpec',
    'incomingUnit',
    'contractUnit',
    'qualityStandard',
    'priceType',
    'priceExcludingTax',
    'priceIncludingTax',
    'actualQuantity',
    'remark'
  ] as const
) {}

export class MaterialReceivingDetailCreateOrUpdateListDto extends MaterialReceivingDetailCreateListDto {
  id?: string;
  addOrUpdate: 'add' | 'update';
}

export class MaterialReceivingDetailUpdateDto extends PickType(
  BaseMaterialReceivingDetailDto,
  [
    'receivingId',
    'priceExcludingTax',
    'priceIncludingTax',
    'taxExcludedAmount',
    'taxIncludedAmount',
    'remark'
  ] as const
) {}

export class MaterialReceivingIncomingDetailResDto {
  @ApiProperty({ description: '收料-验收关联ID' })
  id: string;

  @ApiProperty({ description: '验收单编码' })
  code: string;

  @ApiProperty({ description: '进场数量' })
  siteEntryQuantity: number;

  @ApiProperty({ description: '实收数量' })
  actualQuantity: number;

  @ApiProperty({ description: '计量单位' })
  unit: string;
}
