import {
  BadRequestException,
  Injectable,
  NotFoundException
} from '@nestjs/common';
import { Decimal } from '@prisma/client/runtime/library';
import * as uuid from 'uuid';

import { CommonRepositories } from '@/common/common-repositories';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import {
  AuditStatus,
  Prisma,
  PurchaseType,
  SubmitStatus
} from '@/prisma/generated';

import {
  MaterialReceivingDetailCreateDto,
  MaterialReceivingDetailCreateListDto,
  MaterialReceivingDetailCreateOrUpdateListDto,
  MaterialReceivingDetailUpdateDto
} from './material-receiving-detail.dto';

@Injectable()
export class MaterialReceivingDetailService {
  constructor(private readonly prisma: PrismaService) {}

  // 查询收料单下的明细
  async getList(receivingId: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;
    const receiving = await this.getReceiving(receivingId, reqUser);
    let contractId = receiving.contractId;
    const receivingDate =
      receiving.year + '-' + receiving.month + '-' + receiving.day;
    let taxRate;
    // 查询合同下是否有补充协议
    let contractChild;
    if (contractId) {
      contractChild = await this.prisma.materialContract.findFirst({
        select: {
          id: true
        },
        where: {
          createAt: {
            lte: new Date(receivingDate)
          },
          parentId: contractId,
          orgId,
          tenantId,
          isDeleted: false,
          submitStatus: SubmitStatus.SUBMITTED,
          auditStatus: AuditStatus.APPROVED
        },
        orderBy: {
          createAt: 'desc'
        }
      });
    }
    if (contractChild) {
      contractId = contractChild.id;
    }
    if (contractId) {
      // 查询税率
      taxRate = await this.getTaxRate(contractId, reqUser);
    }
    const list = await this.prisma.materialReceivingDetail.findMany({
      where: {
        receivingId,
        orgId,
        tenantId,
        isDeleted: false
      },
      orderBy: {
        orderNo: 'asc'
      }
    });
    return {
      list,
      taxRate
    };
  }

  /**
   * 查询合同税率
   * @param id 合同id
   */
  async getTaxRate(id: string, reqUser: IReqUser) {
    // 查询合同
    const contract = await this.prisma.materialContract.findUnique({
      where: {
        id,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      }
    });
    const res = await this.prisma.$queryRaw<any[]>`
      select 
        ctfr."id", mcfr.decimal_value as value from field_rule fr
        join contract_template_field_rule ctfr
        on ctfr.field_rule_id = fr.id 
          and ctfr.contract_template_id = ${contract?.contractTemplateId}
          and ctfr.is_deleted = false
        join material_contract_field_rule mcfr
          on mcfr.contract_template_field_rule_id = ctfr."id"
          and mcfr.is_deleted = false
          and mcfr.org_id = ${reqUser.orgId}
          and mcfr.tenant_id = ${reqUser.tenantId}
          and mcfr.material_contract_id = ${id}
        where fr.code = '增值税税率' and fr.is_deleted = false
    `;
    if (res.length) {
      return res[0].value;
    } else {
      return null;
    }
  }

  // 查询可选的进场验收单
  async getChooseIncomingBills(receivingId: string, reqUser: IReqUser) {
    // 查询收料单绑定的供应商id
    const { tenantId, orgId } = reqUser;
    const receiving = await this.getReceiving(receivingId, reqUser);
    return await this.prisma.$queryRaw<any[]>`
    with temp_material_categories as (
        select
          miid.incoming_inspection_id,
          STRING_AGG(distinct
            case
              when position('|' in mdc.full_name) > 0
              then split_part(mdc.full_name, '|', 2)
              else split_part(mdc.full_name, '|', 1)
            end,
            ','
          ) filter (where mdc.full_name is not null) material_categories
        from material_incoming_inspection_detail miid
        join material_dictionary_detail mdd
          on mdd.id = miid.material_id
          and mdd.is_deleted = false
          and mdd.tenant_id = ${reqUser.tenantId}
        join material_dictionary_category mdc
          on mdc.id = mdd.material_dictionary_category_id
          and mdc.is_deleted = false
          and mdc.tenant_id = ${reqUser.tenantId}
          and mdc.org_id = mdd.org_id
        where miid.is_deleted = false
          and miid.tenant_id = ${reqUser.tenantId}
          and miid.org_id = ${reqUser.orgId}
        group by miid.incoming_inspection_id
      )
       select
        mii.id
        ,mii.code
        ,mii.purchase_type
        ,mii.supplier_name
        ,mii.contract_name
        ,tmc.material_categories
        ,mii.creator
        ,CONCAT_WS('-', mii.year, mii.month, mii.day) as incoming_inspection_date
        ,mii.submit_status
        ,mii.audit_status
        ,case mii.audit_status
					when 'APPROVED' then 1
					when 'PENDING' then 2
					when 'AUDITING' then 3
					when 'REJECTED' then 4
				else 5 end as audit_status_order
				,case mii.submit_status
					when 'SUBMITTED' then 1
					when 'PENDING' then 2
				else 3 end as submit_status_order
      from material_incoming_inspection mii
      left join temp_material_categories tmc
        on tmc.incoming_inspection_id = mii.id
      where mii.org_id = ${orgId}
        and mii.tenant_id = ${tenantId}
        and mii.supplier_id = ${receiving.supplierId}
        and mii.contract_id = ${receiving.contractId}
        and mii.is_deleted = false
        and EXISTS (
          select 
            1 
          from material_incoming_inspection_detail miid
          where miid.org_id = ${orgId}
            and miid.tenant_id = ${tenantId}
            and miid.is_deleted = false
            and miid.incoming_inspection_id = mii.id
            and miid.id not in (
              select 
                mrid.incoming_inspection_detail_id
              from material_receiving_incoming_detail mrid
              where mrid.org_id = ${orgId}
                and mrid.tenant_id = ${tenantId}
                and mrid.is_deleted = false
            )
        )
        order by audit_status_order asc, submit_status_order asc, mii.create_at asc
    `;
  }

  /**
   * 根据验收单查询验收单的明细（过滤已被选的）
   * @param incomingInspectionId
   * @param reqUser
   * @returns
   */
  async getChooseIncomingBillsDetail(
    receivingId: string,
    incomingInspectionId: string,
    reqUser: IReqUser
  ) {
    const { tenantId, orgId } = reqUser;
    // 查询进场验收单的类型
    const materialIncomingInspection =
      await this.prisma.materialIncomingInspection.findUnique({
        select: {
          purchaseType: true,
          year: true,
          month: true,
          day: true,
          contractId: true
        },
        where: {
          id: incomingInspectionId,
          orgId,
          tenantId,
          isDeleted: false
        }
      });
    if (!materialIncomingInspection) {
      throw new BadRequestException('无此验收单');
    }
    // 进场日期
    const incomingDate =
      materialIncomingInspection.year +
      '-' +
      materialIncomingInspection.month +
      '-' +
      materialIncomingInspection.day;
    // 合同id
    let contractId = materialIncomingInspection.contractId;
    // 查询合同下是否有补充协议
    let contractChild;
    if (contractId) {
      contractChild = await this.prisma.materialContract.findFirst({
        select: {
          id: true
        },
        where: {
          createAt: {
            lte: new Date(incomingDate)
          },
          parentId: contractId,
          orgId,
          tenantId,
          isDeleted: false,
          submitStatus: SubmitStatus.SUBMITTED,
          auditStatus: AuditStatus.APPROVED
        },
        orderBy: {
          createAt: 'desc'
        }
      });
    }
    if (contractChild) {
      contractId = contractChild.id;
    }
    // 有合同的采购类型
    const purchaseTypeList = [
      'SELF_PURCHASE' as PurchaseType,
      'CENTRALIZED_PURCHASE' as PurchaseType,
      'PARTY_A_DIRECTED' as PurchaseType
    ];
    if (
      purchaseTypeList.includes(materialIncomingInspection.purchaseType) &&
      contractId
    ) {
      // 需要查询合同单价用于计算
      return await this.prisma.$queryRaw`
      select 
        miid.id
        ,mii.code
        , miid.incoming_inspection_id
        , miid.material_id
        , mdd.code as material_code
        , miid.material_name
        , miid.material_spec
        , miid.quality_standard
        , miid.unit as incoming_unit
				, ccmd.unit as contract_unit
        , miid.actual_quantity
        , ccmd.change_price_including_tax as price_including_tax
        , ccmd.change_price_excluding_tax as price_excluding_tax
        , mc.price_type
        , ccmd.remark
      from material_incoming_inspection_detail miid
      join material_incoming_inspection mii
      on mii.id = miid.incoming_inspection_id
        and mii.is_deleted = false
        and mii.org_id = miid.org_id
        and mii.tenant_id = miid.tenant_id
        and mii.is_deleted = false
      join material_dictionary_detail mdd
        on mdd.id = miid.material_id
        and mdd.is_deleted = false
        and mdd.tenant_id = miid.tenant_id
      join material_contract mc
      on mc.id = mii.contract_id
        and mc.is_deleted = false
        and mc.org_id = miid.org_id
        and mc.tenant_id = miid.tenant_id
      join contract_consume_material_details ccmd
      on ccmd.material_contract_id = ${contractId}
        and ccmd.material_dictionary_detail_id = miid.material_id
        and ccmd.org_id = miid.org_id
        and ccmd.tenant_id = miid.tenant_id
        and ccmd.is_deleted = false
      where miid.is_deleted = false
        and miid.org_id = ${orgId}
        and miid.tenant_id = ${tenantId}
        and miid.incoming_inspection_id = ${incomingInspectionId}
        and NOT EXISTS (
          select 
            1
          from material_receiving_incoming_detail mrid
          where mrid.is_deleted = false
            and mrid.org_id = ${orgId}
            and mrid.tenant_id = ${tenantId}
            and miid.id = mrid.incoming_inspection_detail_id
      )
      order by mdd.sort asc
    `;
    } else {
      // 甲供、调入无单价
      return await this.prisma.$queryRaw`
      select 
        miid.id
        ,mii.code
        , miid.incoming_inspection_id
        , miid.material_id
        , mdd.code as material_code
        , miid.material_name
        , miid.material_spec
        , miid.quality_standard
        , miid.unit as incoming_unit
				, null as contract_unit
        , miid.actual_quantity
        , null as price_including_tax
        , null as price_excluding_tax
        , null as price_type
        , miid.remark
      from material_incoming_inspection_detail miid
      join material_dictionary_detail mdd
        on mdd.id = miid.material_id
        and mdd.is_deleted = false
        and mdd.tenant_id = miid.tenant_id
      join material_incoming_inspection mii
      on mii.id = miid.incoming_inspection_id
        and mii.is_deleted = false
        and mii.org_id = miid.org_id
        and mii.tenant_id = miid.tenant_id
        and mii.is_deleted = false
      where miid.is_deleted = false
        and miid.org_id = ${orgId}
        and miid.tenant_id = ${tenantId}
        and miid.incoming_inspection_id = ${incomingInspectionId}
        and NOT EXISTS (
          select 
            1
          from material_receiving_incoming_detail mrid
          where mrid.is_deleted = false
            and mrid.org_id = ${orgId}
            and mrid.tenant_id = ${tenantId}
            and miid.id = mrid.incoming_inspection_detail_id
      )
      order by mdd.sort asc
    `;
    }
  }

  /**
   * 查询收料单
   * @param receivingId
   * @param reqUser
   */
  async getReceiving(receivingId: string, reqUser: IReqUser) {
    const receiving = await this.prisma.materialReceiving.findUnique({
      where: {
        id: receivingId,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      }
    });
    if (!receiving) {
      throw new BadRequestException('收料单不存在');
    }
    return receiving;
  }

  async add(
    receivingId: string,
    reqUser: IReqUser,
    data: MaterialReceivingDetailCreateDto
  ) {
    const { list } = data;
    // 查询收料单的单据类型
    const materialReceiving = await this.getReceiving(receivingId, reqUser);
    // 需要进行数据合并
    await this.mergeMaterialsAdd(
      receivingId,
      list,
      reqUser,
      materialReceiving.contractId as string,
      materialReceiving.purchaseType as PurchaseType
    );
    // 计算收料单的金额
    await this.calculateReceivingAmount(receivingId, reqUser);
    return true;
  }

  async mergeMaterialsAdd(
    receivingId: string,
    list: MaterialReceivingDetailCreateListDto[],
    reqUser: IReqUser,
    contractId: string,
    purchaseType: PurchaseType
  ) {
    const { orgId, tenantId, id: userId } = reqUser;
    // 有合同的采购类型
    const hasContractPurchaseType: PurchaseType[] = [
      PurchaseType.SELF_PURCHASE,
      PurchaseType.CENTRALIZED_PURCHASE,
      PurchaseType.PARTY_A_DIRECTED
    ];
    // 获取合同的发票类型
    let invoiceType: any = null;
    let mergeList: MaterialReceivingDetailCreateOrUpdateListDto[] = [];
    const isExit = hasContractPurchaseType.includes(purchaseType) && contractId;
    if (isExit) {
      // 有合同的
      invoiceType = await this.getInvoiceType(contractId, reqUser);
      // 同材料合并
      mergeList = await this.hasContractMergeMaterials(
        receivingId,
        list,
        reqUser,
        contractId
      );
    } else {
      // 无合同的
      mergeList = await this.notHasContractMergeMaterials(
        receivingId,
        list,
        reqUser
      );
    }
    const addList = mergeList.filter((item) => item.addOrUpdate === 'add');
    const updateList = mergeList.filter(
      (item) => item.addOrUpdate === 'update'
    );
    await this.prisma.$transaction(async (tx) => {
      await tx.materialReceivingDetail.createMany({
        data: addList.map((item) => {
          const {
            materialIncomingInspectionDetailId,
            materialReceivingDetailId,
            addOrUpdate,
            incomingUnit,
            contractUnit,
            ...reset
          } = item;
          return {
            ...reset,
            unit: isExit ? item.contractUnit : item.incomingUnit,
            taxIncludedAmount: isExit
              ? Decimal(item.priceIncludingTax || 0).times(
                  item.actualQuantity || 0
                )
              : null,
            taxExcludedAmount: isExit
              ? Decimal(item.priceExcludingTax || 0).times(
                  item.actualQuantity || 0
                )
              : null,
            receivingId,
            orgId,
            tenantId,
            createBy: userId,
            updateBy: userId
          };
        })
      });
      await tx.materialReceivingIncomingDetail.createMany({
        data: list.map((item) => {
          return {
            receivingId,
            materialIncomingInspectionDetailId:
              item.materialIncomingInspectionDetailId,
            materialReceivingDetailId: item.materialReceivingDetailId as string,
            tenantId,
            orgId,
            createBy: userId,
            updateBy: userId
          };
        })
      });
      for (const element of updateList) {
        await tx.materialReceivingDetail.update({
          where: {
            id: element.id,
            isDeleted: false
          },
          data: {
            actualQuantity: element.actualQuantity,
            priceExcludingTax: element.priceExcludingTax,
            priceIncludingTax: element.priceIncludingTax,
            taxIncludedAmount: isExit
              ? Decimal(element.priceIncludingTax || 0).times(
                  element.actualQuantity || 0
                )
              : null,
            taxExcludedAmount: isExit
              ? Decimal(element.priceExcludingTax || 0).times(
                  element.actualQuantity || 0
                )
              : null,
            orgId,
            tenantId,
            updateBy: userId
          }
        });
      }
      await tx.materialReceivingInventory.createMany({
        data: addList.map((item) => {
          return {
            materialId: item.materialId,
            materialName: item.materialName,
            materialSpec: item.materialSpec,
            price: invoiceType
              ? invoiceType === '增值税专用发票'
                ? Decimal(item.priceExcludingTax ?? 0)
                : Decimal(item.priceIncludingTax ?? 0)
              : null,
            unit: isExit ? item.contractUnit : item.incomingUnit,
            inventoryQuantity: Decimal(item.actualQuantity ?? 0),
            receivingQuantity: Decimal(item.actualQuantity ?? 0),
            requisitionQuantity: 0,
            receivingId,
            orgId,
            tenantId,
            createBy: userId,
            updateBy: userId
          };
        })
      });
      for (const element of updateList) {
        await tx.materialReceivingInventory.updateMany({
          where: {
            receivingId,
            materialId: element.materialId
          },
          data: {
            inventoryQuantity: Decimal(element.actualQuantity ?? 0),
            receivingQuantity: Decimal(element.actualQuantity ?? 0),
            orgId,
            tenantId,
            updateBy: userId
          }
        });
      }
    });
  }

  // 获取合同的发票类型
  async getInvoiceType(contractId: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    // 查询合同信息
    const contract = await this.prisma.materialContract.findUnique({
      where: {
        id: contractId,
        orgId,
        tenantId,
        isDeleted: false
      },
      select: {
        contractTemplateId: true
      }
    });
    // 获取发票类型的字段
    const field = await this.prisma.$queryRaw<any[]>`
      select mcfr.text_value as value from material_contract_field_rule mcfr
        join contract_template_field_rule ctfr
        on ctfr.id = mcfr.contract_template_field_rule_id
          and ctfr.contract_template_id = mcfr.contract_template_id
          and ctfr.is_deleted = false
        join field_rule fr
        on fr.code = '发票类型' and fr.id = ctfr.field_rule_id and fr.is_deleted = false
        where mcfr.contract_template_id = ${contract?.contractTemplateId}
          and mcfr.material_contract_id = ${contractId}
          and mcfr.org_id  = ${reqUser.orgId}
          and mcfr.tenant_id = ${reqUser.tenantId}
          and mcfr.is_deleted = false
    `;
    if (!field[0]?.value) {
      throw new BadRequestException('请先设置发票类型');
    }
    return field[0]?.value;
  }

  async notMergeMaterialsAdd(
    receivingId: string,
    list: MaterialReceivingDetailCreateListDto[],
    reqUser: IReqUser
  ) {
    const { orgId, tenantId, id: userId } = reqUser;
    await this.prisma.$transaction(async (tx) => {
      await tx.materialReceivingDetail.createMany({
        data: list.map((item) => {
          const {
            materialIncomingInspectionDetailId,
            materialReceivingDetailId,
            incomingUnit,
            contractUnit,
            ...reset
          } = item;
          return {
            ...reset,
            unit: item.incomingUnit,
            receivingId,
            orgId,
            tenantId,
            createBy: userId,
            updateBy: userId
          };
        })
      });
      await tx.materialReceivingIncomingDetail.createMany({
        data: list.map((item) => {
          return {
            // materialId: item.materialId,
            materialIncomingInspectionDetailId:
              item.materialIncomingInspectionDetailId,
            materialReceivingDetailId: item.materialReceivingDetailId as string,
            tenantId,
            orgId,
            createBy: userId,
            updateBy: userId
          };
        })
      });
    });
  }

  // 无合同的数据合并
  async notHasContractMergeMaterials(
    receivingId: string,
    list: MaterialReceivingDetailCreateListDto[],
    reqUser: IReqUser
  ): Promise<MaterialReceivingDetailCreateOrUpdateListDto[]> {
    // 同一材料名称加规格加计量单位进行数据合并
    const { orgId, tenantId } = reqUser;
    const mergedMap = new Map<
      string,
      MaterialReceivingDetailCreateOrUpdateListDto
    >();
    // 追溯数据的列表
    const tracingBackList: MaterialReceivingDetailCreateListDto[] = [];
    // 查询已有的数据列表
    const details = await this.prisma.materialReceivingDetail.findMany({
      where: {
        receivingId,
        orgId,
        tenantId,
        isDeleted: false
      },
      select: {
        id: true,
        receivingId: true,
        unit: true,
        materialName: true,
        materialSpec: true,
        priceType: true,
        actualQuantity: true,
        priceExcludingTax: true,
        priceIncludingTax: true
      }
    });
    for (const item of list) {
      const materialReceivingDetailId = uuid.v7();
      item.materialReceivingDetailId = materialReceivingDetailId;
      const key = `${item.materialName}${item.materialSpec}${item.incomingUnit}`;
      // 查询数据库是否存在
      const detail = details.find(
        (detail) =>
          detail.materialName === item.materialName &&
          detail.materialSpec === item.materialSpec &&
          detail.unit === item.incomingUnit &&
          detail.receivingId === receivingId
      );
      // 查询本次提交是否存在
      const existing = mergedMap.get(key);
      if (detail) {
        item.materialReceivingDetailId = detail.id;
        if (!existing) {
          mergedMap.set(key, {
            id: detail.id,
            ...item,
            actualQuantity: Prisma.Decimal(detail.actualQuantity || 0)
              .add(item.actualQuantity || 0)
              .toNumber(),
            addOrUpdate: 'update'
          });
        } else {
          existing.actualQuantity =
            (existing.actualQuantity || 0) + (item.actualQuantity || 0);
        }
      } else {
        if (existing) {
          item.materialReceivingDetailId = existing.id;
          // 累加实际数量（处理可能的null/undefined）
          existing.actualQuantity =
            (existing.actualQuantity || 0) + (item.actualQuantity || 0);
        } else {
          // 深度拷贝避免原始数据引用问题
          mergedMap.set(key, {
            ...item,
            id: materialReceivingDetailId,
            addOrUpdate: 'add'
          });
        }
      }
      tracingBackList.push(item);
    }

    return Array.from(mergedMap.values());
  }

  // 有合同数据合并
  async hasContractMergeMaterials(
    receivingId: string,
    list: MaterialReceivingDetailCreateListDto[],
    reqUser: IReqUser,
    contractId: string
  ): Promise<MaterialReceivingDetailCreateOrUpdateListDto[]> {
    // 同一材料名称加规格进行数据合并（单价类型为固定单价）
    const { orgId, tenantId } = reqUser;
    const mergedMap = new Map<
      string,
      MaterialReceivingDetailCreateOrUpdateListDto
    >();
    // 追溯数据的列表
    const tracingBackList: MaterialReceivingDetailCreateListDto[] = [];
    // 查询已有的数据列表
    const details = await this.prisma.materialReceivingDetail.findMany({
      where: {
        receivingId,
        orgId,
        tenantId,
        isDeleted: false
      },
      select: {
        id: true,
        receivingId: true,
        unit: true,
        materialName: true,
        materialSpec: true,
        priceType: true,
        actualQuantity: true,
        priceExcludingTax: true,
        priceIncludingTax: true
      }
    });
    list = await this.convertActualQuantity(list, reqUser, contractId);
    for (const item of list) {
      const materialReceivingDetailId = uuid.v7();
      item.materialReceivingDetailId = materialReceivingDetailId;
      let key = '';
      if (item.priceType === '固定单价') {
        key = `${item.materialName}${item.materialSpec}${item.contractUnit}${item.priceExcludingTax}`;
        // 查询数据库是否存在
        const detail = details.find(
          (detail) =>
            detail.materialName === item.materialName &&
            detail.materialSpec === item.materialSpec &&
            detail.unit === item.contractUnit &&
            detail.priceExcludingTax === item.priceExcludingTax &&
            detail.receivingId === receivingId
        );
        // 查询本次提交是否存在
        const existing = mergedMap.get(key);
        if (detail) {
          item.materialReceivingDetailId = detail.id;
          if (!existing) {
            mergedMap.set(key, {
              id: detail.id,
              ...item,
              actualQuantity: Prisma.Decimal(detail.actualQuantity || 0)
                .add(item.actualQuantity || 0)
                .toNumber(),
              addOrUpdate: 'update'
            });
          } else {
            existing.actualQuantity =
              (existing.actualQuantity || 0) + (item.actualQuantity || 0);
          }
        } else {
          if (existing) {
            item.materialReceivingDetailId = existing.id;
            // 累加实际数量（处理可能的null/undefined）
            existing.actualQuantity =
              (existing.actualQuantity || 0) + (item.actualQuantity || 0);
          } else {
            // 深度拷贝避免原始数据引用问题
            mergedMap.set(key, {
              ...item,
              id: materialReceivingDetailId,
              addOrUpdate: 'add'
            });
          }
        }
      } else {
        key = `${item.materialName}${item.materialSpec}${item.contractUnit}${item.priceExcludingTax}${item.materialIncomingInspectionDetailId}`;
        // 如果非固定单价，则直接添加
        mergedMap.set(key, {
          ...item,
          id: materialReceivingDetailId,
          addOrUpdate: 'add'
        });
      }
      tracingBackList.push(item);
    }

    return Array.from(mergedMap.values());
  }

  // 根据合同的单位和验收单的单位换算实收数量
  async convertActualQuantity(
    list: MaterialReceivingDetailCreateListDto[],
    reqUser: IReqUser,
    contractId: string
  ): Promise<MaterialReceivingDetailCreateListDto[]> {
    const { orgId, tenantId } = reqUser;
    const materialIds = list.map((item) => item.materialId);
    // 查询合同的单位换算加字典的单位换算
    const contractUnitList = await this.prisma.$queryRaw<any[]>`
      select unit, factor, material_detail_id from material_contract_unit_calculation
      where material_contract_id = ${contractId}
      ${
        materialIds.length
          ? Prisma.sql`and material_detail_id in (${Prisma.join(materialIds)})`
          : Prisma.empty
      }
      and is_deleted = false
      and tenant_id = ${tenantId}
      and org_id = ${orgId}

      union all

      select unit, factor, material_dictionary_detail_id as material_detail_id
      from material_dictionary_unit_calculation
      where is_deleted = false 
      ${
        materialIds.length
          ? Prisma.sql`and material_dictionary_detail_id in (${Prisma.join(materialIds)})`
          : Prisma.empty
      }
      and tenant_id = ${tenantId}
    `;
    return list.map((item) => {
      // 若验收单单位与合同单位不一致，则进行单位换算
      if (item.contractUnit !== item.incomingUnit) {
        // 查找该材料合同单位换算
        const obj = contractUnitList.find(
          (unit) =>
            unit.materialDetailId === item.materialId &&
            unit.unit === item.incomingUnit
        );
        item.actualQuantity = Prisma.Decimal(item.actualQuantity || 0)
          .div(obj?.factor || 1)
          .toNumber();
      }
      return item;
    });
  }

  // 编辑明细
  async update(
    id: string,
    reqUser: IReqUser,
    data: MaterialReceivingDetailUpdateDto
  ) {
    const { receivingId } = data;
    // 查询该明细
    const detail = await this.prisma.materialReceivingDetail.findUnique({
      where: {
        id,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      }
    });
    if (!detail) {
      throw new BadRequestException('该收料单明细不存在');
    }
    await this.prisma.$transaction(async (tx) => {
      await tx.materialReceivingDetail.update({
        where: {
          id,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          isDeleted: false
        },
        data: {
          ...data,
          updateBy: reqUser.id
        }
      });
      await tx.materialReceivingInventory.updateMany({
        where: {
          receivingId: data.receivingId,
          isDeleted: false,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          materialId: detail.materialId,
          materialName: detail.materialName,
          materialSpec: detail.materialSpec,
          unit: detail.unit
        },
        data: {
          price:
            detail.priceType === '固定单价'
              ? data.priceExcludingTax
              : data.priceIncludingTax
        }
      });
    });
    // 计算收料单的金额
    await this.calculateReceivingAmount(receivingId, reqUser);
    return true;
  }

  // 计算收料单的金额
  async calculateReceivingAmount(receivingId: string, reqUser: IReqUser) {
    // 查询单据下所有的明细
    const receivingAmount = await this.prisma.materialReceivingDetail.findMany({
      where: {
        receivingId,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      },
      select: {
        priceExcludingTax: true,
        priceIncludingTax: true,
        actualQuantity: true
      }
    });
    const taxExcludedAmount = receivingAmount.reduce(
      (acc: Decimal, cur) =>
        acc.add(
          Decimal(cur.priceExcludingTax || 0).times(cur.actualQuantity || 0)
        ),
      new Decimal(0)
    );
    const taxIncludedAmount = receivingAmount.reduce(
      (acc: Decimal, cur) =>
        acc.add(
          Decimal(cur.priceIncludingTax || 0).times(cur.actualQuantity || 0)
        ),
      new Decimal(0)
    );
    await this.prisma.materialReceiving.update({
      where: {
        id: receivingId,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      },
      data: {
        taxExcludedAmount,
        taxIncludedAmount,
        updateBy: reqUser.id
      }
    });
  }

  // 删除明细
  async delete(id: string, reqUser: IReqUser) {
    // 查询明细对应的单据id和材料id
    const data = await this.prisma.materialReceivingDetail.findFirst({
      where: {
        id,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      }
    });

    if (!data) {
      throw new BadRequestException('明细不存在');
    }

    const { receivingId, materialId } = data;
    await this.prisma.$transaction(async (tx) => {
      await tx.materialReceivingDetail.update({
        where: {
          id,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
      await tx.materialReceivingIncomingDetail.updateMany({
        where: {
          materialReceivingDetailId: id,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
      // 删除库存
      await tx.materialReceivingInventory.updateMany({
        where: {
          receivingId,
          materialId,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
    });
    // 计算收料单的金额
    await this.calculateReceivingAmount(receivingId, reqUser);
    return true;
  }

  async move(reqUser: IReqUser, fromId: string, toId: string) {
    await CommonRepositories.changeDataOrderNo(this.prisma, {
      tenantId: reqUser.tenantId,
      orgId: reqUser.orgId,
      fromId,
      toId,
      tableName: 'material_receiving_detail'
    });
    return true;
  }

  async getTraceabilityRecord(reqUser: IReqUser, id: string) {
    const { tenantId, orgId } = reqUser;
    return await this.prisma.$queryRaw<any[]>`
      select 
         mrid.id
        , mii.code
        , miid.site_entry_quantity
        , miid.actual_quantity
        , miid.unit 
      from material_receiving_incoming_detail mrid
      join material_incoming_inspection_detail miid
        on miid.id = mrid.incoming_inspection_detail_id
        and miid.is_deleted = false
        and miid.org_id = mrid.org_id
        and miid.tenant_id = mrid.tenant_id
      join material_incoming_inspection mii
        on mii.id = miid.incoming_inspection_id
        and mii.is_deleted = false
        and mii.org_id = miid.org_id
        and mii.tenant_id = miid.tenant_id
      where mrid.receiving_detail_id = ${id}
        and mrid.is_deleted = false
        and mrid.org_id = ${orgId}
        and mrid.tenant_id = ${tenantId}
    `;
  }

  async deleteTraceabilityRecord(reqUser: IReqUser, id: string) {
    const { tenantId, orgId } = reqUser;
    // 查询该数据对应的收料明细
    const data = await this.prisma.materialReceivingIncomingDetail.findFirst({
      select: {
        id: true,
        materialReceivingDetailId: true,
        materialIncomingInspectionDetailId: true,
        receivingId: true,
        materialReceiving: {
          select: {
            contractId: true
          }
        }
      },
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      }
    });
    if (!data) {
      throw new NotFoundException('该追溯记录不存在');
    }
    // 查询该数据对应的验收单明细
    let incomingInspectionDetail =
      await this.prisma.materialIncomingInspectionDetail.findFirst({
        select: {
          id: true,
          materialId: true,
          actualQuantity: true,
          materialName: true,
          materialSpec: true,
          unit: true
        },
        where: {
          id: data?.materialIncomingInspectionDetailId,
          orgId,
          tenantId,
          isDeleted: false
        }
      });

    // 查询该数据对应的收料单明细
    const materialReceivingDetail =
      await this.prisma.materialReceivingDetail.findUnique({
        select: {
          id: true,
          receivingId: true,
          materialId: true,
          actualQuantity: true,
          materialName: true,
          materialSpec: true,
          unit: true
        },
        where: {
          id: data?.materialReceivingDetailId,
          orgId,
          tenantId,
          isDeleted: false
        }
      });
    if (!materialReceivingDetail) {
      throw new NotFoundException('未找到该明细');
    }
    if (data.materialReceiving.contractId) {
      incomingInspectionDetail = await this.getCalculation(
        materialReceivingDetail.materialId,
        reqUser,
        incomingInspectionDetail,
        data.materialReceiving.contractId || ''
      );
    }
    if (
      materialReceivingDetail?.actualQuantity ===
      incomingInspectionDetail?.actualQuantity
    ) {
      await this.prisma.$transaction(async (tx) => {
        await tx.materialReceivingDetail.update({
          data: {
            isDeleted: true,
            updateBy: reqUser.id
          },
          where: {
            id: data?.materialReceivingDetailId,
            isDeleted: false,
            tenantId,
            orgId
          }
        });
        await tx.materialReceivingIncomingDetail.update({
          data: {
            isDeleted: true,
            updateBy: reqUser.id
          },
          where: {
            id,
            isDeleted: false,
            tenantId,
            orgId
          }
        });
      });
    }
    if (
      materialReceivingDetail?.actualQuantity !==
      incomingInspectionDetail?.actualQuantity
    ) {
      await this.prisma.$transaction(async (tx) => {
        // 修改明细表的数量
        await tx.materialReceivingDetail.update({
          data: {
            actualQuantity: Prisma.Decimal(
              materialReceivingDetail?.actualQuantity || 0
            )
              .sub(incomingInspectionDetail?.actualQuantity || 0)
              .toNumber(),
            updateBy: reqUser.id,
            taxExcludedAmount: null,
            taxIncludedAmount: null
          },
          where: {
            id: data?.materialReceivingDetailId,
            isDeleted: false,
            tenantId,
            orgId
          }
        });
        // 修改库存数量
        await tx.materialReceivingInventory.updateMany({
          data: {
            receivingQuantity: Prisma.Decimal(
              materialReceivingDetail?.actualQuantity || 0
            )
              .sub(incomingInspectionDetail?.actualQuantity || 0)
              .toNumber(),
            inventoryQuantity: Prisma.Decimal(
              materialReceivingDetail?.actualQuantity || 0
            )
              .sub(incomingInspectionDetail?.actualQuantity || 0)
              .toNumber(),
            updateBy: reqUser.id
          },
          where: {
            receivingId: materialReceivingDetail?.receivingId,
            materialId: materialReceivingDetail?.materialId,
            isDeleted: false,
            tenantId,
            orgId
          }
        });
        await tx.materialReceivingIncomingDetail.update({
          data: {
            isDeleted: true,
            updateBy: reqUser.id
          },
          where: {
            id,
            isDeleted: false,
            tenantId,
            orgId
          }
        });
      });
    }
    // 计算收料单的金额
    await this.calculateReceivingAmount(data.receivingId, reqUser);
    return true;
  }

  // 查询单位换算
  async getCalculation(
    materialId: string,
    reqUser: IReqUser,
    incomingInspectionDetail: any,
    contractId: string
  ) {
    const { orgId, tenantId } = reqUser;
    // 查询合同的单位换算加字典的单位换算
    const contractUnitList = await this.prisma.$queryRaw<any[]>`
      select unit, factor, material_detail_id from material_contract_unit_calculation
      where material_contract_id = ${contractId}
      and material_detail_id = ${materialId}
      and is_deleted = false
      and tenant_id = ${tenantId}
      and org_id = ${orgId}

      union all

      select unit, factor, material_dictionary_detail_id as material_detail_id
      from material_dictionary_unit_calculation
      where is_deleted = false
      and material_dictionary_detail_id = ${materialId}
      and tenant_id = ${tenantId}
    `;
    // 查找该材料合同单位换算
    const obj = contractUnitList.find(
      (unit) =>
        unit.materialDetailId === incomingInspectionDetail.materialId &&
        unit.unit === incomingInspectionDetail.unit
    );
    incomingInspectionDetail.actualQuantity = Prisma.Decimal(
      incomingInspectionDetail.actualQuantity || 0
    )
      .div(obj?.factor || 1)
      .toNumber();
    return incomingInspectionDetail;
  }
}
