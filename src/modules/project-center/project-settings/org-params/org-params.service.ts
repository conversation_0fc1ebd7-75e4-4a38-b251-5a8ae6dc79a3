import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import { HasBaseAccountResDto } from './org-params.dto';

@Injectable()
export class OrgParamsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 当前项目下是否有基础单据
   * @param reqUser
   * @returns
   */
  async hasBaseAccount(reqUser: IReqUser): Promise<HasBaseAccountResDto> {
    const { tenantId, orgId } = reqUser;
    // TODO: 基础单据包括结算单、收发耗用结存
    return {
      hasBaseAccount: false
    };
  }
}
