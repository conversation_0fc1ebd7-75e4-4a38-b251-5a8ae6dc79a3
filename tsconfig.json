{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": false, "resolveJsonModule": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "paths": {"@/common/*": ["src/common/*"], "@/config/*": ["src/config/*"], "@/modules/*": ["src/modules/*"], "@/prisma/*": ["prisma/*"], "@/public/*": ["src/public/*"]}, "incremental": true, "skipLibCheck": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitThis": true, "strictNullChecks": true, "strictBindCallApply": true, "strictFunctionTypes": true, "useUnknownInCatchVariables": true}}